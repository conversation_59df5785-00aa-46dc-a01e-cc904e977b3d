{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985cdf4fb1442186dccd9f77b73259a01e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "IOSSecuritySuite", "PRODUCT_NAME": "IOSSecuritySuite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e8866149bd3fdabe95ed42b1ca47e4ae", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9810bed04a15dcedac34f2ab3228dc9230", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite.modulemap", "PRODUCT_MODULE_NAME": "IOSSecuritySuite", "PRODUCT_NAME": "IOSSecuritySuite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fcff36923b6a7091bf89f05e0dc40473", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9810bed04a15dcedac34f2ab3228dc9230", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/IOSSecuritySuite/IOSSecuritySuite.modulemap", "PRODUCT_MODULE_NAME": "IOSSecuritySuite", "PRODUCT_NAME": "IOSSecuritySuite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98499c152b9cabca26869a061c7d76b59c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6dd8779450044f31f2f185467cda566", "guid": "bfdfe7dc352907fc980b868725387e98e25ac5f82388561f327db5e418d92064", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982fbf421fe41518899271b05bd966a851", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98777c3bc8f5ed9d41e3e010670f2181b5", "guid": "bfdfe7dc352907fc980b868725387e982a7f7fe28be80683616e69f6ab356e7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870bac3886c05a68629c8127b819d0cc0", "guid": "bfdfe7dc352907fc980b868725387e98b4fdd12b3edec693df73ac76a8d10ee2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c99d2be06901bb7693e421fbf19999f1", "guid": "bfdfe7dc352907fc980b868725387e98df5086b0d8555eacb1509acad2912253"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2de19d6be80e57084f74c627e88f240", "guid": "bfdfe7dc352907fc980b868725387e98247317deaee3fc483d9eebe40f7d286d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8f3007f77bba617abe097a6927d2746", "guid": "bfdfe7dc352907fc980b868725387e987a1b45252443ef9db28eefc281b55398"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1f13389303eee29121d785ab7ae4d95", "guid": "bfdfe7dc352907fc980b868725387e9800da0c1619de17eb3bd21d1429e905ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f2eccbb40161e2d137f842b73480875", "guid": "bfdfe7dc352907fc980b868725387e986615a667e79c21ca4d927ff5f25b6b89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d0fe3fd567f7a6f8c9fc2e18986aa23", "guid": "bfdfe7dc352907fc980b868725387e981f7f76e1db4f57844efce55a69cf2721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848167810ee4d6195dbaeb768d1210237", "guid": "bfdfe7dc352907fc980b868725387e986b9375078e001e867186a9323d83f610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98953e3b70374702a23770a1fdbc401479", "guid": "bfdfe7dc352907fc980b868725387e98c4703e2defb926b80c5795ea99f8363e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fd8c17c4aba545165374388130f028f", "guid": "bfdfe7dc352907fc980b868725387e988fa37a919b78ef2a53740b07beaffbe5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98830a5150511d93481ef080d0c7d59a43", "guid": "bfdfe7dc352907fc980b868725387e9845123f5dac46c651393dcc8c5963b94a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de8865be053e183ee99e636c7f6384af", "guid": "bfdfe7dc352907fc980b868725387e984bf96e189f257204eb5070b0cc2408fb"}], "guid": "bfdfe7dc352907fc980b868725387e98382faaadcd801eee361699db39a9d265", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98b201ca5506baa774551cc627fbbe5931"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d391d713992f1d4e99af11b30a3d8fcd", "guid": "bfdfe7dc352907fc980b868725387e98651ccdc06c6bbc04776c1e50fb14441c"}], "guid": "bfdfe7dc352907fc980b868725387e9877ab0b6e4cd0927bff06b5f1e45cbf23", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e982c512cdc400bc706f8ee7cd2c18242ca", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e980c47d9cc97d9fb26be834bb86d7b9565", "name": "IOSSecuritySuite", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e7249f0aa877cdaf149c69fba7e0f19c", "name": "IOSSecuritySuite.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}