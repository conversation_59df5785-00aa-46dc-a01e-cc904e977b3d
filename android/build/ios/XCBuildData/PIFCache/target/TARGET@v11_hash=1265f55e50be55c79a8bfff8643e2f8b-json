{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98323f549f18bf1111253617388e4b5ea7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6da72224a0002e2c8d4ad6a4140a101", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9812b97c8c86c119b65c79d0a3e31202f2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cab3693a69eb1c553c791758183685f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9812b97c8c86c119b65c79d0a3e31202f2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f480631d57f584ae82569893436b3d19", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9825e0ffbc9a21df54801ef74279d14b95", "guid": "bfdfe7dc352907fc980b868725387e98faa2d77c0ef7eb87f7f8421e51c1b40a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e3dac75ef5081293a3cce12f493338", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980064c03926ea51a192368ebd223aaaba", "guid": "bfdfe7dc352907fc980b868725387e98421ae3c3425f12162bb95608984e2dab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807705aebe299c63ce2e1e8e6d0942f9e", "guid": "bfdfe7dc352907fc980b868725387e98b1129dd415ba4f06f295293352794aa7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883bc00997f1de2aed937e35378e84732", "guid": "bfdfe7dc352907fc980b868725387e98c4d6312cb67f31d7f6f6831dd281e8b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984deb501a9b7243d524c407aa9f6f2709", "guid": "bfdfe7dc352907fc980b868725387e98f04a5c9a9cc81bfcfa6aaed0b00b6c3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c755872b0af2d116492eaa74f1a33ff", "guid": "bfdfe7dc352907fc980b868725387e9843b79fcddec6fa700bb0007202549f98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811936b5807f0f7d829c101dd8203a5d2", "guid": "bfdfe7dc352907fc980b868725387e98c352e5d7dbfbcf376cece1780e77fb1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857df5ea932ef4deb51ffb4c4707d0c64", "guid": "bfdfe7dc352907fc980b868725387e980e3bf0955a353ef83519ce8948a63fc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f5cc96770b885a755fdabd81db40266", "guid": "bfdfe7dc352907fc980b868725387e982fba501f5a0df4030b4a47f567f7966d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ad0af2e42bb4e8384deb50a7b3cbf43", "guid": "bfdfe7dc352907fc980b868725387e985f0b6bcef15399d61fa1794fdb818de4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989329554c091cda0d0a7a919558c061be", "guid": "bfdfe7dc352907fc980b868725387e98abea775179281a469ff2dd1142cb783c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989072b101272a134ef617c53a592b39e6", "guid": "bfdfe7dc352907fc980b868725387e988990c98018a3c452a437cf64f7a7b3fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee6f007f6f3820b5490250819e5a6580", "guid": "bfdfe7dc352907fc980b868725387e98b63102342437207e7dcb551a084a95bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8919381f10311a58bb89defe4dd9ef1", "guid": "bfdfe7dc352907fc980b868725387e98630bd2be35e33853378d0faf1591cb1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cf2160502537294b0f3670ce58f5f1d", "guid": "bfdfe7dc352907fc980b868725387e9864d155bcb5d32ec1f6dcd38e047c4c6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae86217897d5b9bee910a28e59524b28", "guid": "bfdfe7dc352907fc980b868725387e98a43972be6129f885eea405f17f739dcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834c1fb7049516b7da2ccbf05a39fee26", "guid": "bfdfe7dc352907fc980b868725387e98c448602647b7c43f19fc7f2b0a19882c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b710dd4602cb9b9bc325c99cd06ee1d", "guid": "bfdfe7dc352907fc980b868725387e98dfaef89466e79b28a06fbf0d65f3fbaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826e2d7a9fb6ac5b7ba84e82a5094882f", "guid": "bfdfe7dc352907fc980b868725387e9838606c9f15f987693f19420c77bf380a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b6255fe64b9e2a6724764ac52a41586", "guid": "bfdfe7dc352907fc980b868725387e988957ceb19237fd9d69b8a1d6b4cf5425"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c440de891b125e7b3f3683bde7a4ecc", "guid": "bfdfe7dc352907fc980b868725387e98e15821a1a930d75a5f3933f083e375fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802c30adc82d9706e0dd90d5db9e58e26", "guid": "bfdfe7dc352907fc980b868725387e9834028e19953e0b1ed245f3913ae35704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e7fa5567119a3e094231cde030360f2", "guid": "bfdfe7dc352907fc980b868725387e9834aef25fb16e03a76b8bbf16d245c607"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821572df553979126b5dd08dbbaf7fcdc", "guid": "bfdfe7dc352907fc980b868725387e985546f740b510dd60f5bc27de71daf43a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f94c01af108a2829677303dad84d4c9f", "guid": "bfdfe7dc352907fc980b868725387e9837f0f173397389d1816969e4e270046a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830157cc992289a77e7eb5985c7c35980", "guid": "bfdfe7dc352907fc980b868725387e98610dd3c4324e4d24978b8cd7e06bebc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcf0ad9a78d992f1e21c424dcb08c9bb", "guid": "bfdfe7dc352907fc980b868725387e9847c83ba16e50506728ebf97d34be80b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9b266a22a9ecbb663f2dc4f6f733797", "guid": "bfdfe7dc352907fc980b868725387e9894d77dc19091448c9de21434a0e30f0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863456232e00e9bd2c5c28f511b46e841", "guid": "bfdfe7dc352907fc980b868725387e983180877e88a88e10d1a87c3cbf76b203"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986188f57e20e4a5d343e2263600b407f0", "guid": "bfdfe7dc352907fc980b868725387e98a0247398fbdb89c3988b46155d18c067"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806f68ec655948f41fd9beb90474133f7", "guid": "bfdfe7dc352907fc980b868725387e989a4819ab88a56141908e0669a1910c8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883bdd4b80be1fba325a9df9a50cc9016", "guid": "bfdfe7dc352907fc980b868725387e982e29b240ad071385cb99642f4151739c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d160f8b42fbb6da642fb47796d00773e", "guid": "bfdfe7dc352907fc980b868725387e988c2c14f49e3b26c79b06e7b8b6101921"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d48f9dd78b23d8d273f8ce5000f59953", "guid": "bfdfe7dc352907fc980b868725387e98922e57193746f7f5fdd4494b90d735bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d9499ad09c7de7cdf37f8539093230f", "guid": "bfdfe7dc352907fc980b868725387e98ba3fb8295e51139e9cce240a1e715d37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98146510e0b20f78de140f787870d2c982", "guid": "bfdfe7dc352907fc980b868725387e9874987f8de6c5b4c86bf766cadd18dedb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d9701c1cd5a6de367255960e6d6499e", "guid": "bfdfe7dc352907fc980b868725387e9853de38235b70c71345bc88a3f70fb6ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983100b6becb73d4a820376fdf4157bc3d", "guid": "bfdfe7dc352907fc980b868725387e98576d77e91596b9da0d88acba8efaf0c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983528a9dd2188a1c8bb15a1863dcb44e4", "guid": "bfdfe7dc352907fc980b868725387e989f90dd5960fdec2d95b170b382f26e6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeb14211e22ef2f7fdaf914b046f15bd", "guid": "bfdfe7dc352907fc980b868725387e98d259083229b4eed28de4a364a94222ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862df27667264899a91d75fc9adfef1ed", "guid": "bfdfe7dc352907fc980b868725387e98f907c5a490d4761e6ce9b01cae1c968a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ba472b72b35013cfcbed7883ddeeee2", "guid": "bfdfe7dc352907fc980b868725387e980d3613196654dfcf6a896937bb373434"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989525753acefdf071c338776533d7f57c", "guid": "bfdfe7dc352907fc980b868725387e9818cf0c36a9406704d29f6d45ba79c104"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6f53b4d5a66337d60da3f855c546c88", "guid": "bfdfe7dc352907fc980b868725387e98d09bffc616c2cc7ff369d082d9774c93"}], "guid": "bfdfe7dc352907fc980b868725387e980055fbf3a11bc15129d28ebbe5cc1480", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e987ead9e15fc09a570554fa963eed1137d"}], "guid": "bfdfe7dc352907fc980b868725387e98da9cd099e7190758cfc42156c0f4e548", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8defae1b39d33d1c1b6a7ec3e8cf51c", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e987fd8694c1d36b88c99f78feb0d04dd25", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}