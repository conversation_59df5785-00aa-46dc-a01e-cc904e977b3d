{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9842463dbe505462c39bfc09a596294dc5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988ede09cd850a9320253e6928dd9ec9da", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9855ec57bde405ca4d3071a536b08a9fbb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9857211deb1a1df424179c9aca87ee983b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9855ec57bde405ca4d3071a536b08a9fbb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a3504f0b04802db5e8b2964ad2d77e95", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b60e9f9258b769162be7e851e240fc34", "guid": "bfdfe7dc352907fc980b868725387e981f4928332e7236ff661c32976d3ab96f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98736b2bb3b685ec8b0d6cb7fd45270eb1", "guid": "bfdfe7dc352907fc980b868725387e980bb56d46b2fc99034d0a572254fd7e0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98211f719cde0ec830b6c7fc33b3866351", "guid": "bfdfe7dc352907fc980b868725387e9851a8e1bcaa1373bec8a9e00548f43dca", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a5181763a38b143d6a4794dce4202a67", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9870b184b9cfa355c9ee6850dfece691ab", "guid": "bfdfe7dc352907fc980b868725387e9891c945dd777610d21670707798d0cde0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef06ab61016ba4b9f86695ca6a406f59", "guid": "bfdfe7dc352907fc980b868725387e989deb526f4e76702564d5ec552ef29530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981275a7ecda5917993c3e4ac0a900537a", "guid": "bfdfe7dc352907fc980b868725387e9883698c929b9752d5e7bc0d487046d87d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844cfe3af760bbabc6a9afd047aee2c22", "guid": "bfdfe7dc352907fc980b868725387e9800eeb0f66882cf27c0093a910fb6beb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897f06eec729d4ba7ab93f14c1f8d0f9a", "guid": "bfdfe7dc352907fc980b868725387e983b6ec48c0ce629dfa2c88f2a401c2e6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887cb3fc3014aa585c18efae210674cdd", "guid": "bfdfe7dc352907fc980b868725387e98755b42907a4b0c3e21a25418d43ee2a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b31a325d7ff5ff03674f383ff91bac9", "guid": "bfdfe7dc352907fc980b868725387e98299595ffe538999002ddb7c265bdd6b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982675de0427216fe383a742246c598647", "guid": "bfdfe7dc352907fc980b868725387e98927035148584f5ccdac040932f0cb1ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863054bfe170d3bafe3a16ba2da22a941", "guid": "bfdfe7dc352907fc980b868725387e983416b65f8f8547744837d254115a13a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812cb7a6a8a0a1c2af8fba13ead46cc8e", "guid": "bfdfe7dc352907fc980b868725387e986420abc3e78b61b896e66b6116c7c8e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98230beb9697ed987b78658033b2606b75", "guid": "bfdfe7dc352907fc980b868725387e98c676bed71e7855efd6dacdc72d3caabf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a62828545725f1d04e0db025eaa16bb", "guid": "bfdfe7dc352907fc980b868725387e9856ff6501d2de50c29c99828c40651cc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98556537dac17ae7552cca06f1f034df9b", "guid": "bfdfe7dc352907fc980b868725387e98499a06f1aea2e40a95cb9e8af886fd31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989221a656e428749d5726086a89ac1a95", "guid": "bfdfe7dc352907fc980b868725387e9884d118020aa8cca036b4052d52fa4f1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e0cde35d33bf3883db405eeaf5a79c7", "guid": "bfdfe7dc352907fc980b868725387e987a5ab075b10c34bf2caa6a02cf065c66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c26d3c589991d479665ae98f5cb17771", "guid": "bfdfe7dc352907fc980b868725387e98fd026486fd50ae5d07731d65c4187026"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c575f5195a85ba999503e46c80be921", "guid": "bfdfe7dc352907fc980b868725387e983dee119aa00a9a8e064f5fe4e088d599"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f05573afbd8e495290d29e3085aadd24", "guid": "bfdfe7dc352907fc980b868725387e988d43720a1f76fefc301ac85edc4908d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa724713a39319556e3a581fff7d5ad7", "guid": "bfdfe7dc352907fc980b868725387e9854a212117a36fe03aabada8ccd403cf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873b97318048a67f6468d7fe1192b6bf5", "guid": "bfdfe7dc352907fc980b868725387e98dfe70a8eb9bdd4813a299482469f85f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7e43350a0769f2a8981970ba81a19e6", "guid": "bfdfe7dc352907fc980b868725387e98843db9f98b73578283eaadea917899cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880699241d6d8bef4718ea67ccdd016ab", "guid": "bfdfe7dc352907fc980b868725387e9832349ca3dfa2e36ab8aa0ede221bb8d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833fd774371bcff29c8dc16f3531c7dda", "guid": "bfdfe7dc352907fc980b868725387e9803f95cb4d9e98197bd3a1757c0c97d39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a616cf2cc3487785e0131b0cbc157158", "guid": "bfdfe7dc352907fc980b868725387e98d616a92cd46f4ac253d0115e4cea248b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860026c0eb41264427c498c1253014567", "guid": "bfdfe7dc352907fc980b868725387e981c2c83833071e9c9f3b2c69cbada658f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d65bd2d42eb092bfe36400ca84b498b1", "guid": "bfdfe7dc352907fc980b868725387e98e232e2fd0676199c7c3cd852b6fadd11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cff4bbd8b64f119d60dc0925b859673", "guid": "bfdfe7dc352907fc980b868725387e982f47d82e08c8f1b5eb642fa27ca1d3ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988abc72fcbc4ebd58c108dbd87b8f23d0", "guid": "bfdfe7dc352907fc980b868725387e98174a15aa9241a73c1e092695e7fe30db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbb14609046fe56eea849dd94efe6025", "guid": "bfdfe7dc352907fc980b868725387e98767d75aa79157e1a52fe0b5bdf2ba38e"}], "guid": "bfdfe7dc352907fc980b868725387e982e5783770945fff7f34fc86ced754eb3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98b36913090927f44d13a72f9aee03008a"}], "guid": "bfdfe7dc352907fc980b868725387e9889a5fdf4e7b56e0e6f1373530a3d16b4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ba341c1750651ee2002f836eeec3d0eb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a41ba860aa6fc56673ac239987133d67", "name": "FirebaseSessions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}