{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fa6714139686b8cdfca1b1273af77114", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985fa9d377ddc3e585a2d4c5261f0ce481", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a88fe8a23e4fdde4105c4dd606d35ec7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cd03bae7081137a0627ac04fb2916628", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a88fe8a23e4fdde4105c4dd606d35ec7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PromisesSwift/PromisesSwift-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesSwift/PromisesSwift-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesSwift/PromisesSwift.modulemap", "PRODUCT_MODULE_NAME": "Promises", "PRODUCT_NAME": "Promises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.2", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988539f5924aad36970347fda921789a92", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982bf9125167e2ed314668593536fede6b", "guid": "bfdfe7dc352907fc980b868725387e983b2e5d840de4d8d3e7b2829689b8cc45", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9871d8aeaa7953abbd8d677a5c8e2548f1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98858c01ab30f71fcb6f7776f7b9801190", "guid": "bfdfe7dc352907fc980b868725387e98d1bcbb80a47720ea82c70777521515f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd6331281eb2593cd0f967927cf3b769", "guid": "bfdfe7dc352907fc980b868725387e984fabdbb8712c9017b130bef7718a1079"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bb464b5792fb9b2d8483dfabf87e971", "guid": "bfdfe7dc352907fc980b868725387e98ba84ce8b4990d3a65204778e71d28123"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd959c9139cdcdf576fd714c7c180be0", "guid": "bfdfe7dc352907fc980b868725387e9887686db9ee21203a402e757e7d58dcda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800083eedc6fdda2a282a98b5105cf4ea", "guid": "bfdfe7dc352907fc980b868725387e9889a79fcbe916564277cfe921d5388dea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98984da9d847961a8689c038f8bd98721b", "guid": "bfdfe7dc352907fc980b868725387e98cbe3874a26256d965d59917d2da0e26d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8b49df0776f28edb9df35244cc8eeee", "guid": "bfdfe7dc352907fc980b868725387e988d51320c72fbce1c371dfef297d721f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98962035726adf43a9f7fccac1131aa45d", "guid": "bfdfe7dc352907fc980b868725387e987bcd4357e65b7cb5c4a3432eea9b0583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984560d929996dd08a78a97fdcfd9b7eaa", "guid": "bfdfe7dc352907fc980b868725387e980ecbd5ec602a281acef8731d89572b51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981307b65035865d147daca4705906cd44", "guid": "bfdfe7dc352907fc980b868725387e98df457f576f4f0828992bd9cac9d65296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa512c0ab147276fea0b1e058961307a", "guid": "bfdfe7dc352907fc980b868725387e98dce269c87c03613208e1f577713e8bf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98379177711014cd5a28781f338fceb1dc", "guid": "bfdfe7dc352907fc980b868725387e983cfb3d850fe228d764bc3020cdb15a18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4e575fe463952e6cc061a637cc18ed2", "guid": "bfdfe7dc352907fc980b868725387e98cff994735246f6ad2785820998ad270c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e43cf46b505e6b4f1a8d46d122a7e617", "guid": "bfdfe7dc352907fc980b868725387e9820268669c9e5d455e96911010464cae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984da91cc344f2fa288b0d89d7713a8335", "guid": "bfdfe7dc352907fc980b868725387e986ec6d0036d24bd177d733cc3638a0578"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815260efcef2b9d5a56cdb3e0a5961716", "guid": "bfdfe7dc352907fc980b868725387e9841fb2b89769f996906cb9a61d097f716"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825d759e11a11f612b0da098f0c9f274b", "guid": "bfdfe7dc352907fc980b868725387e98f4b139761ca36afca2fc0e71b19ab678"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c35ff4a8b60f75806645436d94f14475", "guid": "bfdfe7dc352907fc980b868725387e9866b78f43f42206ab93c3173f5f25b6cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8a553611a200a38d06baaf49cfe49ef", "guid": "bfdfe7dc352907fc980b868725387e98c1b9543b341b31dadb534e5b77227785"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815e4725baf12f0b02fb620a9f22614c3", "guid": "bfdfe7dc352907fc980b868725387e98acf9893c2666475f7aef9fe2fcf8dc56"}], "guid": "bfdfe7dc352907fc980b868725387e980854e60f4ca1b8956f6929b25a9b3b55", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98a76a7d0d2d00d73e6504aed6fcf00417"}], "guid": "bfdfe7dc352907fc980b868725387e9856e4b5b2add75122aa51204019ef12e7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980b5e544744ebd3d12013c5c3dff86bf9", "targetReference": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3"}], "guid": "bfdfe7dc352907fc980b868725387e986c9cae77b2933199ad2012d3641825a4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e982423904c0fec8d69fb48f8811a58f1b3", "name": "PromisesSwift-Promises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982bfe7b75487d9ef7158f28fa2f89d57f", "name": "Promises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}