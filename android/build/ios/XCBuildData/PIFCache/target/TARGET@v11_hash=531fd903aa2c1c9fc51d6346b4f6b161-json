{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982c46536179edec6c9353cbc2f6f2ef08", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984e2803f3fe43b3bb95cca886f3f97a25", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9897a8927652f88d5f00464f5655e6207b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98376b685d30b84c2ce2dc209bcff7a1ff", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9897a8927652f88d5f00464f5655e6207b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f8534639f6898e62c1fb07ba6675a87b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c8c794857d1ef1d672471452ff887c74", "guid": "bfdfe7dc352907fc980b868725387e98469a1d9797ee4ea9dc51d70ffa18a747"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f997d94c6e28e2b55318bbc9de26ea60", "guid": "bfdfe7dc352907fc980b868725387e9859754693dbd16142478adeda9099d3ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888f9dcdc581a6b8b4bc922c6b096c576", "guid": "bfdfe7dc352907fc980b868725387e987313b1ac2badda810605c1f57eb2ba6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989261ae42a4f69978815d2951490cb444", "guid": "bfdfe7dc352907fc980b868725387e9835988b7d226cef9de18e72f5c8b32191"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836a48b8541c6bf14078d94d77e85f732", "guid": "bfdfe7dc352907fc980b868725387e98a55770a776fca7e050d7eabdc9e849d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7e97a3d6601edda1cdd0d5a87a941ca", "guid": "bfdfe7dc352907fc980b868725387e9898fbae552c737924e97e973c402cc611"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e665b049b80b3424234af1a90ff998ca", "guid": "bfdfe7dc352907fc980b868725387e98009e905f43ade17bc14ae36011c26bd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98570d78cdaacea91aa893c89430d8f180", "guid": "bfdfe7dc352907fc980b868725387e9839cdd183db40766573018e679877b937"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbdeb87caa695f24f5d73a7d8afb4efc", "guid": "bfdfe7dc352907fc980b868725387e9891b1de6196f59033f4b205fade362213", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7e7d378ae04511165a932ca3ebdd56e", "guid": "bfdfe7dc352907fc980b868725387e98dbb3b02ff1f6d91991a65c3484d166a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e1a788b3ceafaa8d96521d4c975cbc0", "guid": "bfdfe7dc352907fc980b868725387e98a75f66d34faf6a1a3f45d3e0af1c4000", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db16b93e9a41afc1b8694c9c8d5bf54b", "guid": "bfdfe7dc352907fc980b868725387e989a43bab201b9915c0e4b4dfe734e228b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982febb2daf33e1f053f04d58df5017850", "guid": "bfdfe7dc352907fc980b868725387e9852a20636de92ee51a1e940476deddc97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9a52e8c75c956cc3ce9d4969117d6ef", "guid": "bfdfe7dc352907fc980b868725387e98def98b9ec60da41fa4dd741b1bfad2db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875b571d88b8f479a166721bed8b454b2", "guid": "bfdfe7dc352907fc980b868725387e983a34b806a03e4bdc23ae4fc7358c37d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3dd76ed6fde8115a8a5d85230bceb32", "guid": "bfdfe7dc352907fc980b868725387e983bc7b31424ef8a654a06ec0c3b27a836"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fcf7ed7d3ab5b11ba6b2e0ff87d073f", "guid": "bfdfe7dc352907fc980b868725387e98d07506e799cf41b6aebfdda9f2c04537"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809bd087e95f909537809c9383317ce95", "guid": "bfdfe7dc352907fc980b868725387e98c784e81332f28c9e655c0476291197cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e6b8ab94012e49678ef3bbc03127229", "guid": "bfdfe7dc352907fc980b868725387e982bece6a328ff5a67b926f4ec99cde29c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec58f492a341217616e11f666cf38a7e", "guid": "bfdfe7dc352907fc980b868725387e989be43b2d6e24e5f18969f19c0e6776e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98581cb37004ad680671a82ed8a58ce87e", "guid": "bfdfe7dc352907fc980b868725387e98038ea4e2b09ecc6102ec11c852d7ba94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c355cf364c1ba4def5e89f3d9c26f6b", "guid": "bfdfe7dc352907fc980b868725387e986be062a04372761265f9d125654ee68f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b9e6ee54370393281fdeaef79784ba8", "guid": "bfdfe7dc352907fc980b868725387e98bf3c1be18991ead7c67ce1f850ce378b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98fe15eb4a720682a637161e6850b43d3f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98420a4ba3cc8565034f5a9c9378b171a7", "guid": "bfdfe7dc352907fc980b868725387e983cf66717d63768bf06499f43f48afc86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864bf2f3efe36d0d720f69eb961244e0a", "guid": "bfdfe7dc352907fc980b868725387e98dae217967074a4462c36d116ae818d49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98177b1a5074009d689e881961bddf72a5", "guid": "bfdfe7dc352907fc980b868725387e98661a981dc1aae6f1c4109a6940439f06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e42f59826a446012eff13a4c0762d485", "guid": "bfdfe7dc352907fc980b868725387e98de8c21be3fc5de37612f4b44bbd63d04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ac0b348c3a759fe75b3a144cb896b47", "guid": "bfdfe7dc352907fc980b868725387e98ab43b6518850bc8bf9bff0f803235a45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825d1ce14ba00b1ec15d158de761b2db0", "guid": "bfdfe7dc352907fc980b868725387e98fc765eb6cb909ab1cd2b46dc4ca9783a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ae48671c9384ef2eab1018e97488bd7", "guid": "bfdfe7dc352907fc980b868725387e989cbc096c1be1bd3e091befefefe2bad1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985907e3386e060576a925b95afebe0f84", "guid": "bfdfe7dc352907fc980b868725387e98669b39d287bafa2154a5b441e232e0b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824669dc4c3ccd8d83e3be84ce2306d91", "guid": "bfdfe7dc352907fc980b868725387e980a6221a2dc2320afe391938aa9529bd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876161027be52214993edfcb4ba66d871", "guid": "bfdfe7dc352907fc980b868725387e986d07a1bb07dac7a7e862ca16a47e47f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875fc13efaacbfcc48b4d174ff631daae", "guid": "bfdfe7dc352907fc980b868725387e9899f54fcdb1fddaa56377a3355c488bba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fb7f19f1aef3555edc1df459a134dda", "guid": "bfdfe7dc352907fc980b868725387e981eeb27717acb9334cf9ea349b68d2204"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98740a1b69fcb18b4a6116ff007abecc90", "guid": "bfdfe7dc352907fc980b868725387e982a5bd087b49cc52f8bc8b13f879ae95a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98643487c915c1b6fae72e445c11504de6", "guid": "bfdfe7dc352907fc980b868725387e9890a761cd6087d9138a11f03224ea6343"}], "guid": "bfdfe7dc352907fc980b868725387e983b5d39d9243a8a90147f44078ecdfbb5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e98cada5abd9d26764606c63006d00fc926"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d391d713992f1d4e99af11b30a3d8fcd", "guid": "bfdfe7dc352907fc980b868725387e9806a60d329051c1072d2ce9b96e80f0c3"}], "guid": "bfdfe7dc352907fc980b868725387e98b44cff98047b0621a562c9321385549f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9862b4a23dbb3fd3b5eff71b2f8fc76240", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e983e2b1a34b797411c435fabf4a2c3592a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}