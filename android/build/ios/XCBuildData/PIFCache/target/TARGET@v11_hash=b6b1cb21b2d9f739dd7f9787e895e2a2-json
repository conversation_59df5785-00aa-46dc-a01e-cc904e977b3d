{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98545bc8ced3f3b529ca64cf988b83f720", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a0c13e2c51b2ef0c59d297267dc8a482", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9837a81f52fa7ad191275d206c47b9f243", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cbe7b1239a861c9d616cf4b184de0831", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9837a81f52fa7ad191275d206c47b9f243", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98111a7198c3917ca8e259f666307f2107", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983adde780bdbd5ce4c7aea096a6013d69", "guid": "bfdfe7dc352907fc980b868725387e980c37c5c2ffd706e51216f744ac1aacc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851db1ab8a2c222376decc9f7cfe370e2", "guid": "bfdfe7dc352907fc980b868725387e98319740d0e1936beb547b4acb957d854c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aab186d5b2baf6e88e417672fc854963", "guid": "bfdfe7dc352907fc980b868725387e9881ae1abab51e1f7e45a90316af15dfc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1e11ba99189b0aeea7ff08b2beb39ad", "guid": "bfdfe7dc352907fc980b868725387e98d979d6f63bef6f909d947d5d342df53f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3b6d48a4d77b2523b8ba03010fb23c4", "guid": "bfdfe7dc352907fc980b868725387e98b076e28437cf33508e3c86f8a755279e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef64e16eb6624f7aa1ac459d83601600", "guid": "bfdfe7dc352907fc980b868725387e98741db92b6310429012041544000aaaa1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd1fa98aefc516d23f6d862ee7b5dab8", "guid": "bfdfe7dc352907fc980b868725387e98e504c5e107e020fde82209705c23a6bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b973eee1e4a65137c78a50e1084a301a", "guid": "bfdfe7dc352907fc980b868725387e98f556d9feac2c6c8a9756a82668945eda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982071d3d0b4f11ef5fb66b0a85e9a9bc5", "guid": "bfdfe7dc352907fc980b868725387e98aef508d26564c7d6da49dc663ce19d79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abbf68e7377128083431819d01d11d8b", "guid": "bfdfe7dc352907fc980b868725387e9841a63ec840a937518963adb0dd19c6ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e6a4be98ef030b1b73a9445bf702e9f", "guid": "bfdfe7dc352907fc980b868725387e98079cc77f555b4d1062bd78c0daace5bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd41b02fb0e17978d3a44b5560a6acd7", "guid": "bfdfe7dc352907fc980b868725387e9869e9cf12fc2332485c47ed2b013b31e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6b9001a41a5ba50fd5e66d8d3cc8f4d", "guid": "bfdfe7dc352907fc980b868725387e983c4bdc25a7c385d1b158b34f91596398"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988295090a412a6445e21a7d845e1458c8", "guid": "bfdfe7dc352907fc980b868725387e983c885b9fbf61eb748c688a8adfb14797"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982412cd50402714e6352641f6f1d41d7a", "guid": "bfdfe7dc352907fc980b868725387e98e803e440c695f4149f65e3df5c63af5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd4276146e8804c63eae56b26b466874", "guid": "bfdfe7dc352907fc980b868725387e9871b9c6905a8dc4be21090df2c48e5934"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e541894c52de75ebe7266b3e0682f09a", "guid": "bfdfe7dc352907fc980b868725387e98e142c7f8e35b4a53ddca3050786c827f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdcf6cb9f17ebd49cffe43e16f134e25", "guid": "bfdfe7dc352907fc980b868725387e98d29ae4de277bf9421c9ab7c0ef639bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c505f183c900c61ce37d7b16c6d32b3", "guid": "bfdfe7dc352907fc980b868725387e985408309a23160dc57a578f850e3e68dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802a2aaada95d908a255cf909cb2f867f", "guid": "bfdfe7dc352907fc980b868725387e989b26d9782b1c568af1638725369ef269"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0709a9b496149c5d077de8085312e57", "guid": "bfdfe7dc352907fc980b868725387e98f3a2079da241f38762ccbfa71999d279"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e80c73eafa45a0b90b5eb45fd247bd5", "guid": "bfdfe7dc352907fc980b868725387e986ceb324ae8bffd4e56c78bbeda7c0c27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800b1048f72c30e9d38ad48b4ecc8c3a0", "guid": "bfdfe7dc352907fc980b868725387e988cc7c9bb723e939085f2482f275eccf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b56f1800d6448ca2cd27289664c63737", "guid": "bfdfe7dc352907fc980b868725387e981f085ac9d18814612370c314d95ecb80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cdcdf31c478bfb1e69e8cfc77396ead", "guid": "bfdfe7dc352907fc980b868725387e9821a8688a13647d8b090efc455da4a672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808404f511e9f9b9333d7aa517445cb67", "guid": "bfdfe7dc352907fc980b868725387e988f3d9d0d658b4731b8e2c3d82bc2a6c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983702c70caf85e26a098749a7573184a3", "guid": "bfdfe7dc352907fc980b868725387e987fded4e99a440138ac03c12497fc8f36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98892760c6eefbdbd8afce62949cf18e51", "guid": "bfdfe7dc352907fc980b868725387e98522f5d29ef9a093c38b83551cdaaa9f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aad403a2e57ff774564db3ef553f5117", "guid": "bfdfe7dc352907fc980b868725387e98ce37d52475b21fd8e9222b5d9cfb5001"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837f2f0e8f68c369e6cfd2938a30cc094", "guid": "bfdfe7dc352907fc980b868725387e9874affe71c6b29a6a0aa466efb72f97fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984407e492ed4c40672f6b4c6debc2a948", "guid": "bfdfe7dc352907fc980b868725387e98809221e15524352b3273f920437255b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827323e4dd6a4787c28b7277dd097e77f", "guid": "bfdfe7dc352907fc980b868725387e98f16c6e0fd070d41265bc5e8a7614cbb4"}], "guid": "bfdfe7dc352907fc980b868725387e98ea4d62e65719d7f9d2f3796d8d326d02", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987d6f80db1819ba2cbfb3fb6cbcfe5902", "guid": "bfdfe7dc352907fc980b868725387e984aec80515b5ce12272e4cf6fbe74893a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881f0bdc826ccad102b60ef79042eef08", "guid": "bfdfe7dc352907fc980b868725387e989398e7e68773cc8e217d2c9eccb1f92b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ccd91b21501dce5e90e4557494a3a7f", "guid": "bfdfe7dc352907fc980b868725387e98cdb2ab8e08720e37cd6b61a685dd7c93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ad5748563687f0cca721ea30bf55569", "guid": "bfdfe7dc352907fc980b868725387e98778cac4d0e156f1be70177e47b5e3909"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d344a189976c819c7d6ba5124a2235dc", "guid": "bfdfe7dc352907fc980b868725387e9882eecb15cf106d89168a5a67c68d557b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eee202e15ed7678df40be8561518c391", "guid": "bfdfe7dc352907fc980b868725387e98b23f941f04312e31c04726758e632d09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6be4a2ff90c1f343b65ef6cc6b23e86", "guid": "bfdfe7dc352907fc980b868725387e9858865301786278f8336ac8983af22160"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989abbb824a25e35cdff3d879fe5079c15", "guid": "bfdfe7dc352907fc980b868725387e9895896638dd2b81c64dcf3d8ab6c48f9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876af7059d37e700edf6bae98aae27415", "guid": "bfdfe7dc352907fc980b868725387e98296f322c965f6513e76ee52b5e0d3a87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab3040a51a13ca44d16cd5a3de67a512", "guid": "bfdfe7dc352907fc980b868725387e98d66072efa45d77ddb2507296222e1b12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f44586e8b62ae07e82adb97dfe78540a", "guid": "bfdfe7dc352907fc980b868725387e98c51a337d19eb51ee05e176ae48fc692d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7f13c415034719692340a9d9d6c65be", "guid": "bfdfe7dc352907fc980b868725387e98e5a43b8f0dcd76e06b927b03433746f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98795b5a60d9c3bc34f8b2ae66992a9a3a", "guid": "bfdfe7dc352907fc980b868725387e98a85547c1331f0aa869e3a9b00b199cf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a14968dea5a20f9445cc0b304b5d550", "guid": "bfdfe7dc352907fc980b868725387e9833876022cc6c62caa7f3e73db94c2b59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a474a77b79c153a04de543ff2ba3aa1", "guid": "bfdfe7dc352907fc980b868725387e980ee2854f9a8ca5e74f1888148826543b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2ab73518fe7ebc30d1897630fe1a701", "guid": "bfdfe7dc352907fc980b868725387e980575cae9e2d32572dcef78fadc4c0597"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1502f8aab861a540eac621d5e8dd2d7", "guid": "bfdfe7dc352907fc980b868725387e98e7e806b00038ba2b45a5bec77c7eda2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834bfc333781cc9adf12ef2c8de21a642", "guid": "bfdfe7dc352907fc980b868725387e98f7a19f794752868d83fb8feb34206ed9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f57b160a1ede0637871c5d3f306e9405", "guid": "bfdfe7dc352907fc980b868725387e98d621b03f0f2bad674fbbd6c7eb0aa53a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7b6c05fe50ad5cb494f09ac4012bbd3", "guid": "bfdfe7dc352907fc980b868725387e9810e398b3fdaae672221dba764ce8e6bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b0f01b3b06a6f34836eb21c1a36599e", "guid": "bfdfe7dc352907fc980b868725387e987d8010b430c1960ed2071ae71c68436e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b62d56c9978aa16ad27dcdc6a49be08", "guid": "bfdfe7dc352907fc980b868725387e986d7fae4ee53bea3e30310f8272fb419d"}], "guid": "bfdfe7dc352907fc980b868725387e98150270c2a17d699fec5f03c40ea506e5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9848dccafdeff5c5cb32bfe42e99537449"}], "guid": "bfdfe7dc352907fc980b868725387e981895eb6b1c6e518381fd71984ad3b922", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984b7fdbe6b39aa32ed5f89365b104c8ab", "targetReference": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca"}], "guid": "bfdfe7dc352907fc980b868725387e9889022aa437b736a78fb35a8c63ce5a12", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca", "name": "FirebaseRemoteConfig-FirebaseRemoteConfig_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e984b1e8e5f67fa144e5e34058df6e2f50c", "name": "FirebaseRemoteConfigInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98928855ae8620d13300183deed96c33a1", "name": "FirebaseRemoteConfig", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980b80126605cba44506bfa90fbbd69742", "name": "FirebaseRemoteConfig.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}