{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9817e037390b81cab28d38419cc5014874", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98819652de8aff2a99e6b4201888847b57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825e24c0fa87ebd27da5fc4c5b01a2e89", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98368aba85652633de2c73d5aebc1cc474", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825e24c0fa87ebd27da5fc4c5b01a2e89", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ee650817e0c1db93666dfcff5242427a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982fedc1452d92dc5bfa1faf1fb174b508", "guid": "bfdfe7dc352907fc980b868725387e98f4239bd831ba5097714a97008aa1e5d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eef55abe37de2020f65ca0430d9904d0", "guid": "bfdfe7dc352907fc980b868725387e9847da9f7db2a77764bb89453ca3ec3c22", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7e5419503c3e43aa79d88df015e1c7f", "guid": "bfdfe7dc352907fc980b868725387e987aeea7a5827b7dd45116c64d8c6f1181"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984212224c33ada177225c894977235042", "guid": "bfdfe7dc352907fc980b868725387e98bc46007d8311897bc3c674493baf2394", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5c5a6af513092382517cedb8eb97ad0", "guid": "bfdfe7dc352907fc980b868725387e9877f21ebc9ee1346a415ddffa3966bb64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a05287a87cb6a00c263e2d24aa1c037", "guid": "bfdfe7dc352907fc980b868725387e980a98642d754a0966eab4634a273c15e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e68fd2fe7d1250b277c6f283f1b2d253", "guid": "bfdfe7dc352907fc980b868725387e98fae0a398fe48dd83154ab07e307678ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdcd406a9a7a3eb446448f741512bbeb", "guid": "bfdfe7dc352907fc980b868725387e9808adabedb4a67d41447d89d7290aaae7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbaeaf09742390125f0ead95c02a4ea6", "guid": "bfdfe7dc352907fc980b868725387e985e2bb8fad2715d4e3f2c84b6aa57eb2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9b1ab153a29c18ed7ef422fa8e1b699", "guid": "bfdfe7dc352907fc980b868725387e98f62a3219269aaef364671e5561f2c53f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2d96ef0db33d486b7dd0506d4cce0df", "guid": "bfdfe7dc352907fc980b868725387e98c04997a1fb918226aae9345c71eeee36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ca3becd009f890e3ef825db2c4a37fd", "guid": "bfdfe7dc352907fc980b868725387e98ea10a0d4481fb93b2bae2de9c92e3f93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877486c78b067e514a18a5673d651148c", "guid": "bfdfe7dc352907fc980b868725387e985b1cd3818de2fa38f758c8c14a44ac23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e978fd761a2bdd53bf454eccf5393357", "guid": "bfdfe7dc352907fc980b868725387e980ccde5dcac902025bf0e85b0f45ec531", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d730c84abb6a94b75e5857cbd168e93", "guid": "bfdfe7dc352907fc980b868725387e987d029e524e20ce955e81fd7849afa973"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98530c0d2a5c092626bae102108504fd5c", "guid": "bfdfe7dc352907fc980b868725387e98b8e08940070b8c78b4036f82516b0766", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd9b4ca8787d198fdfbc5677e46860e1", "guid": "bfdfe7dc352907fc980b868725387e989ffc45a4cf2e3951e467116e73236488", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfb65397b11043e800045f80841581f8", "guid": "bfdfe7dc352907fc980b868725387e9890e351094139cf61709616c3ccb6aeb5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4a4fcce66595e2f04e76713aebe868f", "guid": "bfdfe7dc352907fc980b868725387e9807ae5803b2cfcd8a2a41162f7f29d0b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2ce0f7d1e0365fc5b2657d8df70d1d4", "guid": "bfdfe7dc352907fc980b868725387e98c2c8f95a0535a4962d0455f2268ecd23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878987fc20dcdfca390a99be9d03ff242", "guid": "bfdfe7dc352907fc980b868725387e98532c60a6aa7d95db178e8715d9dbce3a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989503babc96c4d6f8a65d14b44baf4c8f", "guid": "bfdfe7dc352907fc980b868725387e9871ebdd2736b012c5835c03fc9b566304"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8987a4b7f2c5ca3130acdc74ad1c76b", "guid": "bfdfe7dc352907fc980b868725387e989b2a05c01a7ec4dd4e926803a2a1b592"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988beac7e5f808f7305e10582ed47388fd", "guid": "bfdfe7dc352907fc980b868725387e98bf00b612e24cdfecf9507ff12e792e7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a0c39726a80a4c2353220f7b2ba0dd1", "guid": "bfdfe7dc352907fc980b868725387e985fe300fb3273b2d28b4f4db900c5e351"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d078ff50a6ebfb506601681eda581695", "guid": "bfdfe7dc352907fc980b868725387e981664ca6efbc7044f7199bac8932ff1d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d556d19d2f81fc778b208e4f536c310", "guid": "bfdfe7dc352907fc980b868725387e980c71de224ace2d1417dbd42bf713bc94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981add152abe9d6773bf1f0c7212df3281", "guid": "bfdfe7dc352907fc980b868725387e98603dba4249bcbf8677e13b6cf57dd3c1"}], "guid": "bfdfe7dc352907fc980b868725387e9846308ebc2ffa2a2b84a2fec1465a2044", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98277d649a4a02b2a894bacd6c630c7291", "guid": "bfdfe7dc352907fc980b868725387e9886644b6a61c29345ae9824f96e01327b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afe847b2b82ab16c29f3e2e9f9a1dc0a", "guid": "bfdfe7dc352907fc980b868725387e98459a7c4896d1ad586853457a425a95c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed58eb266ae007cef0076bdec4f13bd0", "guid": "bfdfe7dc352907fc980b868725387e9833eceef723d5180fe613ede4b42a8567"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1291b8c4b751996d4ce39b042395982", "guid": "bfdfe7dc352907fc980b868725387e98cddf4fd33ce66d7941c148ce006b0fc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989127fc213011bc67c2da4f655fd5309d", "guid": "bfdfe7dc352907fc980b868725387e983cb54a7e01bea6f818f2c2ebeb5d3afe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8454342c785bf02329ce075a8f8fd2a", "guid": "bfdfe7dc352907fc980b868725387e98423e050f46fc64e84c787ea4add6f083"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e27e1b8d019c6fc09d33e1a7d9488413", "guid": "bfdfe7dc352907fc980b868725387e98d84f7407dc699e94cf4482991a017a78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989975365546a5291f26f91b73d28eab31", "guid": "bfdfe7dc352907fc980b868725387e98bc3513189509ac482020213415e5b300"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c7ac9c12f5630862a9d28bddf0d0c9c", "guid": "bfdfe7dc352907fc980b868725387e98ba911f20d70a7b6ac14f2f984d66492a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5621f1ebb29247f77035bd33a945c00", "guid": "bfdfe7dc352907fc980b868725387e98e5c746381012664becd527ed966f2d2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b26136d8295b929f4c88ab6cf3e162e4", "guid": "bfdfe7dc352907fc980b868725387e98af877b0314559e96164875f1306f07bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bd40b65dc3f36e19a23cf6a03367564", "guid": "bfdfe7dc352907fc980b868725387e980a132733d2af2b3df7943d3a0797073b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7e207d2834951581ab90a2800f0f2b0", "guid": "bfdfe7dc352907fc980b868725387e98c81cc4eae0adb7cec4f1a99a7971398e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98215fe20f3c0a0ef04888b24b8916235f", "guid": "bfdfe7dc352907fc980b868725387e9830ac6b429de201461c9d37695d8b1201"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98891087f0ad767e79529a211821ef5f9a", "guid": "bfdfe7dc352907fc980b868725387e983453a958410d54948a0647902839b18c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b500ab5854da9e3163902ded397afc", "guid": "bfdfe7dc352907fc980b868725387e98a328a9321caf1a5ceab45b9797e498db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986588aa1a6824031479d89e3c8c89d8a8", "guid": "bfdfe7dc352907fc980b868725387e98655a7bc74d7540270c82f135cf2aeaf5"}], "guid": "bfdfe7dc352907fc980b868725387e9872819eec90cfbff07c44f8e3317f32cf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e983c55b3eb4e9f572618917f225566a8c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "guid": "bfdfe7dc352907fc980b868725387e98421a87754f8aa37c2b5dd7584704d420"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f5979f0786f4a5ad9848f0349b1159", "guid": "bfdfe7dc352907fc980b868725387e982b52bdeb3819caa7477ca0d9713a9d60"}], "guid": "bfdfe7dc352907fc980b868725387e989925a0be6d5a3381c50594f33facce6e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987f29ed82074904a904ca0457ae4c16c2", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9889a95d644783d6df0e77db41645024a7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}