{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988db7ba5df968aadd963dc50c2618caf2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9835d822dc67b60b7d941bdc1e1ba90c37", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98552f6965b4d3abd0715c9322daaf3684", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981b51eb55820f934d584affa82226e4bf", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98552f6965b4d3abd0715c9322daaf3684", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9802d77f4ee103d21dd672a47d4d8b70d9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987a7445b79b154f36d6ff3bd9fd3cb804", "guid": "bfdfe7dc352907fc980b868725387e98c2227f038343042095e0d992e7e77f42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982288025603d8ac9fc2160897f1f717e2", "guid": "bfdfe7dc352907fc980b868725387e9888914a50aad124b0e3bb472bd2c72b92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98243497ce4cea0d223c77bb6df9437902", "guid": "bfdfe7dc352907fc980b868725387e98e5114ce14cefb513481bd5ae513537a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b180166d237f53256778ed0d7ebdf832", "guid": "bfdfe7dc352907fc980b868725387e98c807037643dc80ad909401a4a86d9322"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801f2875ed4129444a64a015d9b2874b4", "guid": "bfdfe7dc352907fc980b868725387e98bf2553654a631984fc484574663e636f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4d8955ef417a8581cc9feae421f5265", "guid": "bfdfe7dc352907fc980b868725387e98b181d3c70a4f6d3864cdff4779bd164d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ba6a8e74dddfbd079b43d9f7b19ccc8", "guid": "bfdfe7dc352907fc980b868725387e988898a7f0f6db8de2b8bb2b6626bb5c4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce475a3ca8babd507c644b7413470a1f", "guid": "bfdfe7dc352907fc980b868725387e980b291331ede2ad669cf04742acb182df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c16dbfa1e046d5d3e66f98c62dbde06", "guid": "bfdfe7dc352907fc980b868725387e98deaac9ab2fb0600687f63528cbb5bf7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba9579e38d39d8b91ecbdf0a7bcd1cef", "guid": "bfdfe7dc352907fc980b868725387e981601a22e3db4f486152fe2dc9bd0a2a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819afb6caa25f20214e186cbd310838a8", "guid": "bfdfe7dc352907fc980b868725387e98c38c56c5e56a16c2be519b4f81479f34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b68d067ebcb90127bf7ad88cae073fd", "guid": "bfdfe7dc352907fc980b868725387e98dd20d3b868ba7aab78a6eb7ecc061f27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef24a63c8ddebf7133984535490327cd", "guid": "bfdfe7dc352907fc980b868725387e9850d43e33966dc19ae02064b885ef5649", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de3d432273e2678f35ff5eebb94e9db0", "guid": "bfdfe7dc352907fc980b868725387e98774d5a4d329be9c3e77d2c01e6f40242"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1d30c41ccd1c348d10ccf4b09914705", "guid": "bfdfe7dc352907fc980b868725387e9825b8b1fa61d7d620f3c4760e75b9c09a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98897d592c9701195d1dc8bfeb80055f1d", "guid": "bfdfe7dc352907fc980b868725387e98898ebedf10f94b91ceed37749d1efade", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866dacc0c4eba3869c3901f64a130da6c", "guid": "bfdfe7dc352907fc980b868725387e988713c9c6534a8d94c0a82b1a727401d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c978145ff5e043da462f1fc0cf4534c9", "guid": "bfdfe7dc352907fc980b868725387e989a141a10bfa5d7630bd20e9ed496f90e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840d8f9622bd0d4696b6c385dcd489eba", "guid": "bfdfe7dc352907fc980b868725387e98756ec3b21d60cf82aa8aec6dcd5e8675"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98243d732f1ac2d2e99fe92377453727c0", "guid": "bfdfe7dc352907fc980b868725387e9868157db4a6a651c0a9030bee84e8ca61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813fd9c79766396935734ebd557db6fbc", "guid": "bfdfe7dc352907fc980b868725387e98e9ad2926a9165d4864349cec84c7a1c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d85c58b19970a4323a57acb2914363b7", "guid": "bfdfe7dc352907fc980b868725387e985975758d2d3a488162dc7114b63df6de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c65bca1e71c28d7ea71a845d201de2c", "guid": "bfdfe7dc352907fc980b868725387e98d70bdae615f5fb94999aac128bcec916"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cccd1e0cb5c667200f4e901517a3cc00", "guid": "bfdfe7dc352907fc980b868725387e981ad903f540917903444af677771ffe76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878fdde36d0f9db4518534d419d3a17cb", "guid": "bfdfe7dc352907fc980b868725387e984286d0728718aed6d81e372968008c8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828ed04240b98d07d0ea6188d92788dfe", "guid": "bfdfe7dc352907fc980b868725387e98c62bb1d8cc04ffd84ce8149faf29da91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a4f942c812c651baa1ee441fc650026", "guid": "bfdfe7dc352907fc980b868725387e98a1ef33ce16aef75f21b122ff94757781"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98045b10f792aebee894800cf997c1fb15", "guid": "bfdfe7dc352907fc980b868725387e98130d6576704f39dd7ef8760956805d02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980825f46beb8f92fba4ac5c61a25aa44a", "guid": "bfdfe7dc352907fc980b868725387e988497c65ac8517a18c5af28b0fb5315de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da1ee23428fb4a59e9fc9c77a9ec62cd", "guid": "bfdfe7dc352907fc980b868725387e98819dba0aeb7aa4168ab00094400d6694"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b5c750f2f834f9a6048abfc3420319", "guid": "bfdfe7dc352907fc980b868725387e98266275d350c3ccce72e9ee61071fc7e7"}], "guid": "bfdfe7dc352907fc980b868725387e9836ad02ab9f6af899fcb8846022988b7a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d21d6883bf5effee67025fc5c3943aa4", "guid": "bfdfe7dc352907fc980b868725387e985825f00e2554b83ca026d18efa5e23d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b27664425251e8cc1f343cbbf28a376", "guid": "bfdfe7dc352907fc980b868725387e98db9f86ab02686c6de34deeefc7f81121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98544f38cf9fa7d344b557fa187b03ea66", "guid": "bfdfe7dc352907fc980b868725387e98f1a09f2a51ec7ee8ab9051be4d0d0fee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98909827dd04f66f02d3776d030e4fb22c", "guid": "bfdfe7dc352907fc980b868725387e98f5b1c16f498d5c14154e5a4121dbaaf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f36be956dae0a2cb181bdccb9df1f375", "guid": "bfdfe7dc352907fc980b868725387e9823f364e0342786a84798c73ce6a682f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfb11333560d60378dee61c139cc73e9", "guid": "bfdfe7dc352907fc980b868725387e98ef4f534a690ab262c774d0cbfd9e9cd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985be740084209e650e57c408f9e491798", "guid": "bfdfe7dc352907fc980b868725387e9894ec25fa277b8195c6396931dbb92230"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f1d73a9da85a972baf9d3e0e6f13182", "guid": "bfdfe7dc352907fc980b868725387e982e2cbe30d7c36daf030ac3988a188b3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8eeb01e33cd5bd2f2729195d20f2cb", "guid": "bfdfe7dc352907fc980b868725387e989194b6da29122219baa0f5ff3271751d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986aab681a627f1cb1dca59b4a67b6ed04", "guid": "bfdfe7dc352907fc980b868725387e98ca3ca2ca3761da8c04b6cf71355d8d14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fccc64957d3ba066c42e83ae5069ff75", "guid": "bfdfe7dc352907fc980b868725387e981178105df7e29ac5bdbaf7a6832f6974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98247e981792aa211435f03bd1cda1af4a", "guid": "bfdfe7dc352907fc980b868725387e986afff05f6efc11603ad258422314d466"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c4b9fcd55088806c4faeee781173b24", "guid": "bfdfe7dc352907fc980b868725387e9891787f3183bad0aaeb68455ecba2e261"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bd93e7707bb98d7d6e2bb5a81570ed7", "guid": "bfdfe7dc352907fc980b868725387e98dde3c4b0df3ee0586c913145e3726b5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f18db33b98a36b3df3921a9d2f29cb6", "guid": "bfdfe7dc352907fc980b868725387e98c965459eea867d51668656730411fc58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be15d07dfee5e0fc9e026b9bfd31e01d", "guid": "bfdfe7dc352907fc980b868725387e989fecfe9d5886b1687ffc9878d2d6c42f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889ef34bb9e7fb79b6d8963f6df995ad4", "guid": "bfdfe7dc352907fc980b868725387e98178f6714888edd9ee15db1128c36f1b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b50dbda44902727e5b55b41c289a987c", "guid": "bfdfe7dc352907fc980b868725387e98120e3e9046c3b8bf03687ee66bd98ffe"}], "guid": "bfdfe7dc352907fc980b868725387e98bc27e5ad89239a889bcdf7aaefca963c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e980eff77776b640dbbee7a38dc74b31b31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "guid": "bfdfe7dc352907fc980b868725387e984ede499362dd3b2391310f86037ac1c6"}], "guid": "bfdfe7dc352907fc980b868725387e980295e049863b2f22b3a4358a15c9816d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9838e74678c1611da3cfaa61943f235ee5", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e988eb5a76d89c1ed91c1e42a23c8eb6303", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}