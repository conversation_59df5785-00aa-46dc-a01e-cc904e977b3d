{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98daf8f53d2c1a7645ea8d5096cb1d31d1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988dc0f11e35154cbf08defa2226fed38d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ec57552e8e81a38b56c0ef82a807cf7e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ae09df52386682a57a05c3e27096638a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ec57552e8e81a38b56c0ef82a807cf7e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98778748cbfc241184e46a6d9e7adfc560", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9876a09d7daa6dab54dca6af6f6b535a06", "guid": "bfdfe7dc352907fc980b868725387e98f7d05440ae73c717f6bb7d20aba3b0cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac4846f9e37431bfc8641969e32dd91d", "guid": "bfdfe7dc352907fc980b868725387e98d0f97aad1d0fafde17a35cb47e80e967"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c307b19a4613a0924084e1e995c345d", "guid": "bfdfe7dc352907fc980b868725387e98add1757b46010ed6f72adf55b9be56f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843ed14d7f3bd623234bb954d7c43683e", "guid": "bfdfe7dc352907fc980b868725387e986471f1ba1cd8e262763bce886899020c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb42e1ffafd4bffff69e4c7064efeff1", "guid": "bfdfe7dc352907fc980b868725387e98e8533615fe428f34f51f53e9d9624141"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aece700dcac0432491d3eb7a7276252", "guid": "bfdfe7dc352907fc980b868725387e9858ed40809c20dad2b4597432bfb93ea3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f57eefa89d1e7d4f4318f61c3362e7be", "guid": "bfdfe7dc352907fc980b868725387e98f0a06cba01721d18819b5247f0503bdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cefaeca957f73b6654e305b0071285aa", "guid": "bfdfe7dc352907fc980b868725387e98f60bb32b3ea77ceba414f93c9beb2c63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daf715d3fee3ae434a415462c673c9df", "guid": "bfdfe7dc352907fc980b868725387e980f890c50f9015be31950d6ad4624f5fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfcfe52b0d612fd64cdbbaf615c160ab", "guid": "bfdfe7dc352907fc980b868725387e98403213129132a4a6dcdeaf1e3acfe6ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982217fe37376ac664ac9b14b5f6d5b489", "guid": "bfdfe7dc352907fc980b868725387e986ca8d60455b4125dbb01f83b5429fa7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c002eee6d6fdcd16512a3fa37c19fe87", "guid": "bfdfe7dc352907fc980b868725387e985d718047a0fcfbeb5a58807956b0beff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983215689ed469a02c0f9eaeb42a3ea191", "guid": "bfdfe7dc352907fc980b868725387e983c760679e7e9e6021482f3f176bd6a3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6320b96a4480c757f52823644eb1bf3", "guid": "bfdfe7dc352907fc980b868725387e98f390d5d981e74481009cd9d6dab4b94a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a78a865a9b5a282e5ef368c3523d4e8", "guid": "bfdfe7dc352907fc980b868725387e98ca0fc8728251f77464bc426cf3e3cf53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1ee6e1c860984d5cf4c834f850398c4", "guid": "bfdfe7dc352907fc980b868725387e9878cad875bfbd5523038c77b7e64d45be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d7445442b752dd8b8f3a37768d27bc7", "guid": "bfdfe7dc352907fc980b868725387e987b0c0b31d7469af67d7a78d84c07e4b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836e9a46bcede022792e80b70d501199b", "guid": "bfdfe7dc352907fc980b868725387e9869e12c461720a31244a4a3c626214d2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f493c566af7162be3dc9fc898162482", "guid": "bfdfe7dc352907fc980b868725387e9824d6681a870843e45bb4d601f75955b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831d3f7d0e443088b888281f6fb701501", "guid": "bfdfe7dc352907fc980b868725387e989e9dbcc9c0381d873c8f242d0cd3fe4e"}], "guid": "bfdfe7dc352907fc980b868725387e9839221de8ae2d5558972c136dab6045f4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985ae15305d89ae742103a636059f5485f", "guid": "bfdfe7dc352907fc980b868725387e9806270846f40d3f3d4371e19f603f22ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809b10de9d9b8fd7c4c1e509beb10ea45", "guid": "bfdfe7dc352907fc980b868725387e987beb2fbcaa994b2c1dbe80c53ae0aebf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897bedf71f8c7501f02068a8175052f4a", "guid": "bfdfe7dc352907fc980b868725387e9893309d19bc629b8e58fb9fa1f6f94626"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d636d12a1e34ffef50457c4ee3a6715e", "guid": "bfdfe7dc352907fc980b868725387e98a0f8514ef354267284fd7d1c1cb8bb0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baa741dd7c96e212209c421796922a92", "guid": "bfdfe7dc352907fc980b868725387e9820f4251b02e3e11ba095cafb2e6e210f"}], "guid": "bfdfe7dc352907fc980b868725387e98a01de02311d8f382eeac3dbae69658e9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9877840ecf4fe91d1d077746e2a95b8f96"}], "guid": "bfdfe7dc352907fc980b868725387e98c6d823ef7beb601d8920dacf498dcd90", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98924bda7857be92994dbd7478c59e8178", "targetReference": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443"}], "guid": "bfdfe7dc352907fc980b868725387e98e1c91c0bc3de488e2d0d8bf868bbfb23", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443", "name": "FirebaseABTesting-FirebaseABTesting_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}], "guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98388ecc0b6beee3823c42c78ba6025714", "name": "FirebaseABTesting.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}