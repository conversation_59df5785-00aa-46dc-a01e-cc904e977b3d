{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98382406d3c4c06275e1846a71b17c69fe", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98964fcd42e02234e3086e84647ffc4907", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fb3089e3353ddef1f5a42ed007f6814e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98852e8d94af16d3a5131d0713457a8f96", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fb3089e3353ddef1f5a42ed007f6814e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_CAMERA=1 PERMISSION_LOCATION=1 PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d1939dc62c703c5be5e9a33839bfa6e0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9858c1ac6fa3ff351cfc42f9256ef9ad80", "guid": "bfdfe7dc352907fc980b868725387e980f2e1839d162aa3c1b52b301e2b0f582", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fdeabc853a0bbd77749b7e4f1993539", "guid": "bfdfe7dc352907fc980b868725387e981c54f358d6a0d0e0326939c45788141e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d8e4ddcd6767f60b07f010a7359d66f", "guid": "bfdfe7dc352907fc980b868725387e98a5ff3cd1e99a95e4eb0d4a243f3c218c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b5a26de4e67fcc839144c0dc8c47b9a", "guid": "bfdfe7dc352907fc980b868725387e98e453a4a07708be339f13fba8813005f0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c7f80a311b43a16787acd5fab2939a6", "guid": "bfdfe7dc352907fc980b868725387e983ecf5755fc9daabcc2f5d64d500dc5cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861d951d6e716031dec3a5d92337e7c47", "guid": "bfdfe7dc352907fc980b868725387e984c3fa34c992547ed274b0557558dce47", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876de6b47029ab16c60a4b70bf0f16f12", "guid": "bfdfe7dc352907fc980b868725387e98ac373ea828282cebb806467949558b55", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d36ae3bd93ba038d74cd84ab5c7835c", "guid": "bfdfe7dc352907fc980b868725387e987be8c6923cac2ad5bdccf2c8250a65b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1ff6fc63d390969d9976d12a77b6e74", "guid": "bfdfe7dc352907fc980b868725387e9868baa2d96c1631c4b2bcd247a039d340", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98346982e23ce9f6d705f3ddb78f7ef5b7", "guid": "bfdfe7dc352907fc980b868725387e988356f38a1439dbd92cb0716664f08be1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b9293dd052583ff4bf9e55b903f3a74", "guid": "bfdfe7dc352907fc980b868725387e98e01f3c305ad1ab45c77d0ab6d419dc0d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a32550a458bdb951bc4c6f0a72fc818", "guid": "bfdfe7dc352907fc980b868725387e9843eb190ba291fb91c03e1feabdaef212", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d8808c0cc730931aed7850570c7575", "guid": "bfdfe7dc352907fc980b868725387e98a3788e36b04ca0f0b4cc347feee5f491", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847f9f5c9172d6792379f0e31987d9cd8", "guid": "bfdfe7dc352907fc980b868725387e98c80d35d93e4305551bfd5584d9639c62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b15bd284f294a5b25008b6c504e4151", "guid": "bfdfe7dc352907fc980b868725387e9893da58022b12ccc1000fbcb6f6e6980b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d494331799482a68a8461c446d46f8f6", "guid": "bfdfe7dc352907fc980b868725387e98b36f95bb9e70ccbba6916abc72efbc11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddbc082351f03449ab2ebbbb6fa8d6a4", "guid": "bfdfe7dc352907fc980b868725387e9885931f1d03ed3b0affdfbca2e1d300d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0f23549e5b1cabd3ab9d430bd8c5a8c", "guid": "bfdfe7dc352907fc980b868725387e988fea68bec4ed46cb8c3001954944d08e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcfb2205b89d5d336d514bb8174007f5", "guid": "bfdfe7dc352907fc980b868725387e987d811be17f147bafb7e86f39a0cda1b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984139bcb4c86e008b94aa3169354a1934", "guid": "bfdfe7dc352907fc980b868725387e9886f0c9c7385427bec08fcabb61bab7aa", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868872e72852df5cf0f36df9f3f46b8ce", "guid": "bfdfe7dc352907fc980b868725387e980f05af759c420590a215107da40ef88f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f343e8c39fa4f9560ac317e868517ff8", "guid": "bfdfe7dc352907fc980b868725387e9824be1b75d5f4ff606c1b0bf4b76420f1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9822c413065533a0407976d8be364c6fb6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bd7be5a07898fb5cd8b834158ee5c181", "guid": "bfdfe7dc352907fc980b868725387e9882f8b7b2b715dae534f54309ebf381e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982197a59fd5886560b23332078175855b", "guid": "bfdfe7dc352907fc980b868725387e98411cf4d4cbb413d8bf24ef0c3973bfbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff4fed8cc11c22462f34ef0b0d0efc10", "guid": "bfdfe7dc352907fc980b868725387e98392d2b64d2efb0d128bc6f03457cf42e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a42bfd2837731afb0cadfc174e131bb5", "guid": "bfdfe7dc352907fc980b868725387e9897fdf3f428177cefc89f1580206867fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f38b0139f6bab83e2355b15070175396", "guid": "bfdfe7dc352907fc980b868725387e98e740e8e5f5c0d051e8a5a78851a37383"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98747d3caa5e6a3a89ede93b294b6b4d91", "guid": "bfdfe7dc352907fc980b868725387e98384eb4ea3a0fc46825990739c01eda78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98255c8a59ac89f954a4874ace3d0002ec", "guid": "bfdfe7dc352907fc980b868725387e9864dc910e84fc28ad24ca6b793c990b66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ceb5302a9ed9b840b386e9662c877c0", "guid": "bfdfe7dc352907fc980b868725387e98fc5c9e754df7a342896269f75ecf3d89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e82b8523739a9b0210e91447bb407ac", "guid": "bfdfe7dc352907fc980b868725387e98af0b298776bcb7f43c6ed0abcb11fed6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f60e2740487a749fc3cf321026fcc8c", "guid": "bfdfe7dc352907fc980b868725387e985bd57e57dfafc62917f01a4f7d0b0aec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2c39292e171fd3c95408892cc88aeee", "guid": "bfdfe7dc352907fc980b868725387e98a5d4a4c324eba0c3a066cdeca68f5e29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8838cbdfe71679cb42700e9b90e14fa", "guid": "bfdfe7dc352907fc980b868725387e9844c84d9de954144e56791907cbe8fb08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982db9b34439f877bce095acdaa40f37cd", "guid": "bfdfe7dc352907fc980b868725387e98d24c7e0003852a6eccf731ebfb5fb8ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888113c848910998af7b1b2c87a085774", "guid": "bfdfe7dc352907fc980b868725387e981cff9b3f941b077ffec92984d843fdba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840e620bca6ee65204fd7cbaa5270b409", "guid": "bfdfe7dc352907fc980b868725387e985e8133a40c4190a4712f50320ec4a340"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841e733f4afbd1be9c6426fd1e17b7579", "guid": "bfdfe7dc352907fc980b868725387e988499797ebb964b9b8916703ce2486c08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840f4313805e6d7e9b2e4c03c8d662343", "guid": "bfdfe7dc352907fc980b868725387e98314bb1f114c8f4ef60bd298cbc48a458"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98370714f233bd6018f510c3a8ab77e769", "guid": "bfdfe7dc352907fc980b868725387e98b28ba5eee2b59adfd8f1625a8720eae4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987393e781498f61691eabf3b588110f9f", "guid": "bfdfe7dc352907fc980b868725387e98ee2424fc0ed337ae0633f83dc9a6b59b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b21635e9534d42741f36e550c1f98d6", "guid": "bfdfe7dc352907fc980b868725387e98539b8c11d4d54500f0ebbda7ccf98a0a"}], "guid": "bfdfe7dc352907fc980b868725387e98b5a236b274306f2a4374b0cced2f7fef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "guid": "bfdfe7dc352907fc980b868725387e9850b8d2f637fa249ae088045a51245f86"}], "guid": "bfdfe7dc352907fc980b868725387e9898e23ba2e10145ca0681a5c1fd5b4a26", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9876c182305322910db8d312edeb06e3b9", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98360a9905658dc6f705cd219d36c55a1c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}