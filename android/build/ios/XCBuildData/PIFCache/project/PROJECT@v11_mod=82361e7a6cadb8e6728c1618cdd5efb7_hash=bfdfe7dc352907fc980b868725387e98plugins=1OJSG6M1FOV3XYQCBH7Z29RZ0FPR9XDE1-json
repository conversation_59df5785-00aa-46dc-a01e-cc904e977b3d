{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9866084dfffca259a19e92c457c463132d", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e982f88f3641eebf0bb6bcdb0b45fa8521a", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.6", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e982f576070a878b3723f4268aa94216e4e", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c64cfe3018fb5e35f84288213bc9cdd9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6/ios/firebase_analytics/Sources/firebase_analytics/FLTFirebaseAnalyticsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ef927cbc9db9f913691cef0e116c020", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6/ios/firebase_analytics/Sources/firebase_analytics/include/FLTFirebaseAnalyticsPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986785e981d8d8763525db7f2e1add3264", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98578ce4e49937448f6ec9bcf79a5b92f9", "name": "firebase_analytics", "path": "firebase_analytics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e4b0a67e0783fe9885ca779c6742b97", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981694d9d2de486a12a0805570b7ca84a1", "name": "firebase_analytics", "path": "firebase_analytics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b54b3e5327a0bae6cfb271f6d3ed66cb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9813745eef962d9c6d5ca873d5543822a4", "name": "firebase_analytics", "path": "firebase_analytics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b394c553156c80773af3c730b0e0b6a1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3212f8b9788a1cdec9aa130d3dc50fc", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98322525f4933d706fa5d01470f2e926de", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837c90b9175baee493f025755e024aea0", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a40568b100317d10fe97e84280def0e0", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c43b66ab90d0097ec72c905eae55e161", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98774412d400515fd34bd161ca6e65e946", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98582f030bf88e7ba6d1f489f19025de38", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982664b2ddd1a0efc9bed5d97254a36cfc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984718e4b3dddc9f4170bde5d3f3e5cf02", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98492fd96d0aa9c28c0c8eadeb637e9025", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981fbbd5b1d4e18e33d280d639672bc0a3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98553c05dbf8506fb834901a08561503ef", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9840e4e0df950fd587a1923fbdcd75545f", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6/ios/firebase_analytics/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98dca79943c5696f2b06f10cebeba28e0a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6/ios/firebase_analytics.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e983663321dacebbd621e0399d871fcf56f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c96aabe28ec64da8efe7744a319417aa", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98fd854b62f18eff223093d46f3c977eca", "path": "firebase_analytics.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9854809ff8d50ea341996fa45e8cdfef27", "path": "firebase_analytics-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ad1f704951433ff87179d8f514a33b88", "path": "firebase_analytics-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a31f59a163877695cf01851b3bb18496", "path": "firebase_analytics-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9836d2a3cbb9362ff4bdefb52ea06b98f0", "path": "firebase_analytics-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98cd0abda72250ef24265fb06f92e4d3dc", "path": "firebase_analytics.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9899d0d63fb865a1312c91ac168bbb211f", "path": "firebase_analytics.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98331c30637f882970083489ed31762f09", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_analytics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98adcd91f6ce4a304285212413c3a176f4", "name": "firebase_analytics", "path": "../.symlinks/plugins/firebase_analytics/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a8a3143c1876af96192a6507f494769c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e9f193eb2b24c63d6006f73ef60b38dd", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/FLTFirebaseCorePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98abd1eb95a875949776449634a43eb14d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/FLTFirebasePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98424136618f3483b28a558c6a316ca5f3", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/FLTFirebasePluginRegistry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9891fa9fbb4311575864f911da75fa265c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad71f61746836e84b4e4286091ec8774", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/dummy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98391442d21db130a0db256ca661876223", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebaseCorePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e32cf5cd844d386d77c8d9d786541b2c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebasePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984a4c3d1d4302d4bcbf70b8f7409ee355", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebasePluginRegistry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805daac3ebb7b725dde4fed23cf7b061a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c6f2e762465991bf3332020bb45221e9", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e6497e309b6fbce259e6c36c1a6698c", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870947216bdf2ffc5a4beaa32376a18df", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aba26e5cc000a8a469b96cd04039361b", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc65c2cde55a9f3fd04b8be88f6beb4d", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98564f73c46e2dd874d87a842aad69b890", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98705883887d9641332e8c3402b1e5aee2", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9882a014d261d87a1d08412fd2e99e3c25", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ead31777a27ffdca10e9bec69ab02f04", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f93575810fdcbfc2e1596ed11046a30", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877ef5f3b8413c9736b4518eb0e19fb24", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0d6c011cd6878bf4dc6f80fd13529d1", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7ebe1db2c99226f971132976a077482", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828ad6802077895fbfe50fbf4f5d5588f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5107ac4dc29aa9cf30b588a3f8a0d6c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988074ae0505e8f20cc0430eeba869ae36", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5b2bfbebb53d210c7d4126e69e38fda", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9673246558c4a11b5dc4bfec356abd0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db8641b630b865d54eed54362d27e840", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986860013e46c7a1beaad297480718de25", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9874e84246b6f95ef7ff09ba93c9500390", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98aaf97e0d874ecfd66222bf54978dd485", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/ios/firebase_core.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e981b47a7c266947d5f66027ef4f1ede46f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.13.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9815e455772a6f985732df311ac54d60ba", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98485bc3aff8166b503d39827f2b4e352a", "path": "firebase_core.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f8094b73006c9c678f143934026be3b7", "path": "firebase_core-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ef11ae81979f440db9b86f11e6c03dd4", "path": "firebase_core-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9801ffdb3582c8fd597f44067d1f7843f2", "path": "firebase_core-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860f9aa2b8f894ea82a5bfa3c57b5363f", "path": "firebase_core-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98289804565c770a2f4b5ca4786bd6f44d", "path": "firebase_core.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987a5d06895b3bd65aecb66022f366ae80", "path": "firebase_core.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9893d57932281ef076b41a8d06c22615f1", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e8483a785826764515c19448c34be86", "name": "firebase_core", "path": "../.symlinks/plugins/firebase_core/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989346aa78c4c60ce9bfa7cdc62f020b7c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.6/ios/firebase_crashlytics/Sources/firebase_crashlytics/FLTFirebaseCrashlyticsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cdefed26778189a0042c83ad43baf307", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.6/ios/firebase_crashlytics/Sources/firebase_crashlytics/include/Crashlytics_Platform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838b42e4e168a579ec09c12032049ae7a", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.6/ios/firebase_crashlytics/Sources/firebase_crashlytics/include/ExceptionModel_Platform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895e75f6830c1b92261fffc8a9bf39280", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.6/ios/firebase_crashlytics/Sources/firebase_crashlytics/include/FLTFirebaseCrashlyticsPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98acdb60c18e2997197eb5e3fc538816f2", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6c77d9a3226a1faff14c697f779782d", "name": "firebase_crashlytics", "path": "firebase_crashlytics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8547c7a460adf7817b006eaa7a0a743", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9823416488607943fcf97e208b4867c04e", "name": "firebase_crashlytics", "path": "firebase_crashlytics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9817cb4831aa1045b412d493d803c5e6e8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9893e537a53c5f877fba102a62134d1e3d", "name": "firebase_crashlytics", "path": "firebase_crashlytics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a772e921071ee17f7adc9f09408dbd3a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820ff034e685849f7a4986e242892d47a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980eb4f960d5b2d3e7b9802a3441f97956", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ddff9bfe7dcb11ecf1e098fbb4254107", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a645b2b03ea88fed84416891534f2961", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98490cdf48ecb7b21cea730d8b9befda92", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ffe8017194099c85536c0fcb72954633", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9841bd7c6d3b0d7a1548c45247f8f5b61e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872b93d948af7130e98f1cb341117b3eb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988102cf965fe12cbb5c4fc534c0668291", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d13bdf558cb1143ba4dd55d22293b75d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881f97255680253e5ecf98f0a53c1f211", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8191df6bb97d1653153c5c4625a5af1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c8f6d3d23e0c88e3e4e8820deec183e", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.6/ios/firebase_crashlytics/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98dfe7ce9681a0b3d56d0239502e3779f9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.6/ios/firebase_crashlytics.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c87f2e33734a6bb89e3f94fcae724611", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_crashlytics-4.3.6/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c0eed07de1f8fd1282f69a43cff1bd0e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988995a380b4c64e4ad7ba4de2ac49e56c", "path": "firebase_crashlytics.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d31eb6520858fb23148b68b6a4391792", "path": "firebase_crashlytics-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985e83169589385534dcb39afa3db52aec", "path": "firebase_crashlytics-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987b7659eb465550d5163d8d6113bce352", "path": "firebase_crashlytics-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cb8b38053856c44174456781752b48e7", "path": "firebase_crashlytics-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983f7f454b162e51a013d7dc71db1e4425", "path": "firebase_crashlytics.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d339abbeb226ef1942cce9695f089d05", "path": "firebase_crashlytics.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cfe1ef597c255042adca925527eb9130", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_crashlytics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98707f6a78680074bfe5a64020791528e3", "name": "firebase_crashlytics", "path": "../.symlinks/plugins/firebase_crashlytics/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98104a32cd7e8b4d7ff3248fc1e1863651", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/ios/firebase_messaging/Sources/firebase_messaging/FLTFirebaseMessagingPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98db60bbeb4d7f03ba0e5c046cd8f13f5f", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/ios/firebase_messaging/Sources/firebase_messaging/include/FLTFirebaseMessagingPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d6a5f0f430b97dfa2df7f8ec80f46067", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d1907936bdf10c152f48c0f8f6b40948", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853a8e814449ea7d9353f9bea98eb26a8", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad32f567008bc922ddf92cf375fcfa5a", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818586c4c10b0caff93113bcf29ea20fb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804fd0a1e2931e7d361cf1e0ca5dc7ab2", "name": "firebase_messaging", "path": "firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98306c5dedf9f6f9697002c02da38195db", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4fbd61893f142cce34cb618b4dc280a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98360ce4ed03a0f087b05b38befb54e6fc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870ecc1f38634e041220b4450bf8644b4", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e15b94a949cddd95970575290f2a0527", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b69564f6f20f77165e2cd6e3430481df", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e602dd49a323de70fbafe569a2feed6b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981abd9964c2736298ebc182d9450159a9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d59156470fe86af73891601c7dedff0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815413364ba5e0bac127d26ee74255fb6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b818226054131c64785c7f2177f4bd40", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844fa31b6c0feabe8e540f1e6049acd0a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6573993ca66b93c5ffdbab5bd00c0f7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cfce635b7b5806eb60c45d205c4f0391", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/ios/firebase_messaging/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98c19b5bc79ab1fde888ced149802b7d7e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/ios/firebase_messaging.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e988cfe8272648abc2da4cf0bfbd35e02fe", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985f08c110d9f385151df0ff9ec23cf9f3", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f143ae610c60b776d35d00a3db17e392", "path": "firebase_messaging.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb8d707e8645c4f525f0176f9b102ed7", "path": "firebase_messaging-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98db210e21e44288ea8ac26794589b752c", "path": "firebase_messaging-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d084cb4d5bc5f49894480b7afe682a25", "path": "firebase_messaging-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a34359d4efef26a8b72addc37abcfb22", "path": "firebase_messaging-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984af9a7cff755b82b54735886c24d0a60", "path": "firebase_messaging.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e5e4be382898a14fb2a920bd1231c865", "path": "firebase_messaging.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9828ed3cd6008b39859f8b4e2ae138069f", "path": "ResourceBundle-firebase_messaging_Privacy-firebase_messaging-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98556b778a4abafdc38da1c7a4dd85e08c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_messaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98afe0981798e98a3dbabb0e8a91268827", "name": "firebase_messaging", "path": "../.symlinks/plugins/firebase_messaging/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98163f1e538e60cb93d97b25a92215307a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_performance-0.10.1+6/ios/firebase_performance/Sources/firebase_performance/FLTFirebasePerformancePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98952d3fbb7389e77fa64c966ee75fb2b1", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_performance-0.10.1+6/ios/firebase_performance/Sources/firebase_performance/include/FLTFirebasePerformancePlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a1b3d15b1654518bc96e11693744429f", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f20a7f2377c0307b4341a4303cf7026", "name": "firebase_performance", "path": "firebase_performance", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9eba70e86f54d1b5e0f50a809f2bcb9", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899e2d39efab1dc30d7bb368b25cf27bb", "name": "firebase_performance", "path": "firebase_performance", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98033cf1b2545e44fcaef2c95856bf6a15", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a781e4aa654b9349357dc74df9689a4", "name": "firebase_performance", "path": "firebase_performance", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1fa06b872b084381c5fdab9363800e1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e4be1ed257d0b80a87c5909c44bf0c6", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98edfd31a9f60a78cee9583e75b1985a9b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7da2a48f93a07e7afc8a802d9e0ec39", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988091efaf50da0492e24d2424a1db6567", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb70eada6a267a6da14d4de1785b5bf6", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98534e14e19393578aaf6790ea81f09dc0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6329d0ac52b211121d0f064e80bcd1e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989aee6ab99c5e1919d319ce22ffc6f8d0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854f6ef17b39740b422a1d73bea806de7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988186c4b1a1a1c13551c279de1d497199", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985a691855ea8fdedd1b26bd90fea66fd3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98783b193cfb270916a08031fb2db63cf6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98393f63896a5f7e6e5caa2a16adfb1eea", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_performance-0.10.1+6/ios/firebase_performance/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e984d3397e1a041e9c3bed21dceb6b8bb54", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_performance-0.10.1+6/ios/firebase_performance.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98849df1e2461d0f9700d26cb401adb2df", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_performance-0.10.1+6/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d0fa809aee70cd20f32fbba88940d831", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988b01030cae9b3b48ba77abce650b5b0a", "path": "firebase_performance.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982d7537c8e67a374c5e41a82a33d36dd3", "path": "firebase_performance-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c6a540619bc6d40a5b29bd684f502a9b", "path": "firebase_performance-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a271dcc22bb68a61861051729f30be1d", "path": "firebase_performance-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b9708a860aaa350cadb4186849d58cb", "path": "firebase_performance-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988b3180818785f0ec587b367e88f05bcf", "path": "firebase_performance.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f8dc9d2665eaaa6b7fbfe6bb11637969", "path": "firebase_performance.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983a048902376540efaae0ae2d66053ec6", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_performance", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c07e4ec578766ccac75490acf87fbf48", "name": "firebase_performance", "path": "../.symlinks/plugins/firebase_performance/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f6b8ecc647afdbccb77e5e5e5384ebf9", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf31628da72310ba8bbc220eb77b5b01", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981da6245d1a8d4ae1f33688156c5eb8b2", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9849c0963ebc00aa29e31940b43e213d7b", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9851525a36880121c055449bd025f012f4", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98110c670ec683c6f57ea64b8ee53e56f0", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98741a220ba689ffb32b0da53e94735a57", "path": "../../../../../../dependencies/flutter-heartbeat-plugin/ios/Classes/FlutterHeartbeatPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9870b3f11ad88e6557d268c1f42774e6f3", "path": "../../../../../../dependencies/flutter-heartbeat-plugin/ios/Classes/FlutterHeartbeatPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9803ec297d49406310136bfe42538f9b66", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828624a7b3ec86a24729c09c826a99369", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9813c8c408987dca5c715f7646b5b6c655", "name": "flutter_heartbeat_plugin", "path": "flutter_heartbeat_plugin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e5a3838abfc485d38b39c15e280e6c1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871f1c964f82b7a1d50a0fe7d8e604116", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a8a2ca463fc13d342a133578a23469a9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808d95c918724c63d73a3ebe060f34280", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98026a79170d0cf1b1d8e8ace0bce8f3e5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989927cbffe1f46672922511f6809d2347", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847c75145d941324f39baa421e3f5e2ac", "name": "..", "path": "../../../../../dependencies/flutter-heartbeat-plugin/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9864daddf4aed49e119c1602b2e8323dcf", "path": "../../../../../dependencies/flutter-heartbeat-plugin/ios/flutter_heartbeat_plugin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9876c39f9decf0e065ce48385b999e8d14", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e982f96b029e5f09dbe9ba15a7f2df9cc61", "path": "flutter_heartbeat_plugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9874975ac5e28d4e4cd6f8a949466bac3a", "path": "flutter_heartbeat_plugin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c6dac741d9f11ba786dd98e8ff8ef39e", "path": "flutter_heartbeat_plugin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1d2b49346d511b1f834e7ba62c55cae", "path": "flutter_heartbeat_plugin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9843814a5726010a8e5a503e8de1b538cf", "path": "flutter_heartbeat_plugin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986165fd1fb852f2560d73631184fc6012", "path": "flutter_heartbeat_plugin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980fd34012280c42d3f4c8905f90774ec5", "path": "flutter_heartbeat_plugin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9828ce417d570e3b261d288b765d76dd26", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_heartbeat_plugin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986af93d77e47cf8e564578ab46ca71ea8", "name": "flutter_heartbeat_plugin", "path": "../.symlinks/plugins/flutter_heartbeat_plugin/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a0258a1ada47be9af192ad931f1223f1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_pdfview-1.4.0+1/ios/flutter_pdfview/Sources/flutter_pdfview/FlutterPDFView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983960664880789f8a482aa2e209bd237a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_pdfview-1.4.0+1/ios/flutter_pdfview/Sources/flutter_pdfview/PDFViewFlutterPlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aebbbd0092803111171f796842854588", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_pdfview-1.4.0+1/ios/flutter_pdfview/Sources/flutter_pdfview/include/flutter_pdfview/FlutterPDFView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98168e109e56d8c4bee63a14689c7585ab", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_pdfview-1.4.0+1/ios/flutter_pdfview/Sources/flutter_pdfview/include/flutter_pdfview/PDFViewFlutterPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f45dbb4c0a62fa7e089482341f296ab9", "name": "flutter_pdfview", "path": "flutter_pdfview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989de3d903046d09b24698718272e8c8d5", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832de00332a5dd0e45441a7228e086015", "name": "flutter_pdfview", "path": "flutter_pdfview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9840f57589ceafde05a1bd813c53c5d127", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e901160d09427928fe9d0569d37e6bc9", "name": "flutter_pdfview", "path": "flutter_pdfview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd3b86a160d39a8449c23c185d2f1862", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98338978748b134c0223b046ad9724a63f", "name": "flutter_pdfview", "path": "flutter_pdfview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5a726c32010b25177340ec80df97583", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981daceec8eafc1abf932c976a60b00f38", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb0eb2ae5204cc66198f7afe0ef72a46", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec7b3427515d5320c6f115254e85e3da", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de3c9ed3e49e441012c4f94e9906e771", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867b7de4a63ebd08efa179524ed7e6ac0", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9072c6ecd88d65e4f9391885389df73", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844bc9d2d56be23396b10e449db6a7686", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa875bd4e510581e526537d3d946a396", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892a789676b6c2390627d8b5aa9cff065", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985bdb73f9884e227c62d8e063ea5e00ac", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f1398d68f41397035286874b9fb22ce", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a718d75862cc7b7f17b4cea95600ff62", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f7bf0e44985aa3d41580248ebed86ed", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_pdfview-1.4.0+1/ios/flutter_pdfview/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9802b4aeadbd5c8c7a6186940aeb7775ff", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_pdfview-1.4.0+1/ios/flutter_pdfview.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98834ec6ad101063eb2f913ae5cdff5229", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_pdfview-1.4.0+1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c41c837d2c2c5eca77c3fbfeb4ca0cb0", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98386d88814ccbf80d5f746b01e0e3080b", "path": "flutter_pdfview.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9879597082847d3628e0c413f475f89567", "path": "flutter_pdfview-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bb2d8a66c82d21affe8d3b01a1ffb630", "path": "flutter_pdfview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c0946293b61474bea41324085dd0a20", "path": "flutter_pdfview-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984df95498e5e810aa909915a9f8195c37", "path": "flutter_pdfview-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984660b10f4039cc93e8b4a8dd72d7e42c", "path": "flutter_pdfview.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98358c3649331152c8f8bee90564704d45", "path": "flutter_pdfview.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9874102a57c101e24697d389e1260bda1b", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_pdfview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cfaaad5e9971367653e02ddcd4f009f0", "name": "flutter_pdfview", "path": "../.symlinks/plugins/flutter_pdfview/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9858ec2eacb4dedddbaa79e5d5e70904b5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/FlutterSecureStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf394ca8629a187e0940d35d0ca16a9a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/FlutterSecureStoragePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986c8d02d1165cc779823ff3b35e39ca5d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/FlutterSecureStoragePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f1a15f0c7844e6a48dfd9c94af7e5da6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Classes/SwiftFlutterSecureStoragePlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bff4dd4010e52de55e0670b674b721bd", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e984e4801d9c08052cf3f97e82b97c60b78", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984228f922d03929eac86a825866ea9092", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9819d477e7b55196a1c171157595c986c6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808cfb7ac36c6489aea73ece89cc6b55f", "name": "flutter_secure_storage", "path": "flutter_secure_storage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f079046a1a789affacd14230707f527", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98687f806b41beb274050f3b01c74fe646", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f60fe5de84f3723d10f67f2da47dd280", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855e00d98711ca4a958e65f1177be07ac", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980360a839ed5d65b7d0d0e5547427f9f9", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98694b0d289a498d505779d22817b727b7", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892307ddbca53d24154177fffc84125c6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f626720bebcb241d6e0fa1c7de249097", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9825de9fd62f429d8f054d8dc51deedd39", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98623c87512ea52126c2a733122d18d8a4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c03ec4c5d972de684fc29901531da3dd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a245a53cc6da0c1e59376290d12916f9", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e989d98885e309561a51e39a298669a4648", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/ios/flutter_secure_storage.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a57697f315a97365ee446fde2ebdda65", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98072e79e1c4400c7ab7e50b72758232c5", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c3ec8bc8d6a5b16096215846650c8663", "path": "flutter_secure_storage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b6dd1aa76cbe3c0726b8dc395758f64", "path": "flutter_secure_storage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98098a9cdf5cbd8e424a57bc5007457470", "path": "flutter_secure_storage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5f213b2b28a6c951c381df815dd45d9", "path": "flutter_secure_storage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c3e6f0d14d8bb5c06e399768f81693f6", "path": "flutter_secure_storage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f7d12fb11b2cc4798ac418ed1fcccdc1", "path": "flutter_secure_storage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e4c195d2718ad0875fa6f93a8e6cb898", "path": "flutter_secure_storage.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f6bcb4671f411df4ef5cef2cdfd37953", "path": "ResourceBundle-flutter_secure_storage-flutter_secure_storage-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988e79f73e6a09237d28975cca7e86987c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_secure_storage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca600d67d212e3aed6c54c2460a26e5a", "name": "flutter_secure_storage", "path": "../.symlinks/plugins/flutter_secure_storage/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bd8392bfe4b4735dedba59940ff5dc27", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.6/ios/Classes/CydiaChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988c5786dcc40f2c25aeb9af456cdb8a6d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.6/ios/Classes/FridaChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9847d2038e6434835c4447315dcf7cd6e7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.6/ios/Classes/JailbreakRootDetection.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988edad3e384b6b28000be2bacba72223b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.6/ios/Classes/JailbreakRootDetectionPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9864185c11252fff56b0451b99d5626321", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.6/ios/Classes/JailbreakRootDetectionPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984ddc9253da1455b15df4cba3d8789a9b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.6/ios/Classes/ReverseEngineeringChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9874e1f1fb5693bb77117e6efcf2167e23", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.6/ios/Classes/SwiftJailbreakRootDetectionPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98162ed0fe7d686bf1ac6402321ec25d58", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.6/ios/Classes/Extension/UIDevice+JailBroken.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9895c8bcc9e3220a176ecd17e477f96420", "name": "Extension", "path": "Extension", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f4ffa7e7c2e2f0f4d3a5ea9664ddcc7", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980355d71a7b2edfa5eb9e7a7915ee9b46", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98440620d6fb9a6fdf1167f8bfcdc4dd71", "name": "jailbreak_root_detection", "path": "jailbreak_root_detection", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98979fadaeeb4da2ad8a3346d0c4baeac6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984810142423dffe73802c3cf1d7e66add", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981677cf808eb64a8092def5f6b0a7e56a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da28725d3b0683148160d18f86e751a3", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f03c147380448a2406aa0a69abfdef5", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98022e0223f2b5aee679e9d33924ba23a7", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a9baadea224b199356c5f56beea5c5d4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981634d08ed72246caa3c7df9be73e46e2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f07238e9af0e6b923cd4782ec21323d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831a3c10b05692ea4a59e262a0480e11d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873fa9b664cdbd68a03e05fcc7145345a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6d4f1fe6da750a1c0a57828a19dc788", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.6/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f3f2629cee78d50e35e7099a0edeae6d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.6/ios/jailbreak_root_detection.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985d7d3e67400245b9d525c4261c1d3946", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/jailbreak_root_detection-1.1.6/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bb3784583c298f291b7981adca7ea118", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ff1eabcf3071e815362b4b9f3f5b7371", "path": "jailbreak_root_detection.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987ab568adc296a69957c56afa56ffde38", "path": "jailbreak_root_detection-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9815e4abd31e11c3036180688951f5b86e", "path": "jailbreak_root_detection-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828a77cf4c04c47f93cecd3a2f350b2a1", "path": "jailbreak_root_detection-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b127d641824bc3d3cadc1b95e164611b", "path": "jailbreak_root_detection-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9819d654a0c8b614ec6af23ff88a893dfb", "path": "jailbreak_root_detection.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f8495e3bbe8572381f204ae55c785cdd", "path": "jailbreak_root_detection.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988a8ff95accfe6654619c329ddca25f4e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/jailbreak_root_detection", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df4969e91b5a531c2d5c390a40d0398c", "name": "jailbreak_root_detection", "path": "../.symlinks/plugins/jailbreak_root_detection/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d2c293e45a65f1fad0c519f4b02a4f59", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/darwin/local_auth_darwin/Sources/local_auth_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987116dba84bd026eae45993c412327125", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986521c4c31deb214d099574067bb14d5a", "name": "local_auth_darwin", "path": "local_auth_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98668eecd9ccacb61cb0b533ffef8db20e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985675d3e2e7148492bfa9766cc196efe7", "name": "local_auth_darwin", "path": "local_auth_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb0feca43514db2f631ee1b29af2c455", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa25d33fa57f1e4e5d3f57338f156847", "name": "local_auth_darwin", "path": "local_auth_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f63f1575f14bf8bfdb0f7c1a68c0d3dc", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e96d4033c62c8142f13f98e1a7880d95", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9eb2776d852d15fa191c40345aae00f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98028a756808dea156bbd32b7eef539756", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98adc4f2bf55e2b82c1069a50f8dc765e2", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a4706b00c27c2d6e45a39459960a269", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812684a147aba251007e4ef0b7506caae", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9836711f59e13a67756593bc6a39b675db", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/darwin/local_auth_darwin/Sources/local_auth_darwin/FLALocalAuthPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb0e005d78f2617b8416259a30f69fe9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/darwin/local_auth_darwin/Sources/local_auth_darwin/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986360420dacb1f7f585678b744b263e01", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/darwin/local_auth_darwin/Sources/local_auth_darwin/include/local_auth_darwin/FLALocalAuthPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980d4ef03a58eea0ba0cd392360313a834", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/darwin/local_auth_darwin/Sources/local_auth_darwin/include/local_auth_darwin/FLALocalAuthPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d4b7f4f7798599c7428b0cf4601066c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/darwin/local_auth_darwin/Sources/local_auth_darwin/include/local_auth_darwin/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d43520ca56f0df575e9728a74fbd8124", "name": "local_auth_darwin", "path": "local_auth_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856793e6c0ebd8aae43ea3f7139ba5d9e", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855f47ce9ed99bfa2d60697f84eccf4f1", "name": "local_auth_darwin", "path": "local_auth_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb70fbf42fdc965617105938ef9a39fd", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb56e6da57947bf5383c4d10f3b6a80f", "name": "local_auth_darwin", "path": "local_auth_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a96a53bec457b04c7ca605c3a83a3514", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a6401f36768ff8333754e26a06e8c67", "name": "local_auth_darwin", "path": "local_auth_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98896e446254b4cf90df4c8f46dc57d9bd", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c17c062a719cf323ff813be44be3cd0e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fdb9adf75db0acd9e4d036d4d98ed9ad", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982dd532ba82e7ca63831bfcadb3f4aff4", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c307840a1504c3472cc90ec46d189fb", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f84f8e531d5de2ad6a3638e2045473a0", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871611b077817193fc1facb0fc6730a4d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f5b1171b588c93b7d08acd7687432ba", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98411bc8b44682f9a963ad17332b3c17f0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981ef17b6b7809f0cac74d79f493cfad16", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980356df951e08a5f84e745a0d184a079b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ad64c1cb255ed11613eb2cc608ee271", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b5694261693e9f19bb52b43aa539bde", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98375fafb9eaa214412233e874ea7746bf", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/darwin/local_auth_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9868e3a4d66d9b71dfbc19c81eced2b3bd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e75f92b551917629629df5e96dd32417", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/darwin/local_auth_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985d657c739f449a79e9eaaa8fc298282e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983c8500366273345bfb25475134dace84", "path": "local_auth_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ba70c19adae882e686ca1650450ff2a1", "path": "local_auth_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98eb748404f185d2374d98fe1d1a829fb4", "path": "local_auth_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c4a24a02628388d1fd1b9ab551523a2f", "path": "local_auth_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ccd329853f700e875964412f8a1ddd12", "path": "local_auth_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c862f9d8c0fb0f762142d7241c701dad", "path": "local_auth_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b9fca4da3123a97d5aa382462054851f", "path": "local_auth_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fe783e6f6075443f5f50e1080a6b5d4c", "path": "ResourceBundle-local_auth_darwin_privacy-local_auth_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bdffd779182a11a2234fa341f5259848", "name": "Support Files", "path": "../../../../Pods/Target Support Files/local_auth_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834161b88550819b14c29b53adccfcb50", "name": "local_auth_darwin", "path": "../.symlinks/plugins/local_auth_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad2a46f5f3c22056b0635c8b97b418cd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/ios/Classes/OpenFilePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9869e48e64830f43e279fd355ad8efb915", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/ios/Classes/OpenFilePlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9829ed4ea7c62944a2b43da383f8fa7ecd", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d58094148168511eb172e653263c70f4", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855faca4f38ac56f4ddd563b1ae1f9c9e", "name": "open_file_ios", "path": "open_file_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcf61afb9ba5c7174dbddc0d3a25a9e5", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d0a93c74be9fd025f0b886e05c0fa81", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848a9e012f285aae86501ddd0df04d01b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871f0f890ef077b9e486dfa54b62fe8e0", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988fb48368008c48c8401a753a6ea86ee5", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f78ee29f6b3c752c2531ac9d68a4f7ac", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857435cb89c1101421531074574ee3357", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f781faffee9bc54006a7f6c53c5a7ac", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b650b80f50fe7cf9f2a4c9814c09ab02", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98211f1c8ea443eb194d86ffc535ab584f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98469a5e67181d6d6af5ca1e95c857c4dc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809804567f8e2d6a55d9dd668d83bd8f6", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98414eac7476384befe6bcc90506572442", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98dd08f17b852e0c80fce833585ab88ab0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/ios/open_file_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988fe3376dfd7c10960fe45dcc8fa33a0e", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e6be1618837253f0d798af46fd58ab06", "path": "open_file_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981844055edd6df5c3ee2a8b630323be43", "path": "open_file_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98908c43370fc5d9c16c130aa27c5d6dfd", "path": "open_file_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98787756e0bd5ba1795420a6779fab7d37", "path": "open_file_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98948b31f22cc1d4ffb0c38d8474bb710e", "path": "open_file_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f2c4af21e5ab3ec138abae0eb05f9e6c", "path": "open_file_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d4fd27a96991c00596da1021c7061773", "path": "open_file_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ac0ab3274a05051235a82b1365c8c8f0", "name": "Support Files", "path": "../../../../Pods/Target Support Files/open_file_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872360da2dba15c40a221337ce8b43638", "name": "open_file_ios", "path": "../.symlinks/plugins/open_file_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98903116254dbbe540e8412eb4e96ddba6", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a373f14b571140a9f5f17fc72d9772f4", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c34ffde8a3a668dcba3b368df7f8a1ec", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984fc4afeaac38ce5f375fed277ddad27e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3cce9b2c3da58b3f43484c35184b7b3", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847001eac011a0b86b59572d873a6e0e1", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed74ccbf185e18b8ee3f466afae66990", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98798966f8f3d3598bc7c91fe4d6e1e835", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ffe897ef412e2ae9d08de73cc3cdf80", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fce9009e68b8a07bec3f93f3957dcca1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860aefb4533f8cc7e969cedbbf24fe68d", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98535e338ed03371cae49b33d6d367bea5", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98444f33f127d1a35f082b219f4a822f45", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6f82275fe7b741750fe3a9d8f5eb077", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f7fc11820932612616297d292f82adbc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98da6439042c49eb241bd7cc057692c20b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985e75c5f88bbd1f678ea950fcffe17715", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898676fa861a7c19ce70296a409793bb0", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce70f39e131a650c9e9acff4c006a8aa", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf5397715d1facbd3bd24534aaed27fe", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e44472ab8b4e3ee56b74487ff722964", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f6d1beed153abd5d13ebc3099b5fdf4", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a27bd9ce7a361c21502c25dd6b3a488c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98812c1b3a191f7bfd78648cff226303bc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e830b8aa3ccc5de2e9c62cf77f4cbbc3", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b0be6f4c5df4bcf2930e1738ebb145b", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f4a9af9d2f85751046ca252feb9920d", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831654d5310fc566e4760bfd1b371e245", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7014484d08d07bf98117bd6c1b71568", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9864fdb8eb091683b56b8d8098007f7d71", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98099be3e5ac5be1f13e9b4d79bd734a36", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ac57c426e237c09115def936032f3de", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873b8154d3c6d444e3bd50165e8743c4c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a6aafa0cda5e1e90dd734ab4755973c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98666ac623121b718d0482269af342c8c0", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98164173e0bce50adfd94271bb0ec51793", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e986879d92a57cf7d37d5469adf3ee7c47e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985716885fb68f9dcf6231b62a9cd4398b", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b1aebd871f30cc1bc7c292bf0ec6662d", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b37529ad138a2f32525c10612b0df71d", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c93a305256e47d6816bedc51b18a4625", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9824ec375202d9260e0dde8b5e77df8a9e", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98310aa117667562c3897e181fe8435ab7", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a2fce44cefa611fd537905ee076df829", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981a78b594a0cafa11d7de7b743cdf7921", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98685e67322fadf611ead6bb05272804a7", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d173ab6b98c2b6d07c8ab797451bbb86", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fae22d73869a5b26737f7d6288af87bd", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9814d5b40ccc54216c9d3f30a783370480", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/pdfx-2.9.1/ios/Classes/Document.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9834fa98c509e43b7d8ffe100472bd9071", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/pdfx-2.9.1/ios/Classes/Hooks.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989d2cb9c0411a6b167c372531f6d64cfc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/pdfx-2.9.1/ios/Classes/messages.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980af28a708676a8e89c6704459f3104f7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/pdfx-2.9.1/ios/Classes/messages.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9835fcbadbd7e2b949fd902be00b3542b6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/pdfx-2.9.1/ios/Classes/pdfx.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989ad49efe0e0b7eb8579089a14bd84138", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/pdfx-2.9.1/ios/Classes/PdfxPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98504a41e810fecce15a0bb620e79922da", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/pdfx-2.9.1/ios/Classes/PdfxPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9828cafc134b1a41ab014c04f2ec6f9977", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/pdfx-2.9.1/ios/Classes/Resources.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98469156b699b1678505443ebafdafcf27", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/pdfx-2.9.1/ios/Classes/SwiftPdfxPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e07001b97025cb0afebb938b3c67b46e", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd1a941baab3294c27baf674a961ebc5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983775c6a52ac054e26701945a3249f251", "name": "pdfx", "path": "pdfx", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986688219ac7977cdf3f23be488b425346", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c74034e934039edf8276c7df032848f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983ec5fc433a042bc9d304ae4117d01693", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98690fd7fa83c08afe94e7f939e38ae29e", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef1c4b6eee3ee58a3d766419e4d93d6d", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d13f136ad9d2e9e28873126fcb47ff80", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bc0c4fec7217bae8db0434b8907dcd65", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a6fe525f9f169e5c63633cea97ee2fd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984dffe640a8b3826f1b2989aa3fc6a585", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891b1a860b90561286f8667696f182533", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9813de9e95c94f5f3d320f9bb5435b11f5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873d9a83754a20d2ce952a3c734d99358", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/pdfx-2.9.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98865412fad3e63e8fec7feec7ac70d503", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/pdfx-2.9.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9849ac0439d963d787b557efb68487a010", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/pdfx-2.9.1/ios/pdfx.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9845f8d423c588ebf192a9fe3ebf5f431c", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98fd370ed92eda9b147e6adacf25aeb5ed", "path": "pdfx.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98058bf6953db886e9500f93f020afc2ed", "path": "pdfx-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c0270f4231fcbac782364ba505edb47a", "path": "pdfx-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98594dbe4a5ac12bfa5f47fd65087c51c5", "path": "pdfx-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f569494504441fbd98f66b5fc97ea905", "path": "pdfx-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e9cabbdcc5a7cb8a3b8ca24d58c8a83e", "path": "pdfx.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9849bd41d19f304dc6f5c5bc64ec97e24b", "path": "pdfx.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f7a2cc10273cd79d34237984792690e7", "name": "Support Files", "path": "../../../../Pods/Target Support Files/pdfx", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad7c132f01e82e52b537e9b6b7b6a31e", "name": "pdfx", "path": "../.symlinks/plugins/pdfx/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ca2674841753ab0d8455269a667115e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a32853bdd97a8e22493f19d1a58ca71", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c31cecda7838066a2a43e9c6a23e6f5b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9813decd3c18674d2b108f1406e0fd5f71", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985c17668290c1fdc518f47096f4bf1d3e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98028419a607d0d1b20f48644d5cf5031b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98479cbe0249259dc2256dfc29a76e2bf3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838670d4bc94c8cca107a49cdcfd23bee", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988cd8a8be123f9c9b51336fb6470dda91", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9808529d49557ceefa40ad2c5be8ad8b1c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980244933d0c721dfe61fdb092a5c9867e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a4db6c46d49f628614f1c1545b3c1c2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98919c53ddd5515424761387357382fc81", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98239c8651297e724977e2444b6c9aff61", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a61c8fb604ef91905731798ffa24449f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be812717ce8b4b3097c8f394128123ce", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bc72562590524ed5965bcc6c5ee4bd5a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9836143367b99004d8be2b1d5bf7ac2f8f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9863617b09cf5530db30b0b0da16a6a20b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c1efdf6a9e84d74f98f9bde3cf106b4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98514943699ca0ec8d10e107ad9f5a22d9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819afb86718aead4968ea82c883893bd8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9882e6b1f03f201e5ada8f0171a75bbcaa", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984021bdf7ce46bbd8b6cf861f9fe1054e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987853c919263a0c3ce3aa708ac35f1fb3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d2e62f5ff1f79b06974940360497baf", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9813f744ef70c91ce884fb1731714d4a4f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98146bdcb19de2f338371d76f5870cb19f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c0c8892f7d8213f0309c1ac6d225da82", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98392923b23ae1069700bdacebcbc79071", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f144c532b54bf8876dd3427bb41577c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9837da0d5777aae5d91d42efed4bd12c3b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ef6c9924d5978319877766c2396db87", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987a92ecc168aacfa332e31a6a4f8f1e7d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b55364978f9107722ad7e1177211330d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9806c1427d032ce92d78b3160cf4eaa8da", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb9c9117fb8a2a21e96a62ff4abb7f48", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e9d94c3b37bbfa42ae4c3cd88e5352cb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd66577b60dcff581c97775289b47908", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986415c465d9d3df9d14e9e7440aa469c5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c6e435c9c9cb4f44c4392d5ded89da71", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840dbf9fb13a16850fe17e8a0682c5af4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809c4e616be1032a89be4ad68b3f700d1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985aa3188ae1eb00d3c77744e772fbd403", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982fac03f76a9bf9078657f832f03aea2f", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a65561f5bb2b1a20ef989f6d967f0637", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98450c1e5876ef7ea40c45ea291e3415ea", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f4836f8444ff0d4df4855fe832a97e8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e244cea714e8d9bb9f573a4fe07c2e9f", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9882bd58319e3dd248aba62c8c39e5c51a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98edf31354b4a2e9c5c20148901bda6558", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98251633c973925b25a65a5f5a208c4841", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98299139a5e73c319045cf13d9aeb4da4e", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a2a403b32e7d4366b6329b3403d24bf", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982cd6c5b5b386656f84eaa4670be8c7dd", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b934f29c33af2f773fee5f253a02fd3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98904701200d2367925dece332912e23fd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98741ca929ce8db09e61b8a85fd2fcba38", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d65c87bfe07824be6bea8bce4e3f946", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877f6efc12b6dce70b928f2f053c80169", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98728dd476dc04b95ed0d0420b5cee1313", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985a60a398a0b1e8029282d066a0046229", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98ba2a69c979824662d47f250a4488a4c5", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986177849365390e2cc3447c4c1eb267f4", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98997fb34ff290f5e01bc010a3dca8ff90", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98266a55d0e8504b97e083d71f17451748", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980ba132a9767c874650919850d2573a50", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986c05ab0e4f2f342dafba9f0a1a02ce2f", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed6de6c128881ab117d27e5a751f7af0", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989be86154330cb3d872b32af518db88b1", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d84ded31a465ec7eadb112d2c7163582", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985223044c429e5174a77bf75d1ddd3813", "path": "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859caa08eb7e04d136084fad751243fbe", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983125ef274ee18a49dcfd165889490120", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d5e53be59ba54da867c7ab92ec035fb3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner_plus-2.0.10+1/ios/Classes/FlutterQrPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832e3511a937347191637d64b8d12af49", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner_plus-2.0.10+1/ios/Classes/FlutterQrPlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f6c7299def0ed3306fd3c9ec8d80e0d0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner_plus-2.0.10+1/ios/Classes/QRView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d1cc7eed51a533b66bb1aa4174bd999b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner_plus-2.0.10+1/ios/Classes/QRViewFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983aa1ab91a517d72d207875b3f5c00cae", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner_plus-2.0.10+1/ios/Classes/SwiftFlutterQrPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fd741aa8b731f8a2605a47c59ff8bd5d", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8e5fbeabe83aa20e7c67fd9d227761e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98020d04fb491b4173d411b3268ec709d5", "name": "qr_code_scanner_plus", "path": "qr_code_scanner_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c1a81ba1d2d768593b45c9dd1bb7958c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c8571a736cc393e9c98a226078b4846", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1ae1384b444c7524b3b29b4d39a8989", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b637176a144be86ea07b9c7ca3b372c", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc1b7af7a171e23c253ddd74595521b2", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb295ae656ed769e1ebc82d453bfe633", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eaeb933c2ed459c44fc88859cf6e282c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7bbf02a36404436fd62d3dc4e03bbfa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983908eea97d935c07130805443bec2493", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8ea725699af32bf6384eeb2f45a0f72", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981af20189b2902e061eace8ad7ff1f55b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e1c13c4357582a2975b7e67b6ff9348", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner_plus-2.0.10+1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9823599776356ee639952cb94e2d8c1c04", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner_plus-2.0.10+1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98ad98e91733663635d0ccda8485888082", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/qr_code_scanner_plus-2.0.10+1/ios/qr_code_scanner_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989aa68391bc14a46bbf8b06797d1a0c6d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98428cdbc7589154f895fd7fb412aeed13", "path": "qr_code_scanner_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c228a54b04ed803a99a5302dafc70ff", "path": "qr_code_scanner_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98aa3f7a7ef1c29b23262d11d2d00ec294", "path": "qr_code_scanner_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988dc61a7c87537e24acd2a54a889188ee", "path": "qr_code_scanner_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b376a017c5cbdd4e43a379f3ded3c9e9", "path": "qr_code_scanner_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98aa183b8ec5973cbe02910db41d8eecd1", "path": "qr_code_scanner_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983152d2a7192387f27f084dc580fed478", "path": "qr_code_scanner_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987a688485c10ddff4ed154ca4fc243e31", "name": "Support Files", "path": "../../../../Pods/Target Support Files/qr_code_scanner_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf4a71b56062d59c2ca6e607568fe9c2", "name": "qr_code_scanner_plus", "path": "../.symlinks/plugins/qr_code_scanner_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9861269f875ea7316ae1df5e1f090a71b5", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-11.0.0/ios/share_plus/Sources/share_plus/FPPSharePlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e983dc17e753ceb8980c8a936f60c9bc59a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-11.0.0/ios/share_plus/Sources/share_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98539786af9067d324ff08e985e766884a", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-11.0.0/ios/share_plus/Sources/share_plus/include/share_plus/FPPSharePlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9833ba9628c8b8fa3be44d78bf96626f56", "name": "share_plus", "path": "share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988929fd2c9cfb05f1d0cc037d91342b7e", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866af20f5f5a79df8f9afe1cee3f8f2ea", "name": "share_plus", "path": "share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c2ce46d9c158c40a46d601c739c95da", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6eecad2d6450a06e6cb23cadfd1c215", "name": "share_plus", "path": "share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a77cee6b1dabbdc2e20f1b4804c3a950", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d33cd8ae57fa0a4ba4b04c9a2e3575fe", "name": "share_plus", "path": "share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842e9ac1b2d3e8978891727b157a161aa", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988800f91d409e3c181064b7cdb3e15921", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981be130c222bf4fb095bce9353ecd010f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9838453e6f554d594728cf37c37cf944aa", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b398579f135a81d5ecd38273ce1761e", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4dbe0d64097893c6c25d2d448c59e0d", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985437d4ea3c0eb12e1cdb8abbd5d524cd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98824421c019dcdf8ebf3f71fc58fb17d3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98828e46fb03a1b1eb64d5084e898b8861", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818d2525fc9966bfde1ce7ae750d6962a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98343b1b4633127c010d0b75e1e0edc898", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d7e79001d87278fe9e67e6aa22834324", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897d076edca4f20d016a4338d2e84576b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f7b184131f89bf08732303dba396b59", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-11.0.0/ios/share_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a7b09f9b872ae64517a9d9ac53e7a954", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-11.0.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98ed4bdcd5860f25130c7b5e0867a9ce8c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-11.0.0/ios/share_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983270460cd1f91af465d103c3be3ae726", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a9e3c79efe74d3ff51ff8b24c833b45a", "path": "ResourceBundle-share_plus_privacy-share_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c156cd398c9bfa383643d8af852e1ea4", "path": "share_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986b9acdf67c1e1a163282313a281bd9c6", "path": "share_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989b725408f1325f77c8a4465a8a538df5", "path": "share_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cb32a67dec08a77f4ad56a8857cc760e", "path": "share_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c6ef68084a4b24fd6ddebb435d85a98d", "path": "share_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9896509dc99e816c61832d21ff3f4b0e03", "path": "share_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98cd1f8a7ba3387b6883d27cdfdcac22a0", "path": "share_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9830bfd88f7e7f60637c5810be9ce8fc0e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a5d90a36721c8681f999fdf0fac1caf", "name": "share_plus", "path": "../.symlinks/plugins/share_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98208f4f00babbfea56c1797ed53a554e2", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b3d173ed2311ee23c60cd5437162cf5b", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c606fdd97193506903fe3a30c90e9f7f", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca20a5741f61c6786ca8d317c695471d", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983dc089aee57ebebc8ce1da287d3b7150", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7bbad3b33c80e1074b3919691bab6ee", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea9c016f691c3ef997b12c6059da7b23", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9825cf9bfd63cf940e2c6c3a16d8f5ff99", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984fa2f7285d8d354571414c83b7accd11", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab1ff949abc824328b598a3a29ee6fc5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ffff4af3cc454d1007aefa6c2e28df7", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f39790482f2eaeb73af117d716609d4", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4afda56ba85e13783f3943c3025cfcb", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d87e31302b0118bf117c1f131767ca6", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981345b5dae62b40ee820679d9d3aaa890", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98059b08311312e3dc34f0c47e114f6bbc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f291bcf77a2ead97ff6f68b1abdeaaa3", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9887bf6492f0b15c1670fd1fcbe0d444fa", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824f790464a56137ed58054f06f8e1636", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831ee1212d5eb10616b1fefa50a804a1b", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ba37a9c26c77e0c3f42d66560a84874b", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a887839d1bf13060723b95ae4b604348", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f877744ff489fd3c75aa96d8197e4135", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988658621369f856b003a6fabb8ea1bb43", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b39b0cab7bb4b0afe99e58b63c9ba39a", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e33c23ba081e0979f6177c5b68c9c326", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889fef0090570c434c2216a0f5b07d034", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b16383d1e7055c2e0342e03d56edf1d8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d4fce5972ae903209d1d9f9f36b767b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98019354122b5a3de19e87ba05e335a484", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9822106470b369e2cb6b5ba54e17cf1d11", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d108120ec5c51689413087727dd81197", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c92a9bf4491c836541569dc600a0847", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98651487299a7b9a52c1c2bd265a7422fb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891265876a66fc35c777855c01e5a5ca4", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9848d611a05c22361b646838b30350d269", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e981025e4921e3348ffa30be2717bbc9a74", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c15d3a9d9658528be3eb420f17cc3892", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ab8c0a048853f926cb18914d45b319c8", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981de414a6563714cf198f65055731e41b", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9874e10ecf673b6b1552861e82571a3f08", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9815dcea4d6a59f863071b5add1ff96748", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc12adcc4d29ce7f0fd6d57a490f98b9", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b47729430b5c6b6a11ee785a4b96cd05", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bebdf6af33e58da2710f7563dbcf3dc1", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980a633352914e4871adcfbe31f2285bec", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9891b01a2dd14ba944a2070defedf116b2", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985da563b39e837320cc5c1b9f1666a653", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e985a84fe2f77c3f536b7b737a0d2cac216", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9893625f93cd962f8357bc28e3a09cacb5", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98814ba5aba85a36d4fdba4b5a358f499a", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988558ad635cab0211a365371c12547a1e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836160e90bdd70932b257673266f95e71", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98302f18ca681ebcc2cc643bbde279ab7e", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d5418ad8a63af499507360d09953631", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b1ece1dba89d90e95ef8358a0ba54fa", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985bea905fa06f712d6ce634edf672c658", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3e9167e95f3f29eabb1f4a5ab2c9f05", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989fda051e19937065a98d2aa7d5a7d6b6", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c45a5215c973fa3081993382a0ae5633", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831cf453e00b634ec91bfa1fa4742272e", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d7d8fb6d3e75c1937dc9191d0dddbf2", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980064c03926ea51a192368ebd223aaaba", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/AuthenticationChallengeResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9807705aebe299c63ce2e1e8e6d0942f9e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/AuthenticationChallengeResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9883bc00997f1de2aed937e35378e84732", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ErrorProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984deb501a9b7243d524c407aa9f6f2709", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FlutterAssetManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986c755872b0af2d116492eaa74f1a33ff", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FlutterViewFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9811936b5807f0f7d829c101dd8203a5d2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FrameInfoProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9857df5ea932ef4deb51ffb4c4707d0c64", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/GetTrustResultResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983f5cc96770b885a755fdabd81db40266", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/GetTrustResultResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981ad0af2e42bb4e8384deb50a7b3cbf43", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPCookieProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989329554c091cda0d0a7a919558c061be", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPCookieStoreProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989072b101272a134ef617c53a592b39e6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPURLResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ee6f007f6f3820b5490250819e5a6580", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationActionProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a8919381f10311a58bb89defe4dd9ef1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988cf2160502537294b0f3670ce58f5f1d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ae86217897d5b9bee910a28e59524b28", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NSObjectProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9834c1fb7049516b7da2ccbf05a39fee26", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/PreferencesProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983b710dd4602cb9b9bc325c99cd06ee1d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ProxyAPIRegistrar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9826e2d7a9fb6ac5b7ba84e82a5094882f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScriptMessageHandlerProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983b6255fe64b9e2a6724764ac52a41586", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScriptMessageProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989c440de891b125e7b3f3683bde7a4ecc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScrollViewDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9802c30adc82d9706e0dd90d5db9e58e26", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScrollViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980e7fa5567119a3e094231cde030360f2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecCertificateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9821572df553979126b5dd08dbbaf7fcdc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecTrustProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f94c01af108a2829677303dad84d4c9f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecurityOriginProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9830157cc992289a77e7eb5985c7c35980", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecWrappers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dcf0ad9a78d992f1e21c424dcb08c9bb", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/StructWrappers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c9b266a22a9ecbb663f2dc4f6f733797", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UIDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9863456232e00e9bd2c5c28f511b46e841", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UIViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986188f57e20e4a5d343e2263600b407f0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLAuthenticationChallengeProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9806f68ec655948f41fd9beb90474133f7", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLCredentialProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9883bdd4b80be1fba325a9df9a50cc9016", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLProtectionSpaceProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d160f8b42fbb6da642fb47796d00773e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d48f9dd78b23d8d273f8ce5000f59953", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLRequestProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985d9499ad09c7de7cdf37f8539093230f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UserContentControllerProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98146510e0b20f78de140f787870d2c982", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UserScriptProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984d9701c1cd5a6de367255960e6d6499e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebKitLibrary.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983100b6becb73d4a820376fdf4157bc3d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebpagePreferencesProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983528a9dd2188a1c8bb15a1863dcb44e4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebsiteDataStoreProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9862df27667264899a91d75fc9adfef1ed", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewConfigurationProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983ba472b72b35013cfcbed7883ddeeee2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewFlutterPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989525753acefdf071c338776533d7f57c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewFlutterWKWebViewExternalAPI.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b6f53b4d5a66337d60da3f855c546c88", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c868b285f614e60d2191c7a9e2f2dceb", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e2b9c12a1ab40b88b3a2d0fdeaf1909f", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98178719cd252aef849305f46d426ef80d", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983823d559b9be566579b4536e07f81898", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d8f7eac6cb0782821dd312df8a1d9ca", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5fc236aea5ff24bb10ab5a11ac2667d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98558bf34898fd345404e80b8e802e11ea", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f78e0c275e04cd11c914cee262e6cfc3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c63a8bb21b3f59709c7ac5f110714759", "name": "j17bank-mybank-app", "path": "j17bank-mybank-app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9840f0fccf046eb9fe5144995823feb987", "name": "Foton", "path": "Foton", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f54fe3a14a9e425842ed6405b8488ae0", "name": "Desktop", "path": "Desktop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb0bca113b87e84043201fb18bb63e39", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a615c9d47eb03672b3b4be8c2ec65cf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983436a1acdf816112066fb33cedd54ffa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985bd024b560bbb8fc25afecb8d5d25349", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ed72bfad81afdee960c6624f9f57c58", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a24ab42ccff6071a1a1e14c19853bc9f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9893693481a4b1327c62b438ba48090bf7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898bd1ca56e1a541b18022725058372ad", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e984f333b7be5f670fb5e427bf65a82471d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98de432e798896fee69b22501c578aa9cb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/darwin/webview_flutter_wkwebview.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9853dc98eea2f95f6dc1ad2c8713541f23", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98303cc23f325a1bd69bb79f2410a690b2", "path": "ResourceBundle-webview_flutter_wkwebview_privacy-webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9805230aa51b22ae819880d95f28116232", "path": "webview_flutter_wkwebview.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aeb14211e22ef2f7fdaf914b046f15bd", "path": "webview_flutter_wkwebview-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9896b88b20ebf28819318cea6308d9bdde", "path": "webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9ba0bad57668d3283a0a5771a88e360", "path": "webview_flutter_wkwebview-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825e0ffbc9a21df54801ef74279d14b95", "path": "webview_flutter_wkwebview-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98323f549f18bf1111253617388e4b5ea7", "path": "webview_flutter_wkwebview.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9812b97c8c86c119b65c79d0a3e31202f2", "path": "webview_flutter_wkwebview.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c40dd3d23861acc7a7af5c232eb6e514", "name": "Support Files", "path": "../../../../Pods/Target Support Files/webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980793722b741fa717857423e8bc379c31", "name": "webview_flutter_wkwebview", "path": "../.symlinks/plugins/webview_flutter_wkwebview/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2d0650dcd268f8342e19acace26b9b5", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e989ab1b98dcaecdab67652de081536af71", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVFoundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98e2b71cf372e6e9ae47666e20b9325b75", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreTelephony.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e985c2fe1b0f5d39d4da447f581c05596d1", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98fea329deb62d97ea5f5a28100303f484", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/QuartzCore.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9821c5a86bdaf5129f0bfbd51564017ddb", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Security.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98b5f5979f0786f4a5ad9848f0349b1159", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/SystemConfiguration.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98d391d713992f1d4e99af11b30a3d8fcd", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fd8003707fea3bda0b0e61281546e941", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818a9377e250b9d121296a9203d9c1d90", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98901de247e058ae2cd477259a3795a30b", "path": "CoreOnly/Sources/Firebase.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9867c0736aa2e74ff9eadde987093842ab", "name": "CoreOnly", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98264c23090a5b359fa2a6fc417a888de9", "path": "Firebase.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b5858b7562a18b07527f32abc4ef32f0", "path": "Firebase.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9891b793c00a7bc085a4571f92e04f2bde", "name": "Support Files", "path": "../Target Support Files/Firebase", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a6d4285508840710711697746002a66", "name": "Firebase", "path": "Firebase", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9876a09d7daa6dab54dca6af6f6b535a06", "path": "FirebaseABTesting/Sources/ABTConditionalUserPropertyController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ae15305d89ae742103a636059f5485f", "path": "FirebaseABTesting/Sources/ABTConditionalUserPropertyController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ac4846f9e37431bfc8641969e32dd91d", "path": "FirebaseABTesting/Sources/ABTConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c307b19a4613a0924084e1e995c345d", "path": "FirebaseABTesting/Sources/Private/ABTExperimentPayload.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809b10de9d9b8fd7c4c1e509beb10ea45", "path": "FirebaseABTesting/Sources/ABTExperimentPayload.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9843ed14d7f3bd623234bb954d7c43683e", "path": "Interop/Analytics/Public/FIRAnalyticsInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb42e1ffafd4bffff69e4c7064efeff1", "path": "Interop/Analytics/Public/FIRAnalyticsInteropListener.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983aece700dcac0432491d3eb7a7276252", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f57eefa89d1e7d4f4318f61c3362e7be", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cefaeca957f73b6654e305b0071285aa", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98daf715d3fee3ae434a415462c673c9df", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bfcfe52b0d612fd64cdbbaf615c160ab", "path": "FirebaseABTesting/Sources/Public/FirebaseABTesting/FirebaseABTesting.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c002eee6d6fdcd16512a3fa37c19fe87", "path": "FirebaseABTesting/Sources/Private/FirebaseABTestingInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983215689ed469a02c0f9eaeb42a3ea191", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6320b96a4480c757f52823644eb1bf3", "path": "FirebaseABTesting/Sources/Public/FirebaseABTesting/FIRExperimentController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d636d12a1e34ffef50457c4ee3a6715e", "path": "FirebaseABTesting/Sources/FIRExperimentController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a78a865a9b5a282e5ef368c3523d4e8", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d1ee6e1c860984d5cf4c834f850398c4", "path": "Interop/Analytics/Public/FIRInteropEventNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d7445442b752dd8b8f3a37768d27bc7", "path": "Interop/Analytics/Public/FIRInteropParameterNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9836e9a46bcede022792e80b70d501199b", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983f493c566af7162be3dc9fc898162482", "path": "FirebaseABTesting/Sources/Public/FirebaseABTesting/FIRLifecycleEvents.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98baa741dd7c96e212209c421796922a92", "path": "FirebaseABTesting/Sources/FIRLifecycleEvents.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9831d3f7d0e443088b888281f6fb701501", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98dfcfd80ec5a579c1d05776e7f25ac89d", "path": "FirebaseABTesting/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986026f425c7fc9b89a2793c9d30c5e4d2", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985b53d7234b13fba55cc43d4224a7d112", "path": "FirebaseABTesting.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9897bedf71f8c7501f02068a8175052f4a", "path": "FirebaseABTesting-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985fd6da498b1229048827f7b9914cc146", "path": "FirebaseABTesting-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982217fe37376ac664ac9b14b5f6d5b489", "path": "FirebaseABTesting-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98daf8f53d2c1a7645ea8d5096cb1d31d1", "path": "FirebaseABTesting.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ec57552e8e81a38b56c0ef82a807cf7e", "path": "FirebaseABTesting.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988351b89ae36872755f4949f8ec9ed8fa", "path": "ResourceBundle-FirebaseABTesting_Privacy-FirebaseABTesting-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98488198948bfc8e005ea0332c21b5f6e7", "name": "Support Files", "path": "../Target Support Files/FirebaseABTesting", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4d73f08d2900789a94a55887b5e9c12", "name": "FirebaseABTesting", "path": "FirebaseABTesting", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98fa538a32a009d15164e1458511ebb682", "path": "Frameworks/FirebaseAnalytics.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980dd82cc2ef1f93bc64b9463c17b301f4", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a278f7e7e85f0d4873b49cf73d199574", "name": "AdIdSupport", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98446d27e363202053faa0a8994d6f79d4", "path": "FirebaseAnalytics-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ff6ff462de91e20b105db98250427dca", "path": "FirebaseAnalytics.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982f868b277c96c59e078be9d0b9b982c2", "path": "FirebaseAnalytics.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9800972f11a071758957c21488be0a99bf", "name": "Support Files", "path": "../Target Support Files/FirebaseAnalytics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d7661923ade2ba4e21ab2ba5062a9478", "name": "FirebaseAnalytics", "path": "FirebaseAnalytics", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c8c794857d1ef1d672471452ff887c74", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98420a4ba3cc8565034f5a9c9378b171a7", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f997d94c6e28e2b55318bbc9de26ea60", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRApp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9864bf2f3efe36d0d720f69eb961244e0a", "path": "FirebaseCore/Sources/FIRApp.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888f9dcdc581a6b8b4bc922c6b096c576", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989261ae42a4f69978815d2951490cb444", "path": "FirebaseCore/Sources/FIRBundleUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98177b1a5074009d689e881961bddf72a5", "path": "FirebaseCore/Sources/FIRBundleUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9836a48b8541c6bf14078d94d77e85f732", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e42f59826a446012eff13a4c0762d485", "path": "FirebaseCore/Sources/FIRComponent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e7e97a3d6601edda1cdd0d5a87a941ca", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ac0b348c3a759fe75b3a144cb896b47", "path": "FirebaseCore/Sources/FIRComponentContainer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e665b049b80b3424234af1a90ff998ca", "path": "FirebaseCore/Sources/FIRComponentContainerInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98570d78cdaacea91aa893c89430d8f180", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9825d1ce14ba00b1ec15d158de761b2db0", "path": "FirebaseCore/Sources/FIRComponentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fbdeb87caa695f24f5d73a7d8afb4efc", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986ae48671c9384ef2eab1018e97488bd7", "path": "FirebaseCore/Sources/FIRConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7e7d378ae04511165a932ca3ebdd56e", "path": "FirebaseCore/Sources/FIRConfigurationInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e1a788b3ceafaa8d96521d4c975cbc0", "path": "FirebaseCore/Sources/Public/FirebaseCore/FirebaseCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982febb2daf33e1f053f04d58df5017850", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9a52e8c75c956cc3ce9d4969117d6ef", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9824669dc4c3ccd8d83e3be84ce2306d91", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9875b571d88b8f479a166721bed8b454b2", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9876161027be52214993edfcb4ba66d871", "path": "FirebaseCore/Sources/FIRHeartbeatLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c3dd76ed6fde8115a8a5d85230bceb32", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986fcf7ed7d3ab5b11ba6b2e0ff87d073f", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9875fc13efaacbfcc48b4d174ff631daae", "path": "FirebaseCore/Sources/FIRLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809bd087e95f909537809c9383317ce95", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRLoggerLevel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e6b8ab94012e49678ef3bbc03127229", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIROptions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981fb7f19f1aef3555edc1df459a134dda", "path": "FirebaseCore/Sources/FIROptions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec58f492a341217616e11f666cf38a7e", "path": "FirebaseCore/Sources/FIROptionsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98581cb37004ad680671a82ed8a58ce87e", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRTimestamp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98740a1b69fcb18b4a6116ff007abecc90", "path": "FirebaseCore/Sources/FIRTimestamp.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986c355cf364c1ba4def5e89f3d9c26f6b", "path": "FirebaseCore/Sources/FIRTimestampInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b9e6ee54370393281fdeaef79784ba8", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRVersion.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98643487c915c1b6fae72e445c11504de6", "path": "FirebaseCore/Sources/FIRVersion.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e986f8474fbc09f7203c982f433c6ec1efe", "path": "FirebaseCore/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9860f34a48201b0f48b51d7a83bca7752c", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c84871f73745b81b50a363c54695edfc", "path": "FirebaseCore.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985907e3386e060576a925b95afebe0f84", "path": "FirebaseCore-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98869c98954d114c80bcd5e5f60cbf5198", "path": "FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98db16b93e9a41afc1b8694c9c8d5bf54b", "path": "FirebaseCore-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982c46536179edec6c9353cbc2f6f2ef08", "path": "FirebaseCore.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9897a8927652f88d5f00464f5655e6207b", "path": "FirebaseCore.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d975888491d4d8bc488f03d9467c854b", "path": "ResourceBundle-FirebaseCore_Privacy-FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9885313aebd73a0e8c0d2cd8c284d64623", "name": "Support Files", "path": "../Target Support Files/FirebaseCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0f96e07da0a45f3c51f13e9900b89d9", "name": "FirebaseCore", "path": "FirebaseCore", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983f00e556785eec48931ebc917dd42230", "path": "FirebaseCore/Extension/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b298da9407d9707b15dd675c9c51791b", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d17eb04a278628e5ffbc2b49d009a6ac", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985f0f1d16dbd33d38dd4d6ed2c698c80a", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882302ac48a1be96e76501396ab8da138", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9b3f2c029ef227bad474fcd87101bf8", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b57414b155abbe8820d78c0063708772", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d0d98276d5a0369039bf0994e05d5df1", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98efbb0e3fd58386ce14a35deb122de9c2", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e984b5395d3627ce6c4993f679cdbdd61a9", "path": "FirebaseCore/Extension/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9856f73c678873cff8f61f0a4b2b4be806", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e987d3812360ebc6b794e6922c619bf4dcc", "path": "FirebaseCoreExtension.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ddfc606ae5dbdd77922b15a7426f0010", "path": "FirebaseCoreExtension-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b2cb3ae928786a89ce403b10513070b1", "path": "FirebaseCoreExtension-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983491328a90facdb85322a11bedfed59c", "path": "FirebaseCoreExtension-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98736b3ad19fde8b4ace0daf211f638b47", "path": "FirebaseCoreExtension-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985820d5f43556a92ccc067a8251225302", "path": "FirebaseCoreExtension.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9883d77655a5a0f1d0691ed3c8d47c148f", "path": "FirebaseCoreExtension.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98135da53acf28ae0965ba652e3863f1eb", "path": "ResourceBundle-FirebaseCoreExtension_Privacy-FirebaseCoreExtension-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9848035051e9bf00cb16d90ba8275130b0", "name": "Support Files", "path": "../Target Support Files/FirebaseCoreExtension", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877267213c7a48ba1a5af20f25e3a917c", "name": "FirebaseCoreExtension", "path": "FirebaseCoreExtension", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98273e54c266c7fa9fbb1a1004b37a6626", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e7f98b88d4d198ad488f0fa651a26102", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989b32f5fa70ca8ae96a9260f5f07d1284", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Heartbeat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d2b97aa9e6963efaf506b17a21d376ed", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98decc526647dc37bde1e6399a9c734a4e", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatLoggingTestUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d9b94fa7fd056a843f1ea21cf9ea3c59", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsBundle.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9829395e51bb5b48a9a184d011e55d5fb0", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dcb75e218704d824459f5cdc0f73acfa", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981a1385febeb594f49eade8175f9fb8f6", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/RingBuffer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9810ec3a96a4f50c3da278936056bd9dd8", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Storage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980928c331639fbaa8b463213105b7a0e1", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/StorageFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981b41a2813fd415433998c71849ba88aa", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/WeakContainer.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98683acbfd310154eeb295a3724bfe0c86", "path": "FirebaseCore/Internal/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c9f574f853b9a0f6c6d764f574276241", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9861d1869c6009db9d467f2ecb99b74baa", "path": "FirebaseCoreInternal.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98915b689c1607d8bde618ac61ee4df508", "path": "FirebaseCoreInternal-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98305fec2c2a82d56a3480d8b2ee682287", "path": "FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fab4d2240143c47b3934bf3d7719d3c4", "path": "FirebaseCoreInternal-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98182caa2d3d9727ffb35bf386c0b9f28d", "path": "FirebaseCoreInternal-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f8cd270f0e2929be0c8579ab08ac7358", "path": "FirebaseCoreInternal.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f0aca87a0c65c9739f51ab5b2590fd8e", "path": "FirebaseCoreInternal.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981fc315170ebf21d884b812f02389a325", "path": "ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f5cd1df866f127ea3dd89b1901497c8f", "name": "Support Files", "path": "../Target Support Files/FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd7ecb20035723ce9578ace46093e8b6", "name": "FirebaseCoreInternal", "path": "FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98bd426e0561a97b15fefbfee35acac255", "path": "Crashlytics/Protogen/nanopb/crashlytics.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987895635d68c38611d8443619827ca618", "path": "Crashlytics/Protogen/nanopb/crashlytics.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9818892747ab040883db2a2522d2d2979b", "path": "Crashlytics/Crashlytics/Rollouts/CrashlyticsRemoteConfigManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982c10cce9c40ab48079a09da8d01f1599", "path": "Crashlytics/third_party/libunwind/dwarf.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98151918c25d86c895e5ae3cf2535c1ac0", "path": "Crashlytics/Crashlytics/Rollouts/EncodedRolloutAssignment.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ebc1a2c20959e53771c8baa2b4641fce", "path": "Interop/Analytics/Public/FIRAnalyticsInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985cafecd4baad8735be4d70345df7e4e3", "path": "Interop/Analytics/Public/FIRAnalyticsInteropListener.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989c15e2efa9fe5c50d3392bb0728efa51", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b4efd0305a9ba001f4945290d5af21f5", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSAllocate.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d106a37c8c8d8b9cc3977e9c6ac1f0c3", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSAllocate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd272f6d6a9b64e79b29abf67d5366b7", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSAnalyticsManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98474d31b12e6c9ab24ebd529532e7322d", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSAnalyticsManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98017abae2c9964cb07d8925c4755f4740", "path": "Crashlytics/Crashlytics/Components/FIRCLSApplication.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ad22debefcdd73c57cb716ad043e9361", "path": "Crashlytics/Crashlytics/Components/FIRCLSApplication.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ac3380a7628a0fed4e72ca51316097a", "path": "Crashlytics/Crashlytics/Settings/Models/FIRCLSApplicationIdentifierModel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987e55db0bfa63d5b4e6efef7463870286", "path": "Crashlytics/Crashlytics/Settings/Models/FIRCLSApplicationIdentifierModel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989bebd4791dd27ae338754cd93fa89025", "path": "Crashlytics/Crashlytics/Operations/FIRCLSAsyncOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98048bb4de6d82caeb80433949241c8b76", "path": "Crashlytics/Crashlytics/Operations/FIRCLSAsyncOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fce2c8f0726b1ac244186bfde348d8ce", "path": "Crashlytics/Crashlytics/Operations/FIRCLSAsyncOperation_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9878b54bd693ba17d58b23fee6485349d8", "path": "Crashlytics/Crashlytics/Components/FIRCLSBinaryImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b1003fcde8ac3b63c6b3f65215e0d072", "path": "Crashlytics/Crashlytics/Components/FIRCLSBinaryImage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98510dd9bd686b314cb7e6f7cb13bbf236", "path": "Crashlytics/Shared/FIRCLSByteUtility.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f13d620c665645ae3a5be5a2bdc5464b", "path": "Crashlytics/Shared/FIRCLSByteUtility.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984a8c457ad35e546e74edfa33fba3bcb7", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSCallStackTree.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9896b3af95fc5abd1210c91f5e3557c4d9", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSCallStackTree.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981db5a84ee0e56c3d550c84f357135f0b", "path": "Crashlytics/Shared/FIRCLSMachO/FIRCLSCodeMapping.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9853a3abdb2af453148f75d10f089d2115", "path": "Crashlytics/Shared/FIRCLSMachO/FIRCLSCodeMapping.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9843edda598e4fd03955f681b006c55126", "path": "Crashlytics/Crashlytics/Unwind/Compact/FIRCLSCompactUnwind.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e28f6286e64ceab6229546406916550d", "path": "Crashlytics/Crashlytics/Unwind/Compact/FIRCLSCompactUnwind.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981d8603207355afb285d499f43d05820d", "path": "Crashlytics/Crashlytics/Unwind/Compact/FIRCLSCompactUnwind_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872c2475226edcdc6e90b35fd4fa4c044", "path": "Crashlytics/Shared/FIRCLSOperation/FIRCLSCompoundOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c8c0942ad5522c6366f93789e232691b", "path": "Crashlytics/Shared/FIRCLSOperation/FIRCLSCompoundOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98557e1738f91b8576f9de16852d382bc1", "path": "Crashlytics/Shared/FIRCLSConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f33c2bd786cf3c94e2bc1649ed123d48", "path": "Crashlytics/Shared/FIRCLSConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98610342cd1c9c7f45bad1478b211f752d", "path": "Crashlytics/Crashlytics/Components/FIRCLSContext.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984a2064b170b9642866cbbb8548fdf390", "path": "Crashlytics/Crashlytics/Components/FIRCLSContext.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987bf4740c0dbdfc2fd7b8d0723036e70c", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSContextInitData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987a4a1b541a7b0e571277134840a6a068", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSContextInitData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98afaff96437d94223283dd12aee84ade0", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSContextManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9879f60e9e916b098dbb1e3568b1587779", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSContextManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983191f37661b1be2ab2f0d17d2027997f", "path": "Crashlytics/Crashlytics/Components/FIRCLSCrashedMarkerFile.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984617d804cbde5a7bc6b394f3993f2b1e", "path": "Crashlytics/Crashlytics/Components/FIRCLSCrashedMarkerFile.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d60bdf71834383cc6827d17db51adb0", "path": "Crashlytics/Crashlytics/DataCollection/FIRCLSDataCollectionArbiter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dceb40c13e755a188a70a6429f201d9c", "path": "Crashlytics/Crashlytics/DataCollection/FIRCLSDataCollectionArbiter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de17d9ce47bc3324edcd0bb58291ac7a", "path": "Crashlytics/Crashlytics/DataCollection/FIRCLSDataCollectionToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9816c9d697be5a15bd1a8a6807273528b5", "path": "Crashlytics/Crashlytics/DataCollection/FIRCLSDataCollectionToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98045f9e1ad02c75c6ca4d8b5ccc5f43d2", "path": "Crashlytics/Crashlytics/Unwind/Dwarf/FIRCLSDataParsing.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986318c128b8f66a5a0ef702f3fe15d876", "path": "Crashlytics/Crashlytics/Unwind/Dwarf/FIRCLSDataParsing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab826ecf635410206e39ca2fffdb3895", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSDefines.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9806393f9824d3c240f61f66f4cfd68fdc", "path": "Crashlytics/Crashlytics/Operations/Symbolication/FIRCLSDemangleOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.objcpp", "guid": "bfdfe7dc352907fc980b868725387e98586725acec3374f63b1570e69b9ec985", "path": "Crashlytics/Crashlytics/Operations/Symbolication/FIRCLSDemangleOperation.mm", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989bea95e3699940b74c92f8a02d69c497", "path": "Crashlytics/Crashlytics/Settings/Operations/FIRCLSDownloadAndSaveSettingsOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c1cd8b87cde728f54c6cb085a0564a0e", "path": "Crashlytics/Crashlytics/Settings/Operations/FIRCLSDownloadAndSaveSettingsOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e58eda2727cfa8de438d9d0c9b1b36eb", "path": "Crashlytics/Shared/FIRCLSMachO/FIRCLSdSYM.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9879d71498a12d69eba772e27d769421ba", "path": "Crashlytics/Shared/FIRCLSMachO/FIRCLSdSYM.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9892ee2a7497a6c7c7e62fea96e6004a6d", "path": "Crashlytics/Crashlytics/Unwind/Dwarf/FIRCLSDwarfExpressionMachine.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989c6d4367eee38272e1e0da9611d6e71d", "path": "Crashlytics/Crashlytics/Unwind/Dwarf/FIRCLSDwarfExpressionMachine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d6ffe23f453e7d6c1011f0fde866f21e", "path": "Crashlytics/Crashlytics/Unwind/Dwarf/FIRCLSDwarfUnwind.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98df264a33918bff1cb3a013d222da1b3f", "path": "Crashlytics/Crashlytics/Unwind/Dwarf/FIRCLSDwarfUnwind.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98785ea40889236ae4c8d2ba0c90c1552e", "path": "Crashlytics/Crashlytics/Unwind/Dwarf/FIRCLSDwarfUnwindRegisters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b3cc5c9ec9e10cfe042a8e088956456d", "path": "Crashlytics/Crashlytics/Handlers/FIRCLSException.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.cpp.objcpp", "guid": "bfdfe7dc352907fc980b868725387e988e69674e083fc6a7073de0b544018feb", "path": "Crashlytics/Crashlytics/Handlers/FIRCLSException.mm", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982545153e311528915edabfaf4f80e200", "path": "Crashlytics/Crashlytics/Models/FIRCLSExecutionIdentifierModel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d01984f9bf758c53141fe00049aad7d7", "path": "Crashlytics/Crashlytics/Models/FIRCLSExecutionIdentifierModel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984829c77921b3b8688f67badf5099f9d9", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSExistingReportManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98961c7184b24212c6cf1baf8cf9745bce", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSExistingReportManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b4afd2a2cb894650047a95f4fa37459", "path": "Crashlytics/Crashlytics/Private/FIRCLSExistingReportManager_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e0a5df4326099f6ab2c578d659265a2", "path": "Crashlytics/Shared/FIRCLSOperation/FIRCLSFABAsyncOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820efaa445cebc46c11f546fe20c99418", "path": "Crashlytics/Shared/FIRCLSOperation/FIRCLSFABAsyncOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f0a9b389531a02eb348c4a51d46bf0d", "path": "Crashlytics/Shared/FIRCLSOperation/FIRCLSFABAsyncOperation_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bbf7d6a474a67218186af80706c7e125", "path": "Crashlytics/Shared/FIRCLSFABHost.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983311ff5517c96c7328830c84df1da41d", "path": "Crashlytics/Shared/FIRCLSFABHost.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e0187748d5193e9706d85393a5130c0c", "path": "Crashlytics/Shared/FIRCLSNetworking/FIRCLSFABNetworkClient.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d4652128332610bc2bfe0faaa5513224", "path": "Crashlytics/Shared/FIRCLSNetworking/FIRCLSFABNetworkClient.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9868206b045946b0b12edf9d31c086af94", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSFeatures.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e92dd967e0f7f4eb9ecfccc9fd988a2b", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSFile.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98be51849c9a163f501f568bb01f97e1ba", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSFile.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a1510033e8793a291fb9a8ce3be6253", "path": "Crashlytics/Crashlytics/Models/FIRCLSFileManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a0abccc2dddaea03e6cb6e9dc911d7be", "path": "Crashlytics/Crashlytics/Models/FIRCLSFileManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9884fffd964bc3e5d2c2327373fc951711", "path": "Crashlytics/Crashlytics/Components/FIRCLSGlobals.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ebf3eba04a84d8d6f0265afdd0302bd1", "path": "Crashlytics/Crashlytics/Handlers/FIRCLSHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c0661fa811172dab239909549d6ca46b", "path": "Crashlytics/Crashlytics/Handlers/FIRCLSHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980d0143575711b4ce3b2e86041e97a582", "path": "Crashlytics/Crashlytics/Components/FIRCLSHost.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f37cb5903028d44b3fc2242c95057ab", "path": "Crashlytics/Crashlytics/Components/FIRCLSHost.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98606801bb7e7eff5fefd77a45b8e0ca58", "path": "Crashlytics/Crashlytics/Models/FIRCLSInstallIdentifierModel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98072ebf97e22987719ce6b050510a46bc", "path": "Crashlytics/Crashlytics/Models/FIRCLSInstallIdentifierModel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f77ac8e677eaf8238dd4be42ba6de4ce", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSInternalLogging.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a40635d68447e64289027feaa50dfa02", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSInternalLogging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893c9dbf689abbe980d66dbdfaaed239c", "path": "Crashlytics/Crashlytics/Models/FIRCLSInternalReport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a6c90b13eb940ffeabee7751126c70a8", "path": "Crashlytics/Crashlytics/Models/FIRCLSInternalReport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9892534ad94c2ff49d263f8b4f97f180eb", "path": "Crashlytics/Crashlytics/Models/FIRCLSLaunchMarkerModel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9855381ef8815b33974a91b3f4e734d949", "path": "Crashlytics/Crashlytics/Models/FIRCLSLaunchMarkerModel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6cb5ea1e477685af90fc73a9b39a4f4", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987845e7aac07f6f2600b19529895cd48e", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a4c572bf63d84002a2269b3ce66d565f", "path": "Crashlytics/Crashlytics/Handlers/FIRCLSMachException.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8f739dd801445914877eb82614bead6", "path": "Crashlytics/Crashlytics/Handlers/FIRCLSMachException.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983eb5dc6ea5c6cc424ab64b923dbfa3b7", "path": "Crashlytics/Shared/FIRCLSMachO/FIRCLSMachO.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cae72d8d873480525c7b5c6a0700467c", "path": "Crashlytics/Shared/FIRCLSMachO/FIRCLSMachO.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830404a89146ffec88424fac70d3fa98f", "path": "Crashlytics/Shared/FIRCLSMachO/FIRCLSMachOBinary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af71e67310831e6b84ece7e0c0995cad", "path": "Crashlytics/Shared/FIRCLSMachO/FIRCLSMachOBinary.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0632540d44cf3b1a418dfb67eedbb4b", "path": "Crashlytics/Shared/FIRCLSMachO/FIRCLSMachOSlice.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f8e7fdea550a155e0204075e4ecf9784", "path": "Crashlytics/Shared/FIRCLSMachO/FIRCLSMachOSlice.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f188dc4b2d225dfc3d5d8cb02dad4e6a", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSManagerData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9865386d5e3ebc68b9ce2fe17449950621", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSManagerData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c624ad89047896c0ac2ce248356147b", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSMetricKitManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b0c17def3bf466c849bcc43f5b891117", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSMetricKitManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b26d3cb76c1fbf11ac32270c818e7138", "path": "Crashlytics/Crashlytics/Settings/Operations/FIRCLSNetworkOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982d2d5642855626d375c7d949114e1e1b", "path": "Crashlytics/Crashlytics/Settings/Operations/FIRCLSNetworkOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c3db7adf84d2fe4d81af690c6e91e85a", "path": "Crashlytics/Shared/FIRCLSNetworking/FIRCLSNetworkResponseHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983f22a0e110ac49dd5174657099b5d9b3", "path": "Crashlytics/Shared/FIRCLSNetworking/FIRCLSNetworkResponseHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809ce03640fc14ea9ba97d68695095e06", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSNotificationManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bdf4548393f6506d1e9e2d3ca417e47a", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSNotificationManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986801202b3410b0b338bf33bdb8901235", "path": "Crashlytics/Crashlytics/Models/FIRCLSOnDemandModel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9880c22ba8182c1b3d4b84ebae7cde5675", "path": "Crashlytics/Crashlytics/Models/FIRCLSOnDemandModel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988da80766b0a82f90263c38e878742290", "path": "Crashlytics/Crashlytics/Private/FIRCLSOnDemandModel_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b8c0f5d475e8e02314ff45ac76efed5f", "path": "Crashlytics/Shared/FIRCLSOperation/FIRCLSOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98aba520f13d80bcc9e257c1d8a0a1b3c9", "path": "Crashlytics/Crashlytics/Components/FIRCLSProcess.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f1b3e3e32537833a2a8ed46e4d77dc00", "path": "Crashlytics/Crashlytics/Components/FIRCLSProcess.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed34e8c87a012a266526342d303a2e08", "path": "Crashlytics/Crashlytics/Operations/Reports/FIRCLSProcessReportOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982e4f22ffa7d20f65129bf2bc59789e65", "path": "Crashlytics/Crashlytics/Operations/Reports/FIRCLSProcessReportOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859efc518d8dd023b9c36ae5ccc52656e", "path": "Crashlytics/Crashlytics/Models/Record/FIRCLSRecordApplication.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987a339020e32e1c2e55eff412a288e26f", "path": "Crashlytics/Crashlytics/Models/Record/FIRCLSRecordApplication.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9841beb33feac9a3ab39d0f05ff6716056", "path": "Crashlytics/Crashlytics/Models/Record/FIRCLSRecordBase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98789a65e7583ab5393968d88b766ed767", "path": "Crashlytics/Crashlytics/Models/Record/FIRCLSRecordBase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9815e70d919974d10805bb7561c1b9656e", "path": "Crashlytics/Crashlytics/Models/Record/FIRCLSRecordHost.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9839b01554590356bec29800aeac1a4c99", "path": "Crashlytics/Crashlytics/Models/Record/FIRCLSRecordHost.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9839e5f2162535279103ba8afdaaebb1ec", "path": "Crashlytics/Crashlytics/Models/Record/FIRCLSRecordIdentity.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9858624847a13141f02b8f90410cff6c45", "path": "Crashlytics/Crashlytics/Models/Record/FIRCLSRecordIdentity.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ad65b23cf9fe7f5abdf401334b37e44", "path": "Crashlytics/Crashlytics/Models/Record/FIRCLSReportAdapter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985797731cbcfb623f1392ee3c8a62cb15", "path": "Crashlytics/Crashlytics/Models/Record/FIRCLSReportAdapter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9863b84f896bc8c2d2ffc22af1a63a00c1", "path": "Crashlytics/Crashlytics/Models/Record/FIRCLSReportAdapter_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6c2fc48ec690517c3c09054f0580251", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSReportManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cc9b45a286761a5900ea93eb771dd59e", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSReportManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9822584c632535733bb8dc2efdd6e800e1", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSReportManager_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d84a29ed8b08717dd6b9d676f835e0d", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSReportUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d60cce5da2ecb435f559a3fadfa4f682", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSReportUploader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de9614976d2d435c8ec91f2283550f11", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSReportUploader_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9810d1201c7fda19219d151b701f86b0e7", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSRolloutsPersistenceManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af8d49b04a85db78f41faabe17f15104", "path": "Crashlytics/Crashlytics/Controllers/FIRCLSRolloutsPersistenceManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98833384ab5a18364067570a78a9dbda64", "path": "Crashlytics/Crashlytics/Operations/Symbolication/FIRCLSSerializeSymbolicatedFramesOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9857c4273fe4e3b39a94fcc46b536183ef", "path": "Crashlytics/Crashlytics/Operations/Symbolication/FIRCLSSerializeSymbolicatedFramesOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986673caed021b3ba9e1ca25794481291d", "path": "Crashlytics/Crashlytics/Models/FIRCLSSettings.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a743c788753018861f218553becfb422", "path": "Crashlytics/Crashlytics/Models/FIRCLSSettings.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee04cd7071abdfc99a61c6d23c9b302b", "path": "Crashlytics/Crashlytics/Settings/FIRCLSSettingsManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983186f609d407f27932ae5a6f3c5d4e46", "path": "Crashlytics/Crashlytics/Settings/FIRCLSSettingsManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ad9028707e85c8920fbd40dc536bd3a4", "path": "Crashlytics/Crashlytics/Handlers/FIRCLSSignal.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840cc11477a83705869d8683630056fdb", "path": "Crashlytics/Crashlytics/Handlers/FIRCLSSignal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980dd3bbf4425743f8c723d50e0254988e", "path": "Crashlytics/Crashlytics/Operations/Symbolication/FIRCLSSymbolicationOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ceec2216d1e2e314d509b5db8c3f0b41", "path": "Crashlytics/Crashlytics/Operations/Symbolication/FIRCLSSymbolicationOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a6f12529a50a92fd8865821eb5756566", "path": "Crashlytics/Crashlytics/Models/FIRCLSSymbolResolver.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9861d99e8da17a5bf7b03b03fd7dae3413", "path": "Crashlytics/Crashlytics/Models/FIRCLSSymbolResolver.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9811692f2fbd829b5819deec986fd48e62", "path": "Crashlytics/Crashlytics/Operations/Symbolication/FIRCLSThreadArrayOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98684bd0b0890c0c70b7de98affaf3e13f", "path": "Crashlytics/Crashlytics/Operations/Symbolication/FIRCLSThreadArrayOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982543ab8ffdcb64cf8dd159f6d27bdf5d", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSThreadState.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b80c56fc9d19532319dacbb59cf5bbf2", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSThreadState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d13fdcff9e9aef4119741707cfd339da", "path": "Crashlytics/Crashlytics/Unwind/FIRCLSUnwind.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855d559dc6cda55f3b6b855ffbe45fddd", "path": "Crashlytics/Crashlytics/Unwind/FIRCLSUnwind.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9864b8ee43894e56dd4ae17a09dce2b5aa", "path": "Crashlytics/Crashlytics/Unwind/FIRCLSUnwind_arch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98912876f112ad50a75fa406a273943912", "path": "Crashlytics/Crashlytics/Unwind/FIRCLSUnwind_arm.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981eb787ed48e6928332d57de4ffc22e37", "path": "Crashlytics/Crashlytics/Unwind/FIRCLSUnwind_x86.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b532f4f0b280ada1a7625504381f044", "path": "Crashlytics/Crashlytics/Unwind/FIRCLSUnwind_x86.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98511807474aa226e73aa64831d412376d", "path": "Crashlytics/Shared/FIRCLSNetworking/FIRCLSURLBuilder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d21a163202480e7cd216f276d21838a4", "path": "Crashlytics/Shared/FIRCLSNetworking/FIRCLSURLBuilder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9880fc8b3b277f48b5ae019382127a3d63", "path": "Crashlytics/Crashlytics/FIRCLSUserDefaults/FIRCLSUserDefaults.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989398b3ff61a8fdcd8f89cd0ebca4fbe3", "path": "Crashlytics/Crashlytics/FIRCLSUserDefaults/FIRCLSUserDefaults.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986754b306202511fd778261403f1db9fe", "path": "Crashlytics/Crashlytics/FIRCLSUserDefaults/FIRCLSUserDefaults_private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989eff39412b464f5cb9ec497e921cd72c", "path": "Crashlytics/Crashlytics/Components/FIRCLSUserLogging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca23b1ee260e30cb7fc6c2e185b1dea0", "path": "Crashlytics/Crashlytics/Components/FIRCLSUserLogging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985fc12dcab22d8e7d62b59a8c4958b59f", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSUtility.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dcaa9648bfb15e84fa735d6e2f90643a", "path": "Crashlytics/Crashlytics/Helpers/FIRCLSUtility.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ef31756f1fefc4f185dcf1b05900ef2", "path": "Crashlytics/Shared/FIRCLSUUID.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984cd49fbe968526669421091973e0ac33", "path": "Crashlytics/Shared/FIRCLSUUID.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b4085ce9fe890ed323c549e986e8fbbf", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9845f375a130ad3f56cac97498f7db8ba3", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9824a5e7eea032a08fd22776285772a301", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98278c89337a875f2f75c6c4340e155e12", "path": "Crashlytics/Crashlytics/Public/FirebaseCrashlytics/FIRCrashlytics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f6aed6f3212900432c9bf7ed0e1a3884", "path": "Crashlytics/Crashlytics/FIRCrashlytics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98201ca7801c9bfd845b42e6d606c51037", "path": "Crashlytics/Crashlytics/Public/FirebaseCrashlytics/FIRCrashlyticsReport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c56ab52484ba978251f2bcc9c0d3dac4", "path": "Crashlytics/Crashlytics/FIRCrashlyticsReport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c11b48bf325ba0ce82fddfa5cbff0351", "path": "Crashlytics/Crashlytics/Private/FIRCrashlyticsReport_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98639f6da0e2ddb7732c580fd4e3e32b5f", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5836c436d6e70f6a0be6b985a149c6b", "path": "Crashlytics/Crashlytics/Public/FirebaseCrashlytics/FirebaseCrashlytics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f229d3a6891737c4668a1a24ea1196e1", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9847265399bd74eb8a6ec4be2c70dbfacb", "path": "Crashlytics/Crashlytics/Public/FirebaseCrashlytics/FIRExceptionModel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98220c9f67f5d3b798e722468b951306a8", "path": "Crashlytics/Crashlytics/FIRExceptionModel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f5558a798945fb2e0a4b1e0d66b2f3c", "path": "Crashlytics/Crashlytics/Private/FIRExceptionModel_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b0316e28a4a01989d1c697492f4bfdf", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e25423da8f92c47e85dd86079fa326e2", "path": "Interop/Analytics/Public/FIRInteropEventNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9899e1dbbec2b1cd1bdb84fd29e8351534", "path": "Interop/Analytics/Public/FIRInteropParameterNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f256ee1e6ea9fdca4366dd67a112464", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849a601c3e000f66260dbd7a21277e9b1", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98779297dd707077996c21518f7d42206f", "path": "Crashlytics/Crashlytics/Public/FirebaseCrashlytics/FIRStackFrame.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9839551a8ee99d540018d0ca1397954402", "path": "Crashlytics/Crashlytics/FIRStackFrame.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986011ccc4a5f7bcff10409132cc0d19b0", "path": "Crashlytics/Crashlytics/Private/FIRStackFrame_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9882220df656220dbcb5fd4cc9ddd03da7", "path": "Crashlytics/Crashlytics/Rollouts/StringToHexConverter.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9870e6045cb57edea490e92c3365c36d6d", "path": "Crashlytics/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98084f4e4c427b518887f1aed8a51f55b7", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9820e905a2a44733678185d519f5f85033", "path": "FirebaseCrashlytics.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9828648ab1743c280899f15f748e432830", "path": "FirebaseCrashlytics-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982f15a5393f4befceda54285f84480886", "path": "FirebaseCrashlytics-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98310e76bcb510155c20dd2b502c1f8119", "path": "FirebaseCrashlytics-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ce91ab76c8964031655c72de4de6b5ad", "path": "FirebaseCrashlytics.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c37fd367dd60f3d4254b804ee18d4cef", "path": "FirebaseCrashlytics.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9819e250f1bd85623b129675e857d57aab", "path": "ResourceBundle-FirebaseCrashlytics_Privacy-FirebaseCrashlytics-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f961b952782b0ac15fa12ae5c34767d1", "name": "Support Files", "path": "../Target Support Files/FirebaseCrashlytics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1de2ee1592831c10603108c2b9d1e74", "name": "FirebaseCrashlytics", "path": "FirebaseCrashlytics", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a7445b79b154f36d6ff3bd9fd3cb804", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982288025603d8ac9fc2160897f1f717e2", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98243497ce4cea0d223c77bb6df9437902", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b180166d237f53256778ed0d7ebdf832", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9801f2875ed4129444a64a015d9b2874b4", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d21d6883bf5effee67025fc5c3943aa4", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRCurrentDateProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d4d8955ef417a8581cc9feae421f5265", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ba6a8e74dddfbd079b43d9f7b19ccc8", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FirebaseInstallations.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c16dbfa1e046d5d3e66f98c62dbde06", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba9579e38d39d8b91ecbdf0a7bcd1cef", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819afb6caa25f20214e186cbd310838a8", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallations.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98544f38cf9fa7d344b557fa187b03ea66", "path": "FirebaseInstallations/Source/Library/FIRInstallations.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b68d067ebcb90127bf7ad88cae073fd", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98909827dd04f66f02d3776d030e4fb22c", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef24a63c8ddebf7133984535490327cd", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallationsAuthTokenResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f36be956dae0a2cb181bdccb9df1f375", "path": "FirebaseInstallations/Source/Library/FIRInstallationsAuthTokenResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de3d432273e2678f35ff5eebb94e9db0", "path": "FirebaseInstallations/Source/Library/FIRInstallationsAuthTokenResultInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1d30c41ccd1c348d10ccf4b09914705", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsBackoffController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bfb11333560d60378dee61c139cc73e9", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsBackoffController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98897d592c9701195d1dc8bfeb80055f1d", "path": "FirebaseInstallations/Source/Library/Public/FirebaseInstallations/FIRInstallationsErrors.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9866dacc0c4eba3869c3901f64a130da6c", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsErrorUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985be740084209e650e57c408f9e491798", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsErrorUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c978145ff5e043da462f1fc0cf4534c9", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsHTTPError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982f1d73a9da85a972baf9d3e0e6f13182", "path": "FirebaseInstallations/Source/Library/Errors/FIRInstallationsHTTPError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840d8f9622bd0d4696b6c385dcd489eba", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsIDController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fe8eeb01e33cd5bd2f2729195d20f2cb", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsIDController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98243d732f1ac2d2e99fe92377453727c0", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986aab681a627f1cb1dca59b4a67b6ed04", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9813fd9c79766396935734ebd557db6fbc", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDTokenStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fccc64957d3ba066c42e83ae5069ff75", "path": "FirebaseInstallations/Source/Library/IIDMigration/FIRInstallationsIIDTokenStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d85c58b19970a4323a57acb2914363b7", "path": "FirebaseInstallations/Source/Library/FIRInstallationsItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98247e981792aa211435f03bd1cda1af4a", "path": "FirebaseInstallations/Source/Library/FIRInstallationsItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982c65bca1e71c28d7ea71a845d201de2c", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsItem+RegisterInstallationAPI.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981c4b9fcd55088806c4faeee781173b24", "path": "FirebaseInstallations/Source/Library/InstallationsAPI/FIRInstallationsItem+RegisterInstallationAPI.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cccd1e0cb5c667200f4e901517a3cc00", "path": "FirebaseInstallations/Source/Library/FIRInstallationsLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981bd93e7707bb98d7d6e2bb5a81570ed7", "path": "FirebaseInstallations/Source/Library/FIRInstallationsLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9878fdde36d0f9db4518534d419d3a17cb", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsSingleOperationPromiseCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f18db33b98a36b3df3921a9d2f29cb6", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsSingleOperationPromiseCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828ed04240b98d07d0ea6188d92788dfe", "path": "FirebaseInstallations/Source/Library/InstallationsIDController/FIRInstallationsStatus.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a4f942c812c651baa1ee441fc650026", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98be15d07dfee5e0fc9e026b9bfd31e01d", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98045b10f792aebee894800cf997c1fb15", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredAuthToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9889ef34bb9e7fb79b6d8963f6df995ad4", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredAuthToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980825f46beb8f92fba4ac5c61a25aa44a", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b50dbda44902727e5b55b41c289a987c", "path": "FirebaseInstallations/Source/Library/InstallationsStore/FIRInstallationsStoredItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da1ee23428fb4a59e9fc9c77a9ec62cd", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830b5c750f2f834f9a6048abfc3420319", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9862ad904e9b8e5698c279955866bf924f", "path": "FirebaseInstallations/Source/Library/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e9db2e0f2695d0cb08abc7a48f03bbbe", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c68d1e6495923cc488977497a249e2c2", "path": "FirebaseInstallations.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984b27664425251e8cc1f343cbbf28a376", "path": "FirebaseInstallations-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98144461598c5d0873f218830aa1eadaea", "path": "FirebaseInstallations-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce475a3ca8babd507c644b7413470a1f", "path": "FirebaseInstallations-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988db7ba5df968aadd963dc50c2618caf2", "path": "FirebaseInstallations.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98552f6965b4d3abd0715c9322daaf3684", "path": "FirebaseInstallations.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989f065937d05f1c199e1f90c7e7f75161", "path": "ResourceBundle-FirebaseInstallations_Privacy-FirebaseInstallations-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986befc6e3beeebb876f17bd1195e334cb", "name": "Support Files", "path": "../Target Support Files/FirebaseInstallations", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4589d1d13d0dc421ac701f465480b30", "name": "FirebaseInstallations", "path": "FirebaseInstallations", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9e5cbe6e7a245609659ca5544889814", "path": "Interop/Analytics/Public/FIRAnalyticsInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1ae1fdb64275271839581ef94b07865", "path": "Interop/Analytics/Public/FIRAnalyticsInteropListener.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1e7b28b1baa8b1789a6f037b2af7d18", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805be441c253fa6ef87a3df6547aff775", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834295e98caa4e78cb5f6c2f1c9215037", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985eb02d135302239c09f22077fc8a56ef", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893e07503942eab529db00cdc5d8b19aa", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9879b9818192d23282eb2ee071ef503e69", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9806952309ca0e414dc83b2830db412481", "path": "FirebaseMessaging/Sources/FirebaseMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c43ddb7dc6aabbe011d66354a73cb4fd", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FirebaseMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883122c1f7d0468fa5c1bb76eb4890d06", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aeec041e6e9608073d210643ec1a833b", "path": "Interop/Analytics/Public/FIRInteropEventNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e405f1a2fde239233c5cd928d5a8678", "path": "Interop/Analytics/Public/FIRInteropParameterNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98558285f897da2d9162ce80a9633eec90", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9800c382e669980055d0aba103e4ec5d0f", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9890a2b040c02093b6872b8652cc1b22d4", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9857a73434c1975f56abe59cd3868ad3a2", "path": "FirebaseMessaging/Sources/FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d524ff001ff623dcabfe7b29d389712", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessaging+ExtensionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982077a4bf04d45ad99afc67c6718ba3b4", "path": "FirebaseMessaging/Sources/FIRMessaging+ExtensionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989ab0eb25ed2f07d167279bbabfcebeac", "path": "FirebaseMessaging/Sources/FIRMessaging_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9850b5fca406794ae8a3f6a78aba36871b", "path": "FirebaseMessaging/Sources/FIRMessagingAnalytics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9866d012f5c000379284fbb8690070c863", "path": "FirebaseMessaging/Sources/FIRMessagingAnalytics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a49d3a51760b59d5150719de842f8ca", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAPNSInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981b4aba722570827c9cf33ebeaa1315a1", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAPNSInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1201342f4b4cc7bc49589b92bbaf2a2", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthKeychain.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c8cf110cd58ed6f472a5eed53d1690c", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthKeychain.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805debdf17e6b607370b1da903acce092", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb3a113bd8115e5a542471287d06503e", "path": "FirebaseMessaging/Sources/Token/FIRMessagingAuthService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b16ed53df98e38ce44aec29e340d9d9e", "path": "FirebaseMessaging/Sources/Token/FIRMessagingBackupExcludedPlist.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cef114f70fd941b1d7e0c2873a6e4682", "path": "FirebaseMessaging/Sources/Token/FIRMessagingBackupExcludedPlist.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98acb69528a03f707ba3d765e112b15924", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinPreferences.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a5446f3cb01b7070f140f0ce73c5ccee", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinPreferences.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe11243a9827966227fea49be0e1ee8b", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804939d721512e40ff31340904f77b8f2", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d08db289f80942715eb320da76dc9f0", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98993bf1ad987f0f51b9075154756d2467", "path": "FirebaseMessaging/Sources/Token/FIRMessagingCheckinStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987285f208246c4eff5f2373512e48ee8c", "path": "FirebaseMessaging/Sources/FIRMessagingCode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985331d158fcc9b126d5f3537187765e67", "path": "FirebaseMessaging/Sources/FIRMessagingConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98170d2de0f5e5ee84907a83c4c08a0bac", "path": "FirebaseMessaging/Sources/FIRMessagingConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eda91e3ddc93c524a8ae7478426555ed", "path": "FirebaseMessaging/Sources/FIRMessagingContextManagerService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832dcce5bc855180a78825e38caa4a5c3", "path": "FirebaseMessaging/Sources/FIRMessagingContextManagerService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f39a0999b81f527f2f2c1a6534ca3ade", "path": "FirebaseMessaging/Sources/FIRMessagingDefines.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982027b94e1c9b4307fd7e6a36f5ed3b4d", "path": "FirebaseMessaging/Sources/Public/FirebaseMessaging/FIRMessagingExtensionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982490fa0fdfe8c346e057b194879e9eee", "path": "FirebaseMessaging/Sources/FIRMessagingExtensionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98360a1cc4d0596e9e08174e954e95dd93", "path": "FirebaseMessaging/Interop/FIRMessagingInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9b48a50f08193ed9077036d3510b2e7", "path": "FirebaseMessaging/Sources/Token/FIRMessagingKeychain.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98836ea417ac71e99225f21813aa8de7d3", "path": "FirebaseMessaging/Sources/Token/FIRMessagingKeychain.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809ccc1ddf36ddb0d96bb4eaabda62c05", "path": "FirebaseMessaging/Sources/FIRMessagingLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9811ef6192938189962c331e590eae7221", "path": "FirebaseMessaging/Sources/FIRMessagingLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984550486768e9781f8675524bfa6bbdc0", "path": "FirebaseMessaging/Sources/FIRMessagingPendingTopicsList.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fecc63ecf7e73ead0de8b778f23ac88b", "path": "FirebaseMessaging/Sources/FIRMessagingPendingTopicsList.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982657bc6cb82499dcc7ed1d097e36fee9", "path": "FirebaseMessaging/Sources/FIRMessagingPersistentSyncMessage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985837ca1d012512d965b423fbbdc6fdfc", "path": "FirebaseMessaging/Sources/FIRMessagingPersistentSyncMessage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e4f89ffbd02c39562c6110a12e9839e", "path": "FirebaseMessaging/Sources/FIRMessagingPubSub.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a84c59a37ee32a5b6842d23ff809c97", "path": "FirebaseMessaging/Sources/FIRMessagingPubSub.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98044f576333f79393db19dad150a5dc2e", "path": "FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98174e5f8fedf3ee788a7a275dbdd6e6b0", "path": "FirebaseMessaging/Sources/FIRMessagingRemoteNotificationsProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a00e4ae67a99ed2037467992c6535c4", "path": "FirebaseMessaging/Sources/FIRMessagingRmqManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b50aac2edcb0a8c5c358620d2cc4bed6", "path": "FirebaseMessaging/Sources/FIRMessagingRmqManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826a0edc80af17c163c5fd9199884046a", "path": "FirebaseMessaging/Sources/FIRMessagingSyncMessageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fc327aa9dbf6fefba15108847f34acd4", "path": "FirebaseMessaging/Sources/FIRMessagingSyncMessageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9884a7627211748ff9a54d4dfe31de6427", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenDeleteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988af9203febe506f93dd125d2774393ef", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenDeleteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a04c5d0de90f45df07b3c43d978e9c61", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenFetchOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e15d5e156e28ba1f0f81a26591f81265", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenFetchOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ac452b5e22d751d773299601a795e484", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e9de616181608edc0dd45d45afcd8abc", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e279a3355b1bd79572de3a547cb5b219", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dbcc073fd58f24d833af8eeac06786ee", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982869a8a0b6aa374a254980e69a64f7f8", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9834b0eb060ef101f55c243d034cc11471", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e59da75323ff5bfdc0562a666da0c79f", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ccd29f085b9629801cf7ae59f539526", "path": "FirebaseMessaging/Sources/Token/FIRMessagingTokenStore.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9800d6c739d1f0a3f96fabbc6d074f5899", "path": "FirebaseMessaging/Sources/FIRMessagingTopicOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98161ff68a3c58e4ebff59cea3e3100b33", "path": "FirebaseMessaging/Sources/FIRMessagingTopicOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986c605732c51e877540ad071f2cfb54d8", "path": "FirebaseMessaging/Sources/FIRMessagingTopicsCommon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98485284dcca9a6dcb203e86573de57bb6", "path": "FirebaseMessaging/Sources/FIRMessagingUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984f5fe8abe0f430e98849509d154b9c39", "path": "FirebaseMessaging/Sources/FIRMessagingUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9857eb190bccaf793e55ed0601e16b2624", "path": "FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98702b860afce30249ccce6cc41feeb1d4", "path": "FirebaseMessaging/Sources/Protogen/nanopb/me.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983fe6fe18b3f36e0ed7b92039f90c5ddd", "path": "FirebaseMessaging/Sources/NSDictionary+FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9816fcdfdd6d1f8a246f88081490c66aba", "path": "FirebaseMessaging/Sources/NSDictionary+FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986473571f990f19a1195649ec2239e91f", "path": "FirebaseMessaging/Sources/NSError+FIRMessaging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f76cf77cce6e004635ac26f42569ca18", "path": "FirebaseMessaging/Sources/NSError+FIRMessaging.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98ef24ce97a5e8ebebfcaa8fd2237313ed", "path": "FirebaseMessaging/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9838c1c9c617562a852df7159eb08e9c80", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986e7954bb46d198011be78470f63c7008", "path": "FirebaseMessaging.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988f69782af0c40827d43d5b866de08d72", "path": "FirebaseMessaging-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98852a1f53db7f099110f72329a4e7cfd3", "path": "FirebaseMessaging-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98095356abd20f7cf1121d41aa72bc9209", "path": "FirebaseMessaging-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9831758fff2186bce10a1cbdb3ffa00b92", "path": "FirebaseMessaging.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98622923acd35caf79ea312941202db5f4", "path": "FirebaseMessaging.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989394cce7be341ca2f8c8e0052a0fb7dd", "path": "ResourceBundle-FirebaseMessaging_Privacy-FirebaseMessaging-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9871636301a184251e8a2e7d178b3bdf7f", "name": "Support Files", "path": "../Target Support Files/FirebaseMessaging", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c682ba3326657e6d67013607481f96e4", "name": "FirebaseMessaging", "path": "FirebaseMessaging", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af0ae7a78e05d13fe4f3b81dd1b593a9", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9801c26e41fd868c9616368fdc503ed8c8", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98466063bf6a86e1d1c06e1113b7c78504", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e1275a520a4647f5bb2407033113212", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989cbefaba3e1b6c6de0d0b0ba3b8d4e04", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98920c50391ea1202c31197a5b8e7e0115", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987d3f36002e18894868289096d441a11b", "path": "FirebasePerformance/Sources/Public/FirebasePerformance/FirebasePerformance.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981d7e102a56f04dab0a161c75bf7a6b88", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f0dcd336b56719c043dc9bf0ddc866f", "path": "FirebasePerformance/Sources/Public/FirebasePerformance/FIRHTTPMetric.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987d1c8c3e6f12909ffa9c319ef979ca4b", "path": "FirebasePerformance/Sources/Instrumentation/FIRHTTPMetric.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984830d8393549ba2057d76e6579eea9a4", "path": "FirebasePerformance/Sources/Instrumentation/FIRHTTPMetric+Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982719cc1fd504425161ceb17789553f1d", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dcba20095643f192bc94d85ead3dc5e1", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bdb8095795bbef3cc6a6819eafc496e3", "path": "FirebasePerformance/Sources/Public/FirebasePerformance/FIRPerformance.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984f83c886b90ed71efcbd1bc06f522e8d", "path": "FirebasePerformance/Sources/FIRPerformance.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980959a36979f57dfc35601b2e4ebb97a5", "path": "FirebasePerformance/Sources/FIRPerformance+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b5bf252ac9dce74ee73d750d1813adc", "path": "FirebasePerformance/Sources/FIRPerformance_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9d00ff170d94de96589efacfcf4582a", "path": "FirebasePerformance/Sources/Public/FirebasePerformance/FIRPerformanceAttributable.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b6c7b09eea796aac95d44c7cf63723c", "path": "FirebaseRemoteConfig/Sources/Private/FIRRemoteConfig_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ddf5eacb973c3d2dd9a0257999b182b", "path": "FirebasePerformance/Sources/Public/FirebasePerformance/FIRTrace.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fac6416bc850fb4670633f0d1ac07645", "path": "FirebasePerformance/Sources/Timer/FIRTrace.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98284a2afbe458608e921e7410f0e86a4d", "path": "FirebasePerformance/Sources/Timer/FIRTrace+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c71724f20a178b2e0d1235c8f052828f", "path": "FirebasePerformance/Sources/Timer/FIRTrace+Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5865e43ed0dae833a1ee7dbfdce2976", "path": "FirebasePerformance/Sources/AppActivity/FPRAppActivityTracker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989b3fd4a8021430e632969f2b8069a31d", "path": "FirebasePerformance/Sources/AppActivity/FPRAppActivityTracker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982dd96580de589ccd9d0d9e56a051ac6e", "path": "FirebasePerformance/Sources/Instrumentation/FPRClassInstrumentor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ecb710c554bc1013a45eb0bbe791f3ea", "path": "FirebasePerformance/Sources/Instrumentation/FPRClassInstrumentor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e5ad7a01be751e1b0054ea1553ab840", "path": "FirebasePerformance/Sources/Instrumentation/FPRClassInstrumentor_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b08ca68dc15473883790aa6b74caf920", "path": "FirebasePerformance/Sources/FPRClient.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982dcde31bb1f7e9b7b39d901c510edd06", "path": "FirebasePerformance/Sources/FPRClient.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98755857d7e9b9378612e0d2b105de2ea5", "path": "FirebasePerformance/Sources/FPRClient+Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b616a89576554b7451db99b7a68486b6", "path": "FirebasePerformance/Sources/FPRConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982248707ce0ad9e56e83104bc7a206cb9", "path": "FirebasePerformance/Sources/FPRConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98218ff0f81311bfc739be727ad4594396", "path": "FirebasePerformance/Sources/Configurations/FPRConfigurations.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98971ecd87f7a8cb44ce6a66eff84d0edc", "path": "FirebasePerformance/Sources/Configurations/FPRConfigurations.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a13b087759dde8ee40bb91d24290514", "path": "FirebasePerformance/Sources/Configurations/FPRConfigurations+Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eceb32f0840ac9efc63b1c769fa1ce85", "path": "FirebasePerformance/Sources/FPRConsoleLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982bb1ecd42ee6b00fe411d7e163c86b48", "path": "FirebasePerformance/Sources/FPRConsoleLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985145a7c0f981a6ab29eff31f9b4ab65b", "path": "FirebasePerformance/Sources/Common/FPRConsoleURLGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a930d396014c30bc24dddfa980000d64", "path": "FirebasePerformance/Sources/Common/FPRConsoleURLGenerator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ed80803fd461c6e9c0192e0cb5a6196", "path": "FirebasePerformance/Sources/Common/FPRConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988a3e313c762b302ebcad422762ee3328", "path": "FirebasePerformance/Sources/Common/FPRConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cca01711fa3ea4b8f658a13662fa832d", "path": "FirebasePerformance/Sources/Timer/FPRCounterList.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb7aa1164e075da20399e0608bca4cac", "path": "FirebasePerformance/Sources/Timer/FPRCounterList.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f68cf9a2345e2edaa9ffd6bfe4707988", "path": "FirebasePerformance/Sources/Gauges/CPU/FPRCPUGaugeCollector.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ddb5a709a836265dafa9bc5e51e941b1", "path": "FirebasePerformance/Sources/Gauges/CPU/FPRCPUGaugeCollector.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d3efa71ad7ce1820a922e9b77ec2a55f", "path": "FirebasePerformance/Sources/Gauges/CPU/FPRCPUGaugeCollector+Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9875735aea77fcc0f926bc64337b5ee9dd", "path": "FirebasePerformance/Sources/Gauges/CPU/FPRCPUGaugeData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e3d1b3ff1c4305388dc3bafa2fe7aa55", "path": "FirebasePerformance/Sources/Gauges/CPU/FPRCPUGaugeData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c8f69ec0d22939ce7e8a0363af0272c4", "path": "FirebasePerformance/Sources/FPRDataUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985d514a9b53eb5c6596255590be88f9d2", "path": "FirebasePerformance/Sources/FPRDataUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980b880f17bf9cfcdede1b6f7af37b3ff5", "path": "FirebasePerformance/Sources/Common/FPRDate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9845296d725ffa00ed8fa3e5ffa43daccd", "path": "FirebasePerformance/Sources/Common/FPRDiagnostics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d0a587ee79223fa2fdcd897b81c22a28", "path": "FirebasePerformance/Sources/Common/FPRDiagnostics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d7eb808a5a6acefb07930e16d075c343", "path": "FirebasePerformance/Sources/Common/FPRDiagnostics_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98802e993b2dc77f785a858556d15f37a9", "path": "FirebasePerformance/Sources/Gauges/FPRGaugeCollector.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860b245b57d6c6488d77dd3de4b4d3fd3", "path": "FirebasePerformance/Sources/Gauges/FPRGaugeManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98706934ee5effb5e541916d1998e5f214", "path": "FirebasePerformance/Sources/Gauges/FPRGaugeManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98baacd2d708e5808efcb1233b4c5abe2c", "path": "FirebasePerformance/Sources/Gauges/FPRGaugeManager+Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98664671417ae3f10ca74166e0722ad9b2", "path": "FirebasePerformance/Sources/Loggers/FPRGDTEvent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f64e12e272541ea7caacbce6d748a078", "path": "FirebasePerformance/Sources/Loggers/FPRGDTEvent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98994bf242530db94d3e01237713ad5ed2", "path": "FirebasePerformance/Sources/Loggers/FPRGDTLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815bc2e7b26db9368477769232853bd96", "path": "FirebasePerformance/Sources/Loggers/FPRGDTLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985a9e513c05b78d221801137cc91539c2", "path": "FirebasePerformance/Sources/Loggers/FPRGDTLogger_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f209e753caf9984285036cf5a88b47a", "path": "FirebasePerformance/Sources/Loggers/FPRGDTLogSampler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d2a6114a31bc5385f368a593602ae147", "path": "FirebasePerformance/Sources/Loggers/FPRGDTLogSampler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986a3618b3839b798fab571c25b493f04b", "path": "FirebasePerformance/Sources/Loggers/FPRGDTLogSampler+Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809e24cff6988c0394cce193eb26786dc", "path": "FirebasePerformance/Sources/Loggers/FPRGDTRateLimiter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98823bca679424b06765282cbaea64b0f9", "path": "FirebasePerformance/Sources/Loggers/FPRGDTRateLimiter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980acf5b2083ea14fd89af6d2245664405", "path": "FirebasePerformance/Sources/Loggers/FPRGDTRateLimiter+Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98df14f46273bfbd18035b295b9b84b13b", "path": "FirebasePerformance/Sources/Instrumentation/FPRInstrument.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843f1aac3cc65543233dded425b08c776", "path": "FirebasePerformance/Sources/Instrumentation/FPRInstrument.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc763ddef080b99377824c53fd1d7b57", "path": "FirebasePerformance/Sources/Instrumentation/FPRInstrument_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865d0b024b78cab78f7a04a533af3dad1", "path": "FirebasePerformance/Sources/Instrumentation/FPRInstrumentation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d7ed2e3c8af10ac502fd4d819f7b6274", "path": "FirebasePerformance/Sources/Instrumentation/FPRInstrumentation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e28558e0ed6ac2879ab076f0e78d4bf", "path": "FirebasePerformance/Sources/Gauges/Memory/FPRMemoryGaugeCollector.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98202b2e55f48c599b954e7ffeebfdee46", "path": "FirebasePerformance/Sources/Gauges/Memory/FPRMemoryGaugeCollector.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982bb96f6fc3b70291d16f4112255ce4ae", "path": "FirebasePerformance/Sources/Gauges/Memory/FPRMemoryGaugeCollector+Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980b30513cd8b2a7e00431b03f277d094a", "path": "FirebasePerformance/Sources/Gauges/Memory/FPRMemoryGaugeData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98657f55ee755c9573b22904a369358114", "path": "FirebasePerformance/Sources/Gauges/Memory/FPRMemoryGaugeData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aa5ee34fa7b862a1827d8dcb96da591e", "path": "FirebasePerformance/Sources/FPRNanoPbUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98551f7804ea2cc68beebc14e7e01cc952", "path": "FirebasePerformance/Sources/FPRNanoPbUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987426aace655716c9dff3bf2cd29afd4b", "path": "FirebasePerformance/Sources/Instrumentation/Network/FPRNetworkInstrumentHelpers.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981b6390683be71926ee0adfffdc8e91cc", "path": "FirebasePerformance/Sources/Instrumentation/Network/FPRNetworkInstrumentHelpers.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b8fc5c58c2de21bcb201151ae8ddf89a", "path": "FirebasePerformance/Sources/Instrumentation/FPRNetworkTrace.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987e956a784346f546fc816cf2edd6cbb9", "path": "FirebasePerformance/Sources/Instrumentation/FPRNetworkTrace.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98638c5422f46fe84d06b8430ff5b74f58", "path": "FirebasePerformance/Sources/Instrumentation/FPRNetworkTrace+Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d69cbb4973be04e4768c0e15fee40224", "path": "FirebasePerformance/Sources/Instrumentation/Network/Delegates/FPRNSURLConnectionDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9839bd4217cf0918db0ab60ac180c201b1", "path": "FirebasePerformance/Sources/Instrumentation/Network/Delegates/FPRNSURLConnectionDelegate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819460d1e3b1806d693f5c44e53ea8288", "path": "FirebasePerformance/Sources/Instrumentation/Network/Delegates/FPRNSURLConnectionDelegateInstrument.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98522096187795b13908d37544ef85c0b5", "path": "FirebasePerformance/Sources/Instrumentation/Network/Delegates/FPRNSURLConnectionDelegateInstrument.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b85c08ec65794dc353df802182d57f97", "path": "FirebasePerformance/Sources/Instrumentation/Network/FPRNSURLConnectionInstrument.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a6b24ca90cf7bdca7af1254501db54cf", "path": "FirebasePerformance/Sources/Instrumentation/Network/FPRNSURLConnectionInstrument.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855af3ba6ec2dc337e6f8c3667aa93e00", "path": "FirebasePerformance/Sources/Instrumentation/Network/FPRNSURLConnectionInstrument_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b15c8fb0ee6e36362714bae812443f64", "path": "FirebasePerformance/Sources/Instrumentation/Network/Delegates/FPRNSURLSessionDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e7d1bff44566571731da226d31ce365b", "path": "FirebasePerformance/Sources/Instrumentation/Network/Delegates/FPRNSURLSessionDelegate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6d7f3ac9ca6e28cafccaed2f432ee31", "path": "FirebasePerformance/Sources/Instrumentation/Network/Delegates/FPRNSURLSessionDelegateInstrument.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b869bb077ba49cda31f7b756ad46aa6d", "path": "FirebasePerformance/Sources/Instrumentation/Network/Delegates/FPRNSURLSessionDelegateInstrument.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894dbb127e70909f592f0c491da89264b", "path": "FirebasePerformance/Sources/Instrumentation/Network/FPRNSURLSessionInstrument.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983c64ff27038a851fd139c2f2a1bbff53", "path": "FirebasePerformance/Sources/Instrumentation/Network/FPRNSURLSessionInstrument.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9867db4e98d2328b4b3852f68b7bdfadd4", "path": "FirebasePerformance/Sources/Instrumentation/Network/FPRNSURLSessionInstrument_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad499869fd0d0e2464b6b801422b60a1", "path": "FirebasePerformance/Sources/Instrumentation/FPRObjectInstrumentor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981d086b9afd5a68accfacc412113145ae", "path": "FirebasePerformance/Sources/Instrumentation/FPRObjectInstrumentor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cf78605a51fc3f16d4c2a0b1cd834b0d", "path": "FirebasePerformance/Sources/ISASwizzler/FPRObjectSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984daec9462026db223e24a026b2c130d6", "path": "FirebasePerformance/Sources/ISASwizzler/FPRObjectSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ee0e0f04ff4340f70e157978e955331", "path": "FirebasePerformance/Sources/ISASwizzler/FPRObjectSwizzler+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982acb20502b57a3077672076c05d3911c", "path": "FirebasePerformance/Sources/Common/FPRPerfDate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4ec7f96b8aaab3d9bff5f97af0b9582", "path": "FirebasePerformance/Sources/Common/FPRPerfDate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988878f178dee929c7280ab484ac9d5013", "path": "FirebasePerformance/Sources/Instrumentation/FPRProxyObjectHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9839808d80dc025dfd0bf63df248843843", "path": "FirebasePerformance/Sources/Instrumentation/FPRProxyObjectHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98085c88543dc710ee1ed876ae66429151", "path": "FirebasePerformance/Sources/Configurations/FPRRemoteConfigFlags.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d0111bd200062df1be5c14eb8a5008db", "path": "FirebasePerformance/Sources/Configurations/FPRRemoteConfigFlags.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98259ea317f376b2bab29a27a72e6319b4", "path": "FirebasePerformance/Sources/Configurations/FPRRemoteConfigFlags+Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f4594baa073ad2fb9dd47cb292f02836", "path": "FirebasePerformance/Sources/AppActivity/FPRScreenTraceTracker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98286392b1009c274579d7fdd721a132ca", "path": "FirebasePerformance/Sources/AppActivity/FPRScreenTraceTracker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad962303707dcc3a70365e3dfe59cd8c", "path": "FirebasePerformance/Sources/AppActivity/FPRScreenTraceTracker+Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98620911c14f80a60e4b7e370695cf5550", "path": "FirebasePerformance/Sources/Instrumentation/FPRSelectorInstrumentor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986be91174ba2218660fb7f67381a9f43f", "path": "FirebasePerformance/Sources/Instrumentation/FPRSelectorInstrumentor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894848b4cb1a153cace5e6c4434e145c9", "path": "FirebasePerformance/Sources/AppActivity/FPRSessionDetails.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9839d8221f8eea87e3380c44914dd32052", "path": "FirebasePerformance/Sources/AppActivity/FPRSessionDetails.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b026ba1914d5eb3062c4852f7f5a8ecf", "path": "FirebasePerformance/Sources/AppActivity/FPRSessionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9840a416005a697d613124a23b0eabe521", "path": "FirebasePerformance/Sources/AppActivity/FPRSessionManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c6bd09baa110ec7f498ddfd5322267b", "path": "FirebasePerformance/Sources/AppActivity/FPRSessionManager+Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dc64704b7d2070eb00e30f4b7e77f4b5", "path": "FirebasePerformance/Sources/ISASwizzler/FPRSwizzledObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d896f3974e78c9164d4753839e02b3ae", "path": "FirebasePerformance/Sources/ISASwizzler/FPRSwizzledObject.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fec18545bec7e94f2ca3f5133c6b7e09", "path": "FirebasePerformance/Sources/AppActivity/FPRTraceBackgroundActivityTracker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832d3ad361b153c34ec1ebaca3f52b9e7", "path": "FirebasePerformance/Sources/AppActivity/FPRTraceBackgroundActivityTracker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984cd2d0be81e66e62ec196367122c1d1b", "path": "FirebasePerformance/Sources/Instrumentation/UIKit/FPRUIViewControllerInstrument.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98297c2f949e35bc09509d724566ad5fc6", "path": "FirebasePerformance/Sources/Instrumentation/UIKit/FPRUIViewControllerInstrument.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f65ee2de020f9ac215c438e723ad9052", "path": "FirebasePerformance/Sources/FPRURLFilter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ed12e23d1b8e959fbe491db0d854929", "path": "FirebasePerformance/Sources/FPRURLFilter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849b0b064790dedd8771d0b2e377ce503", "path": "FirebasePerformance/Sources/FPRURLFilter_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98545b47fcda441e7eb2a9defb31016238", "path": "FirebasePerformance/Sources/Protogen/nanopb/perf_metric.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dbbad7a5d22676e1b3651d23e1b63edd", "path": "FirebasePerformance/Sources/Protogen/nanopb/perf_metric.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9867e1ff0ee6a9649d0767a70a0d039150", "path": "FirebaseRemoteConfig/Sources/Private/RCNConfigFetch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f123aca03f292961592e6f64b9b5104", "path": "FirebaseRemoteConfig/Sources/Private/RCNConfigSettings.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98650707c48bbe3a86d87734783edfc8c8", "path": "FirebasePerformance.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ee91f25d0122930f83211b36f7496d24", "path": "FirebasePerformance-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989bf8c1b96b8891c01be3d324fa9f445c", "path": "FirebasePerformance-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988bfaf21173a6b523816698eb77f8e0bc", "path": "FirebasePerformance-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9845b7ffa2b4345b1c561629f141bdd911", "path": "FirebasePerformance.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ab2b1e7df9867ec9bb6b2561da413899", "path": "FirebasePerformance.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98838c8ea3e85ac53501c50fb648aa24db", "name": "Support Files", "path": "../Target Support Files/FirebasePerformance", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e39cded8879ec7b1b1bae446699a1af", "name": "FirebasePerformance", "path": "FirebasePerformance", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983adde780bdbd5ce4c7aea096a6013d69", "path": "FirebaseABTesting/Sources/Private/ABTExperimentPayload.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987d6f80db1819ba2cbfb3fb6cbcfe5902", "path": "FirebaseRemoteConfig/Swift/Codable.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9881f0bdc826ccad102b60ef79042eef08", "path": "FirebaseRemoteConfig/Swift/CustomSignals.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851db1ab8a2c222376decc9f7cfe370e2", "path": "Interop/Analytics/Public/FIRAnalyticsInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aab186d5b2baf6e88e417672fc854963", "path": "Interop/Analytics/Public/FIRAnalyticsInteropListener.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1e11ba99189b0aeea7ff08b2beb39ad", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a3b6d48a4d77b2523b8ba03010fb23c4", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef64e16eb6624f7aa1ac459d83601600", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd1fa98aefc516d23f6d862ee7b5dab8", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989ccd91b21501dce5e90e4557494a3a7f", "path": "FirebaseRemoteConfig/Sources/FIRConfigValue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b973eee1e4a65137c78a50e1084a301a", "path": "FirebaseABTesting/Sources/Private/FirebaseABTestingInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982071d3d0b4f11ef5fb66b0a85e9a9bc5", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98abbf68e7377128083431819d01d11d8b", "path": "FirebaseInstallations/Source/Library/Private/FirebaseInstallationsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e6a4be98ef030b1b73a9445bf702e9f", "path": "FirebaseRemoteConfig/Sources/Public/FirebaseRemoteConfig/FirebaseRemoteConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d344a189976c819c7d6ba5124a2235dc", "path": "FirebaseRemoteConfig/Swift/FirebaseRemoteConfigValueDecoderHelper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6b9001a41a5ba50fd5e66d8d3cc8f4d", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988295090a412a6445e21a7d845e1458c8", "path": "Interop/Analytics/Public/FIRInteropEventNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982412cd50402714e6352641f6f1d41d7a", "path": "Interop/Analytics/Public/FIRInteropParameterNames.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd4276146e8804c63eae56b26b466874", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e541894c52de75ebe7266b3e0682f09a", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fdcf6cb9f17ebd49cffe43e16f134e25", "path": "FirebaseRemoteConfig/Sources/Public/FirebaseRemoteConfig/FIRRemoteConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eee202e15ed7678df40be8561518c391", "path": "FirebaseRemoteConfig/Sources/FIRRemoteConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982c505f183c900c61ce37d7b16c6d32b3", "path": "FirebaseRemoteConfig/Sources/Private/FIRRemoteConfig_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802a2aaada95d908a255cf909cb2f867f", "path": "FirebaseRemoteConfig/Sources/FIRRemoteConfigComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d6be4a2ff90c1f343b65ef6cc6b23e86", "path": "FirebaseRemoteConfig/Sources/FIRRemoteConfigComponent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989abbb824a25e35cdff3d879fe5079c15", "path": "FirebaseRemoteConfig/Sources/FIRRemoteConfigUpdate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d0709a9b496149c5d077de8085312e57", "path": "FirebaseRemoteConfig/Sources/RCNConfigConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e80c73eafa45a0b90b5eb45fd247bd5", "path": "FirebaseRemoteConfig/Sources/RCNConfigContent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9876af7059d37e700edf6bae98aae27415", "path": "FirebaseRemoteConfig/Sources/RCNConfigContent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9800b1048f72c30e9d38ad48b4ecc8c3a0", "path": "FirebaseRemoteConfig/Sources/RCNConfigDBManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ab3040a51a13ca44d16cd5a3de67a512", "path": "FirebaseRemoteConfig/Sources/RCNConfigDBManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b56f1800d6448ca2cd27289664c63737", "path": "FirebaseRemoteConfig/Sources/RCNConfigDefines.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984cdcdf31c478bfb1e69e8cfc77396ead", "path": "FirebaseRemoteConfig/Sources/RCNConfigExperiment.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f44586e8b62ae07e82adb97dfe78540a", "path": "FirebaseRemoteConfig/Sources/RCNConfigExperiment.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9808404f511e9f9b9333d7aa517445cb67", "path": "FirebaseRemoteConfig/Sources/Private/RCNConfigFetch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d7f13c415034719692340a9d9d6c65be", "path": "FirebaseRemoteConfig/Sources/RCNConfigFetch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983702c70caf85e26a098749a7573184a3", "path": "FirebaseRemoteConfig/Sources/RCNConfigRealtime.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98795b5a60d9c3bc34f8b2ae66992a9a3a", "path": "FirebaseRemoteConfig/Sources/RCNConfigRealtime.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98892760c6eefbdbd8afce62949cf18e51", "path": "FirebaseRemoteConfig/Sources/Private/RCNConfigSettings.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986a14968dea5a20f9445cc0b304b5d550", "path": "FirebaseRemoteConfig/Sources/RCNConfigSettings.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aad403a2e57ff774564db3ef553f5117", "path": "FirebaseRemoteConfig/Sources/RCNConfigValue_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987a474a77b79c153a04de543ff2ba3aa1", "path": "FirebaseRemoteConfig/Sources/RCNConstants3P.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837f2f0e8f68c369e6cfd2938a30cc094", "path": "FirebaseRemoteConfig/Sources/RCNDevice.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e2ab73518fe7ebc30d1897630fe1a701", "path": "FirebaseRemoteConfig/Sources/RCNDevice.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984407e492ed4c40672f6b4c6debc2a948", "path": "FirebaseRemoteConfig/Sources/RCNPersonalization.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e1502f8aab861a540eac621d5e8dd2d7", "path": "FirebaseRemoteConfig/Sources/RCNPersonalization.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9827323e4dd6a4787c28b7277dd097e77f", "path": "FirebaseRemoteConfig/Sources/RCNUserDefaultsManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9834bfc333781cc9adf12ef2c8de21a642", "path": "FirebaseRemoteConfig/Sources/RCNUserDefaultsManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f57b160a1ede0637871c5d3f306e9405", "path": "FirebaseRemoteConfig/Swift/PropertyWrapper/RemoteConfigProperty.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a7b6c05fe50ad5cb494f09ac4012bbd3", "path": "FirebaseRemoteConfig/Swift/PropertyWrapper/RemoteConfigValueObservable.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981b0f01b3b06a6f34836eb21c1a36599e", "path": "FirebaseRemoteConfig/Swift/SPMSwiftHeaderWorkaround.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984b62d56c9978aa16ad27dcdc6a49be08", "path": "FirebaseRemoteConfig/Swift/Value.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9871dc8572e94fffa65f7a11cf058426fb", "path": "FirebaseRemoteConfig/Swift/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f7185335c5c0a19d867bb0d6a3147d8e", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9852458eff65eea7a2d37147f2ce4f7c7b", "path": "FirebaseRemoteConfig.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ad5748563687f0cca721ea30bf55569", "path": "FirebaseRemoteConfig-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985e1a222aea6afb451b766819dd79aa0c", "path": "FirebaseRemoteConfig-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd41b02fb0e17978d3a44b5560a6acd7", "path": "FirebaseRemoteConfig-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98545bc8ced3f3b529ca64cf988b83f720", "path": "FirebaseRemoteConfig.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9837a81f52fa7ad191275d206c47b9f243", "path": "FirebaseRemoteConfig.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987b2e992e6db2c20c27c0212b27d32513", "path": "ResourceBundle-FirebaseRemoteConfig_Privacy-FirebaseRemoteConfig-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9864505736943dc87dd8cf91d7a48522d1", "name": "Support Files", "path": "../Target Support Files/FirebaseRemoteConfig", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b297b6e3425774de8fc64c9f6fa4786", "name": "FirebaseRemoteConfig", "path": "FirebaseRemoteConfig", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9819c26788336cd2c784d49e4dedacf15d", "path": "FirebaseRemoteConfig/Interop/RemoteConfigConstants.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980ed125f3111c505908cfa4b6f02415aa", "path": "FirebaseRemoteConfig/Interop/RemoteConfigInterop.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ccebf5515082fa37984a7bf6a6cb26b7", "path": "FirebaseRemoteConfig/Interop/RolloutAssignment.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981431927b8063bf378f5b81260c8ef3c4", "path": "FirebaseRemoteConfig/Interop/RolloutsStateSubscriber.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98774242ac2940c78b6d4ab7f9f8fdf68a", "path": "FirebaseRemoteConfigInterop.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a135fd1397272f4f0b88c550f02d1afd", "path": "FirebaseRemoteConfigInterop-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9868a8ca6c005e14f1e9f166f3e49ae0e0", "path": "FirebaseRemoteConfigInterop-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f2d6f1104ee537e6b13aa7cc60589b35", "path": "FirebaseRemoteConfigInterop-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9880363a16f12142f4f0b0c05ecc5a2696", "path": "FirebaseRemoteConfigInterop.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bb8b29a3873fbbcab496ca32ea3c6248", "path": "FirebaseRemoteConfigInterop.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988cec7b28b056c490f96129a68cb4ac25", "name": "Support Files", "path": "../Target Support Files/FirebaseRemoteConfigInterop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896bbd31bc8e09a6f4ded5accdbfd15cc", "name": "FirebaseRemoteConfigInterop", "path": "FirebaseRemoteConfigInterop", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9870b184b9cfa355c9ee6850dfece691ab", "path": "FirebaseSessions/Sources/ApplicationInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ef06ab61016ba4b9f86695ca6a406f59", "path": "FirebaseSessions/Sources/Development/DevEventConsoleLogger.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981275a7ecda5917993c3e4ac0a900537a", "path": "FirebaseSessions/Sources/EventGDTLogger.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9844cfe3af760bbabc6a9afd047aee2c22", "path": "FirebaseSessions/Sources/FirebaseSessions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9887cb3fc3014aa585c18efae210674cdd", "path": "FirebaseSessions/Sources/FirebaseSessionsError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98736b2bb3b685ec8b0d6cb7fd45270eb1", "path": "FirebaseSessions/SourcesObjC/NanoPB/FIRSESNanoPBHelpers.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b31a325d7ff5ff03674f383ff91bac9", "path": "FirebaseSessions/SourcesObjC/NanoPB/FIRSESNanoPBHelpers.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982675de0427216fe383a742246c598647", "path": "FirebaseSessions/Sources/GoogleDataTransport+GoogleDataTransportProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9863054bfe170d3bafe3a16ba2da22a941", "path": "FirebaseSessions/Sources/Installations+InstallationsProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9812cb7a6a8a0a1c2af8fba13ead46cc8e", "path": "FirebaseSessions/Sources/Settings/LocalOverrideSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98230beb9697ed987b78658033b2606b75", "path": "FirebaseSessions/Sources/Logger.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986a62828545725f1d04e0db025eaa16bb", "path": "FirebaseSessions/Sources/Development/NanoPB+CustomStringConvertible.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98556537dac17ae7552cca06f1f034df9b", "path": "FirebaseSessions/Sources/NetworkInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989221a656e428749d5726086a89ac1a95", "path": "FirebaseSessions/Sources/Settings/RemoteSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987e0cde35d33bf3883db405eeaf5a79c7", "path": "FirebaseSessions/Sources/Settings/SDKDefaultSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c26d3c589991d479665ae98f5cb17771", "path": "FirebaseSessions/Sources/SessionCoordinator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986c575f5195a85ba999503e46c80be921", "path": "FirebaseSessions/Sources/SessionGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f05573afbd8e495290d29e3085aadd24", "path": "FirebaseSessions/Sources/SessionInitiator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98aa724713a39319556e3a581fff7d5ad7", "path": "FirebaseSessions/SourcesObjC/Protogen/nanopb/sessions.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98211f719cde0ec830b6c7fc33b3866351", "path": "FirebaseSessions/SourcesObjC/Protogen/nanopb/sessions.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9873b97318048a67f6468d7fe1192b6bf5", "path": "FirebaseSessions/Sources/Public/SessionsDependencies.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d7e43350a0769f2a8981970ba81a19e6", "path": "FirebaseSessions/Sources/Public/SessionsProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9880699241d6d8bef4718ea67ccdd016ab", "path": "FirebaseSessions/Sources/Settings/SessionsSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9833fd774371bcff29c8dc16f3531c7dda", "path": "FirebaseSessions/Sources/Public/SessionsSubscriber.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a616cf2cc3487785e0131b0cbc157158", "path": "FirebaseSessions/Sources/SessionStartEvent.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9860026c0eb41264427c498c1253014567", "path": "FirebaseSessions/Sources/Settings/SettingsCacheClient.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d65bd2d42eb092bfe36400ca84b498b1", "path": "FirebaseSessions/Sources/Settings/SettingsDownloadClient.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982cff4bbd8b64f119d60dc0925b859673", "path": "FirebaseSessions/Sources/Settings/SettingsProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988abc72fcbc4ebd58c108dbd87b8f23d0", "path": "FirebaseSessions/Sources/Settings/SettingsProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cbb14609046fe56eea849dd94efe6025", "path": "FirebaseSessions/Sources/Time.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9839450e32b9a9bb3b10f53f8d17e0eb33", "path": "FirebaseSessions.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9897f06eec729d4ba7ab93f14c1f8d0f9a", "path": "FirebaseSessions-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9847406f8fa03e459df7041af1e3feb284", "path": "FirebaseSessions-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b60e9f9258b769162be7e851e240fc34", "path": "FirebaseSessions-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9842463dbe505462c39bfc09a596294dc5", "path": "FirebaseSessions.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9855ec57bde405ca4d3071a536b08a9fbb", "path": "FirebaseSessions.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98190a26ef94c7eb796ae626d26fb13dcd", "name": "Support Files", "path": "../Target Support Files/FirebaseSessions", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d92c9ead76f4619165b8fc7290d8c29", "name": "FirebaseSessions", "path": "FirebaseSessions", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9807233c5f8cbe07a3ff884db58ca34a2e", "path": "FirebaseSharedSwift/Sources/third_party/FirebaseDataEncoder/FirebaseDataEncoder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b45175e098bfd059afbeaa2c8f1cadc3", "path": "FirebaseSharedSwift/Sources/FirebaseRemoteConfigValueDecoding.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9818df971c192a796e6072bd19b5773f3b", "path": "FirebaseSharedSwift.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b1d213cc5ba1770149c97f2960334312", "path": "FirebaseSharedSwift-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e2c2ce7cfce01e32e7dd9f93eb2f20a", "path": "FirebaseSharedSwift-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0b25348417e03f54753fd1bed0cc08e", "path": "FirebaseSharedSwift-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98efab497131d705a83d7622cd49b6974e", "path": "FirebaseSharedSwift.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989251bc98c928996e7744650f3041b5cc", "path": "FirebaseSharedSwift.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c0285a1622d59c0fd6f8308a6a81aa49", "name": "Support Files", "path": "../Target Support Files/FirebaseSharedSwift", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b87ddafaab748aa37eed10c1168bab0", "name": "FirebaseSharedSwift", "path": "FirebaseSharedSwift", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e989e1d0a2bddb1df97c7f517d8f03b566e", "path": "Frameworks/GoogleAppMeasurementIdentitySupport.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983862f578dfe5275be07282d6231db119", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e93d907e1e4738669ca3e4650cdfedcd", "name": "AdIdSupport", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e984cb489d15d6798c8263cbc6461d6c779", "path": "GoogleAppMeasurement-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f1ed97f8a2bb6597cde21aca836c5308", "path": "GoogleAppMeasurement.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98eacf57fc5e18fce0308ac880b9c0ca6f", "path": "GoogleAppMeasurement.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b46e216873c5fafbd4f065ed74bd301c", "name": "Support Files", "path": "../Target Support Files/GoogleAppMeasurement", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e988487c1b38c6276c729a5c6a4341cb3c4", "path": "Frameworks/GoogleAppMeasurement.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984ccb1aa652a81f0b7528f3d7a6580226", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b141e47fba64f7682c2df6ddb218a7a", "name": "WithoutAdIdSupport", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985bf5adbf848517adfff4d067d294af05", "name": "GoogleAppMeasurement", "path": "GoogleAppMeasurement", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e980ff0e861307bfefe938578452e51353e", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a56d0dafa74d8dee49eabce0b9982a0", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98165667606263461e39157ff574b04471", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983faabe52d21d59e54f84407726068f0f", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98eb68ddf7850e3faf085fe64fc9bef555", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e9bca4bcedaae58416266a99af3c43d6", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b202520cb80bcd3feeae7b194ea9ee60", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eafa41306329bd7d0fc3a6cf0e74b8f0", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982b97c3494c3676112bc04a798e5d9b1e", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985f86fcedd732712691705e73945e9115", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9835ffa59254e14b0b090fa5fb12c327f5", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTCompressionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987120c28cd9b607e03e455545126b2d22", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTCompressionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a891bedd13a6f2038b108a55aec6365b", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTNanopbHelpers.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98585641f1e8e2a623ee754ebdb8c7f3fd", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTNanopbHelpers.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aca427f51bfee5f70ee828dfb143e8c0", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ed3da76f753741a233512dfb0387c85d", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833ffe0a91dca4c98263d3e164c6ed950", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploadOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9841738c61d9d2d2aac5052f5a5ecdbeec", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploadOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f7adf61622f54fc3c7669ab22bb119bd", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTURLSessionDataResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cb00d3eef9c913f84e70b5e3122348b9", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTURLSessionDataResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828dc95419708fdadc3f6784e9de97769", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORAssert.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98162cd5f65c1f8dab22a58a248d4bd3b4", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORAssert.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987df8f80579edf61b365ded5c0877607c", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORClock.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98510b1831ee37800d2f3c3b7ecd4f3cb1", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORClock.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dbe74f0dbd27bfd82ffc6d2f9814fb71", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORConsoleLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e07c88a50697de56b9c872945ccb0889", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORConsoleLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e31619652df901925340bb40f288caa2", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORDirectorySizeTracker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d813ec01a256c316f2c065900f9b5032", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORDirectorySizeTracker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986474887c57ad3516a694a61f55c10890", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREndpoints.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af318b2c0047db5e9557c9c8a6e1fe3b", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREndpoints.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a8246639e5a5605d2bdc575c19ac91cc", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREndpoints_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba95d91ec5e6fbabd67e165492a041a1", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREvent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983b37caa90310179112248b9e3449ac35", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREvent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981dbc33395d196a4c0cff248d7570ce1c", "path": "GoogleDataTransport/GDTCCTLibrary/Public/GDTCOREvent+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d3f8e781cf8166c730a0c6adcb036612", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4d56e894886dc8e37b6b39c7ef3f75d", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCOREvent+GDTMetricsSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981ddc3501bcc459c9cceacc7b37630dfb", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTMetricsSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d12a0f097eadfde081510525615b2901", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREvent_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c05e46735110889d5cd1afe59fceffd", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventDataObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c8b9d5ada20ef6b440b1689e48b0cf9", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCOREventDropReason.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1aa6085e2b43b3a0bb05a719bcbd641", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e2d9d9041e96bfa9aeb263f82ad559ca", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988e2ebfb12ac8472156d00c045cd250bb", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b72dd8342334f80f586d64c3a5206292", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage+Promises.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9880a43eaed7bcb090fd858d12b034a1fb", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage+Promises.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858d899044e7e1afd1e8ac4a1b1ddf692", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORLifecycle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982bf399d3d42e8cab5f3611d7f4b81573", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLifecycle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9801aafc6dca28f11191040f75dfb2f5c1", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORLogSourceMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3d9e3071405bd3c3a28375babbb5f9b", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLogSourceMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ca3772b99778d79093df9df8be369d5a", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989a69a94aaf389666369c532e21cfb415", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981cd802b42c280f1acd166f50f35fb3e1", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCORMetrics+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c9a0376cbeda742f82354f89aadcd3cb", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCORMetrics+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b13ec687c99d2b027ae00b6383835fc0", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98317297159313121d705246811b2f5446", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858ff2a3ec8b123f9159a0621e9d6da80", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORMetricsControllerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dc13ab9d69e6efcefdc371e039367c68", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98327e4cc76a6e13d11eae03bb49005278", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e74d64f36231c750ef20a31a8ddb466e", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORPlatform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9895942184f99234134e807378bc4c2ff9", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORPlatform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4cbc2bfe09fbc3f8068195203efabd3", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORProductData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9877abc116cca17bcbf503d05ff2c9dbbe", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORProductData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985fea328a35324ca82dbe44521346d0e4", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORReachability.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98973b38b8a660586a78b0fc8e726f3e81", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORReachability.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872702a66fa11d7ff10158270a7abb25e", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORReachability_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986bb0c0cd6cc5c1f68b63695e4fcc2958", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORRegistrar.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a0f4d74eb29ffaebe840866d9a0ba1d6", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORRegistrar.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980d1cdf9f1e8e0ed659b0dbb774b1d73f", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORRegistrar_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c4ac66a2a3e1e66428c9eba496c83df0", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageEventSelector.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f46221e80f56c68dbc4af3a73bc13ff2", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageEventSelector.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1f32ad6235d9cf5c3a27658c6779562", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORStorageMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e161280039d33938bf06e2b35f13c726", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ac4f6ee026aa5c077e1006c93e18708", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98407ef6116852c4b67a5e8eaa23a8dec7", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageSizeBytes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9824dd7f7439202b90ba94d043f765bc09", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTargets.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882470f0dff6055dbcc45185404366f1e", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b9fa1b8dfca2fa436754986d4650db4", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b841a031ea97f4de43f91c3f4ffe5e6", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848e9140e8d3be9b3c691ad6f52506f94", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTransport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985a41d626456a0b16cbe82430f82032c8", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aefe513d2304cba2ff53c44f4686dad8", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransport_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a839a80707d2ce7a93620271e13db196", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadBatch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f2b7b04494474b7c1fdd482e09d2800d", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadBatch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9835e95b4728d0b361848279983b241734", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadCoordinator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986ed245221abb65ba542bed5d54a78d33", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadCoordinator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897578d2e2e075c6c0bd4d8d11e6c588e", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9890619c627a926c2a6254098755bb5042", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GoogleDataTransport.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e4864f763f55b69f391640edce708ff5", "path": "GoogleDataTransport/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bec645b72b2edf47b4ecd38d11c3ae61", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e7a0c2fce3b1476471d246bfba4a2fc5", "path": "GoogleDataTransport.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986479ada1c3fb6c304d7b1e72bf6f7731", "path": "GoogleDataTransport-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984f95a92477e14fb68e9aa6d9a2b49aea", "path": "GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed6cda88f36b0db93798ba36cfda86c9", "path": "GoogleDataTransport-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f3219ddfa271799b82fe175be8666e91", "path": "GoogleDataTransport.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988ca46cbc4f3ce9d452c4ceedd7aa9663", "path": "GoogleDataTransport.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983757a81a348c549cecbc4197330848ad", "path": "ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984993d8cc940ff08f6c5763c676f1ca68", "name": "Support Files", "path": "../Target Support Files/GoogleDataTransport", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98545201d5475c5758bb3312f4f507a72b", "name": "GoogleDataTransport", "path": "GoogleDataTransport", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eef55abe37de2020f65ca0430d9904d0", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULAppDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98afe847b2b82ab16c29f3e2e9f9a1dc0a", "path": "GoogleUtilities/AppDelegateSwizzler/GULAppDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e7e5419503c3e43aa79d88df015e1c7f", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULAppDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d5c5a6af513092382517cedb8eb97ad0", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULApplication.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dbaeaf09742390125f0ead95c02a4ea6", "path": "GoogleUtilities/Common/GULLoggerCodes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988beac7e5f808f7305e10582ed47388fd", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULSceneDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98215fe20f3c0a0ef04888b24b8916235f", "path": "GoogleUtilities/AppDelegateSwizzler/GULSceneDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a0c39726a80a4c2353220f7b2ba0dd1", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULSceneDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982ce194ba87304e490b46f2f1db1d77db", "name": "AppDelegateSwizzler", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984212224c33ada177225c894977235042", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULAppEnvironmentUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ed58eb266ae007cef0076bdec4f13bd0", "path": "GoogleUtilities/Environment/GULAppEnvironmentUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a05287a87cb6a00c263e2d24aa1c037", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c1291b8c4b751996d4ce39b042395982", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e68fd2fe7d1250b277c6f283f1b2d253", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989127fc213011bc67c2da4f655fd5309d", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e978fd761a2bdd53bf454eccf5393357", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULNetworkInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d5621f1ebb29247f77035bd33a945c00", "path": "GoogleUtilities/Environment/NetworkInfo/GULNetworkInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981add152abe9d6773bf1f0c7212df3281", "path": "third_party/IsAppEncrypted/Public/IsAppEncrypted.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986588aa1a6824031479d89e3c8c89d8a8", "path": "third_party/IsAppEncrypted/IsAppEncrypted.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9857201f1a1380707243ee4ee3021b1083", "name": "Environment", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bdcd406a9a7a3eb446448f741512bbeb", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a8454342c785bf02329ce075a8f8fd2a", "path": "GoogleUtilities/Logger/GULLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9b1ab153a29c18ed7ef422fa8e1b699", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLoggerLevel.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989d049a66de9c29d51b1900b553ab1668", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b2ce0f7d1e0365fc5b2657d8df70d1d4", "path": "GoogleUtilities/MethodSwizzler/Public/GoogleUtilities/GULOriginalIMPConvenienceMacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d078ff50a6ebfb506601681eda581695", "path": "GoogleUtilities/MethodSwizzler/Public/GoogleUtilities/GULSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98891087f0ad767e79529a211821ef5f9a", "path": "GoogleUtilities/MethodSwizzler/GULSwizzler.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e583d2918f9ec95e673e99760343bbb7", "name": "MethodSwizzler", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c2d96ef0db33d486b7dd0506d4cce0df", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULMutableDictionary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e27e1b8d019c6fc09d33e1a7d9488413", "path": "GoogleUtilities/Network/GULMutableDictionary.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ca3becd009f890e3ef825db2c4a37fd", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetwork.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989975365546a5291f26f91b73d28eab31", "path": "GoogleUtilities/Network/GULNetwork.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9877486c78b067e514a18a5673d651148c", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c7ac9c12f5630862a9d28bddf0d0c9c", "path": "GoogleUtilities/Network/GULNetworkConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d730c84abb6a94b75e5857cbd168e93", "path": "GoogleUtilities/Network/GULNetworkInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98530c0d2a5c092626bae102108504fd5c", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkLoggerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd9b4ca8787d198fdfbc5677e46860e1", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkMessageCode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cfb65397b11043e800045f80841581f8", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkURLSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b26136d8295b929f4c88ab6cf3e162e4", "path": "GoogleUtilities/Network/GULNetworkURLSession.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98db60bf12dfe02fd7053ffc23a45b95cf", "name": "Network", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4a4fcce66595e2f04e76713aebe868f", "path": "GoogleUtilities/NSData+zlib/Public/GoogleUtilities/GULNSData+zlib.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985bd40b65dc3f36e19a23cf6a03367564", "path": "GoogleUtilities/NSData+zlib/GULNSData+zlib.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a17d418b1df5766f62e9dcbd8e95add5", "name": "NSData+zlib", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98cb76aafaa0b212d6891b76f8b4414341", "path": "GoogleUtilities/Privacy/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9871e7102418c4d9cc586313b0db92217e", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9882943071467bd9166f941dca25beb0f4", "name": "Privacy", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9878987fc20dcdfca390a99be9d03ff242", "path": "GoogleUtilities/Reachability/Public/GoogleUtilities/GULReachabilityChecker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a7e207d2834951581ab90a2800f0f2b0", "path": "GoogleUtilities/Reachability/GULReachabilityChecker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989503babc96c4d6f8a65d14b44baf4c8f", "path": "GoogleUtilities/Reachability/GULReachabilityChecker+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8987a4b7f2c5ca3130acdc74ad1c76b", "path": "GoogleUtilities/Reachability/GULReachabilityMessageCode.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98442412d8768a40e1ad5310d5650abefe", "name": "Reachability", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9839335562e7a0fb7fda7bea450454b8c7", "path": "GoogleUtilities.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98277d649a4a02b2a894bacd6c630c7291", "path": "GoogleUtilities-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985a7c21ea8c91ab3b3e9b709632ce8e1a", "path": "GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982fedc1452d92dc5bfa1faf1fb174b508", "path": "GoogleUtilities-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9817e037390b81cab28d38419cc5014874", "path": "GoogleUtilities.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9825e24c0fa87ebd27da5fc4c5b01a2e89", "path": "GoogleUtilities.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b372c372c397ca263702f8a69465952c", "path": "ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a57de9697f64fe888998f0748499e7a2", "name": "Support Files", "path": "../Target Support Files/GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988d556d19d2f81fc778b208e4f536c310", "path": "GoogleUtilities/UserDefaults/Public/GoogleUtilities/GULUserDefaults.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d6b500ab5854da9e3163902ded397afc", "path": "GoogleUtilities/UserDefaults/GULUserDefaults.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9832c31cb14eab85734567203601e6bcd0", "name": "UserDefaults", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898daccc91ae53d86633dfafcc2529f04", "name": "GoogleUtilities", "path": "GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e982f8327a790b0598f743ce5a02f57c789", "path": "Heartbeat.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9861bb5489761ba0a0eaa5b2a59eba3631", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e988a419bd591ebe6f810ce06064b8021d7", "path": "Heartbeat-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98532a62c951c4594af02a40bb18d9ba05", "path": "Heartbeat.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982d34c4aad96fb108c96f73338eb5b6ea", "path": "Heartbeat.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985f2d980b08370cbe238adf332667b89b", "name": "Support Files", "path": "../Target Support Files/Heartbeat", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6c369a465c208c38d663a4e9416d326", "name": "Heartbeat", "path": "Heartbeat", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98777c3bc8f5ed9d41e3e010670f2181b5", "path": "IOSSecuritySuite/DebuggerChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9870bac3886c05a68629c8127b819d0cc0", "path": "IOSSecuritySuite/EmulatorChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c99d2be06901bb7693e421fbf19999f1", "path": "IOSSecuritySuite/FailedChecks.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a2de19d6be80e57084f74c627e88f240", "path": "IOSSecuritySuite/FileChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f8f3007f77bba617abe097a6927d2746", "path": "IOSSecuritySuite/FishHookChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f1f13389303eee29121d785ab7ae4d95", "path": "IOSSecuritySuite/IntegrityChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985f2eccbb40161e2d137f842b73480875", "path": "IOSSecuritySuite/IOSSecuritySuite.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9848167810ee4d6195dbaeb768d1210237", "path": "IOSSecuritySuite/JailbreakChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98953e3b70374702a23770a1fdbc401479", "path": "IOSSecuritySuite/MSHookFunctionChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985fd8c17c4aba545165374388130f028f", "path": "IOSSecuritySuite/ProxyChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98830a5150511d93481ef080d0c7d59a43", "path": "IOSSecuritySuite/ReverseEngineeringToolsChecker.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98de8865be053e183ee99e636c7f6384af", "path": "IOSSecuritySuite/RuntimeHookChecker.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9813547fe1dd7e44989394a568d944608e", "path": "IOSSecuritySuite.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985d0fe3fd567f7a6f8c9fc2e18986aa23", "path": "IOSSecuritySuite-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fa778e785e0d9f0fedc1fa1f29d0b57d", "path": "IOSSecuritySuite-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d85e967d9bf51fea5d69eed3d52aa8df", "path": "IOSSecuritySuite-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6dd8779450044f31f2f185467cda566", "path": "IOSSecuritySuite-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985cdf4fb1442186dccd9f77b73259a01e", "path": "IOSSecuritySuite.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9810bed04a15dcedac34f2ab3228dc9230", "path": "IOSSecuritySuite.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bb4495f7f301f89cf7f839131ef3766f", "name": "Support Files", "path": "../Target Support Files/IOSSecuritySuite", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cdf431f9e2a9a5ebc15f002c3fb6f221", "name": "IOSSecuritySuite", "path": "IOSSecuritySuite", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98583ebc777a0684595b404db7138d37ed", "path": "Classes/ios/Scanners/MTBBarcodeScanner.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9836a48c0fedd9e280062df1f99c8ce7ab", "path": "Classes/ios/Scanners/MTBBarcodeScanner.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9859534e65cca2dd056b224701a8a64b5e", "path": "MTBBarcodeScanner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f20eee92968efd7b3ee7f21c21086959", "path": "MTBBarcodeScanner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e88ac859f373d6a9e99b2e84039e22f6", "path": "MTBBarcodeScanner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bc2019cc43ee146fe6a94e79f1f99b32", "path": "MTBBarcodeScanner-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9b9c79628628624bd3fe6106a23813e", "path": "MTBBarcodeScanner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989dc906d100ac4069825d48a0d40088f6", "path": "MTBBarcodeScanner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988902e042f844c502973f4ca96845a63a", "path": "MTBBarcodeScanner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9843edde82130fe137894e52e5aa110f18", "name": "Support Files", "path": "../Target Support Files/MTBBarcodeScanner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98698f1ace099c09c09c830474edcf48e3", "name": "MTBBarcodeScanner", "path": "MTBBarcodeScanner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d0252f69f03ef08c0a6aa5f40aac06e", "path": "pb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9882e04200d1c024625a63037c163247b5", "path": "pb_common.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9889e55b310b564c2d60ed3b348c4c899f", "path": "pb_common.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98728b630cae789d8a2c5020c1774c5262", "path": "pb_decode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be5f834df24013a6ab364008e50d47c6", "path": "pb_decode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986eeb0beacf99dd6ef6474d3016bcc48f", "path": "pb_encode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984b0ba4903e1ea5375614df6b89f78749", "path": "pb_encode.h", "sourceTree": "<group>", "type": "file"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3006a2e56b8f0509c1654dae5a3f16d", "name": "decode", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98d2862ead4d0fc181509190b1306b4273", "name": "encode", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e988d3ba200707e8b5cc9b34f4252ee3d23", "path": "spm_resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9801dec8ce276fdbb41aa05b26dd3e50db", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b01214e2b42ef2572a665ff195e73280", "path": "nanopb.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98629b4099243be0d8cd1a9e025092ba58", "path": "nanopb-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e5d4f492d827cafbcca5f47315a18eb0", "path": "nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856859ada74c78077ddbe126e965ee57f", "path": "nanopb-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9844359ea6c82e0422859aef4a284c9428", "path": "nanopb-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984ed282f93438e8dae89e46863033e0ee", "path": "nanopb.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f9c71f469bff57a6d27a00020269a2d0", "path": "nanopb.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98806f2773e0aa252c3dbdde221973c157", "path": "ResourceBundle-nanopb_Privacy-nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988de2ae1f4c14de4792203bd03fe30f6b", "name": "Support Files", "path": "../Target Support Files/nanopb", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6c9a65bf27a2f27badfc13d4ee0d365", "name": "nanopb", "path": "nanopb", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98980d6f8dc7b91dd28f1ede09be797ba4", "path": "OFDCamera.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988954f6ba45d16454fdb6043db2a9e089", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98724d0d5578365a4a205b2b9764755c30", "path": "OFDCamera-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983468feb18cb49c4b2a37b67b5247d5c9", "path": "OFDCamera.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9890be1d697ea500b74b67d76b42ff4e23", "path": "OFDCamera.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ac10a74138329802d02b4acbb914ecf7", "name": "Support Files", "path": "../Target Support Files/OFDCamera", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3fb4736bf858eacae537ba9fee36d07", "name": "OFDCamera", "path": "OFDCamera", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858c1ac6fa3ff351cfc42f9256ef9ad80", "path": "Sources/FBLPromises/include/FBLPromise.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bd7be5a07898fb5cd8b834158ee5c181", "path": "Sources/FBLPromises/FBLPromise.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987fdeabc853a0bbd77749b7e4f1993539", "path": "Sources/FBLPromises/include/FBLPromise+All.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982197a59fd5886560b23332078175855b", "path": "Sources/FBLPromises/FBLPromise+All.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988d8e4ddcd6767f60b07f010a7359d66f", "path": "Sources/FBLPromises/include/FBLPromise+Always.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ff4fed8cc11c22462f34ef0b0d0efc10", "path": "Sources/FBLPromises/FBLPromise+Always.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b5a26de4e67fcc839144c0dc8c47b9a", "path": "Sources/FBLPromises/include/FBLPromise+Any.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a42bfd2837731afb0cadfc174e131bb5", "path": "Sources/FBLPromises/FBLPromise+Any.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c7f80a311b43a16787acd5fab2939a6", "path": "Sources/FBLPromises/include/FBLPromise+Async.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f38b0139f6bab83e2355b15070175396", "path": "Sources/FBLPromises/FBLPromise+Async.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9861d951d6e716031dec3a5d92337e7c47", "path": "Sources/FBLPromises/include/FBLPromise+Await.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98747d3caa5e6a3a89ede93b294b6b4d91", "path": "Sources/FBLPromises/FBLPromise+Await.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9876de6b47029ab16c60a4b70bf0f16f12", "path": "Sources/FBLPromises/include/FBLPromise+Catch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98255c8a59ac89f954a4874ace3d0002ec", "path": "Sources/FBLPromises/FBLPromise+Catch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d36ae3bd93ba038d74cd84ab5c7835c", "path": "Sources/FBLPromises/include/FBLPromise+Delay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982ceb5302a9ed9b840b386e9662c877c0", "path": "Sources/FBLPromises/FBLPromise+Delay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d1ff6fc63d390969d9976d12a77b6e74", "path": "Sources/FBLPromises/include/FBLPromise+Do.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980e82b8523739a9b0210e91447bb407ac", "path": "Sources/FBLPromises/FBLPromise+Do.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98346982e23ce9f6d705f3ddb78f7ef5b7", "path": "Sources/FBLPromises/include/FBLPromise+Race.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984f60e2740487a749fc3cf321026fcc8c", "path": "Sources/FBLPromises/FBLPromise+Race.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987b9293dd052583ff4bf9e55b903f3a74", "path": "Sources/FBLPromises/include/FBLPromise+Recover.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d2c39292e171fd3c95408892cc88aeee", "path": "Sources/FBLPromises/FBLPromise+Recover.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a32550a458bdb951bc4c6f0a72fc818", "path": "Sources/FBLPromises/include/FBLPromise+Reduce.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a8838cbdfe71679cb42700e9b90e14fa", "path": "Sources/FBLPromises/FBLPromise+Reduce.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7d8808c0cc730931aed7850570c7575", "path": "Sources/FBLPromises/include/FBLPromise+Retry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982db9b34439f877bce095acdaa40f37cd", "path": "Sources/FBLPromises/FBLPromise+Retry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9847f9f5c9172d6792379f0e31987d9cd8", "path": "Sources/FBLPromises/include/FBLPromise+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9888113c848910998af7b1b2c87a085774", "path": "Sources/FBLPromises/FBLPromise+Testing.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b15bd284f294a5b25008b6c504e4151", "path": "Sources/FBLPromises/include/FBLPromise+Then.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9840e620bca6ee65204fd7cbaa5270b409", "path": "Sources/FBLPromises/FBLPromise+Then.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d494331799482a68a8461c446d46f8f6", "path": "Sources/FBLPromises/include/FBLPromise+Timeout.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9841e733f4afbd1be9c6426fd1e17b7579", "path": "Sources/FBLPromises/FBLPromise+Timeout.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ddbc082351f03449ab2ebbbb6fa8d6a4", "path": "Sources/FBLPromises/include/FBLPromise+Validate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9840f4313805e6d7e9b2e4c03c8d662343", "path": "Sources/FBLPromises/FBLPromise+Validate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0f23549e5b1cabd3ab9d430bd8c5a8c", "path": "Sources/FBLPromises/include/FBLPromise+Wrap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98370714f233bd6018f510c3a8ab77e769", "path": "Sources/FBLPromises/FBLPromise+Wrap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dcfb2205b89d5d336d514bb8174007f5", "path": "Sources/FBLPromises/include/FBLPromiseError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987393e781498f61691eabf3b588110f9f", "path": "Sources/FBLPromises/FBLPromiseError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984139bcb4c86e008b94aa3169354a1934", "path": "Sources/FBLPromises/include/FBLPromisePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9868872e72852df5cf0f36df9f3f46b8ce", "path": "Sources/FBLPromises/include/FBLPromises.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98996deacae33965d2e70ffdafd7734312", "path": "Sources/FBLPromises/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fcd8166eb2761d34cb94fdeed7965fc9", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98adda731faf2881a2a650730cab4be6f6", "path": "PromisesObjC.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b21635e9534d42741f36e550c1f98d6", "path": "PromisesObjC-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983d51d40cd9705fc51922e6abbb0aa65f", "path": "PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f343e8c39fa4f9560ac317e868517ff8", "path": "PromisesObjC-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98382406d3c4c06275e1846a71b17c69fe", "path": "PromisesObjC.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fb3089e3353ddef1f5a42ed007f6814e", "path": "PromisesObjC.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e335c73a8751211772e9c069e172012", "path": "ResourceBundle-FBLPromises_Privacy-PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9848abef82ef76f92b14295e9079e5dbc7", "name": "Support Files", "path": "../Target Support Files/PromisesObjC", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9822cbb187ef8325fb271e644204a44dba", "name": "PromisesObjC", "path": "PromisesObjC", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98858c01ab30f71fcb6f7776f7b9801190", "path": "Sources/Promises/Promise.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dd6331281eb2593cd0f967927cf3b769", "path": "Sources/Promises/Promise+All.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980bb464b5792fb9b2d8483dfabf87e971", "path": "Sources/Promises/Promise+Always.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bd959c9139cdcdf576fd714c7c180be0", "path": "Sources/Promises/Promise+Any.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9800083eedc6fdda2a282a98b5105cf4ea", "path": "Sources/Promises/Promise+Async.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98984da9d847961a8689c038f8bd98721b", "path": "Sources/Promises/Promise+Await.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b8b49df0776f28edb9df35244cc8eeee", "path": "Sources/Promises/Promise+Catch.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98962035726adf43a9f7fccac1131aa45d", "path": "Sources/Promises/Promise+Delay.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984560d929996dd08a78a97fdcfd9b7eaa", "path": "Sources/Promises/Promise+Do.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981307b65035865d147daca4705906cd44", "path": "Sources/Promises/Promise+Race.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fa512c0ab147276fea0b1e058961307a", "path": "Sources/Promises/Promise+Recover.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98379177711014cd5a28781f338fceb1dc", "path": "Sources/Promises/Promise+Reduce.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b4e575fe463952e6cc061a637cc18ed2", "path": "Sources/Promises/Promise+Retry.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e43cf46b505e6b4f1a8d46d122a7e617", "path": "Sources/Promises/Promise+Testing.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984da91cc344f2fa288b0d89d7713a8335", "path": "Sources/Promises/Promise+Then.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9815260efcef2b9d5a56cdb3e0a5961716", "path": "Sources/Promises/Promise+Timeout.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9825d759e11a11f612b0da098f0c9f274b", "path": "Sources/Promises/Promise+Validate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c35ff4a8b60f75806645436d94f14475", "path": "Sources/Promises/Promise+Wrap.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f8a553611a200a38d06baaf49cfe49ef", "path": "Sources/Promises/PromiseError.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a4204d87cda4730cfe63838de5423d7e", "path": "Sources/Promises/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985045a9be629d74a3943f0f0de2bf14dc", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98beff8885e59707673875b9de531313ea", "path": "PromisesSwift.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815e4725baf12f0b02fb620a9f22614c3", "path": "PromisesSwift-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983c803915c5b15558cd3994262045f067", "path": "PromisesSwift-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9884fc7534c7aed3b99983ca5361b543b7", "path": "PromisesSwift-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982bf9125167e2ed314668593536fede6b", "path": "PromisesSwift-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fa6714139686b8cdfca1b1273af77114", "path": "PromisesSwift.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a88fe8a23e4fdde4105c4dd606d35ec7", "path": "PromisesSwift.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984414dac83452ebda93fb7e8cb7c3ff55", "path": "ResourceBundle-Promises_Privacy-PromisesSwift-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f6e332acd38d9134f3c0be577189ce95", "name": "Support Files", "path": "../Target Support Files/PromisesSwift", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884736b7613b012b96d2388799a977774", "name": "PromisesSwift", "path": "PromisesSwift", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98349754cc849ba5c1efb8320b19b53c89", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98ca328e25754bf77e01a819c4d2c7c3c7", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/Desktop/Foton/j17bank-mybank-app/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/Foton/j17bank-mybank-app/ios/Pods", "targets": ["TARGET@v11_hash=15057efef18e58047fe22a657eac2886", "TARGET@v11_hash=963095dd76339eff28f5a2d2d4120470", "TARGET@v11_hash=604ebe021c9b7acc95f47634513d409e", "TARGET@v11_hash=b8376783924f6f88ad81ba5ae98eeacd", "TARGET@v11_hash=067b82ef44ef5a7a9f662d9a8c8be6bb", "TARGET@v11_hash=6c7c8843306a39ff46104142f904a86f", "TARGET@v11_hash=c4573f02e096f4c125a5c2f95c22ab9f", "TARGET@v11_hash=9ae187a3eba64989e877ca76b1319772", "TARGET@v11_hash=ee0adbe3614aaf332016a24975181ff0", "TARGET@v11_hash=e400ca109eb818a901e707f62816b270", "TARGET@v11_hash=531fd903aa2c1c9fc51d6346b4f6b161", "TARGET@v11_hash=c6e8b7281ba785f2fdb1e841f9ac6db1", "TARGET@v11_hash=b17a898119c054fc60b88620477b8d81", "TARGET@v11_hash=b28b9f7e25a6f48362f20a31c2bb753e", "TARGET@v11_hash=b575aa4664d86760c755c734565442f3", "TARGET@v11_hash=df6e5b2a9802e16145fadf919186d058", "TARGET@v11_hash=ec02f8a6df2538c4e5cd683c9cf85695", "TARGET@v11_hash=e79ed835a4fb11f0bbfcc953c0302baf", "TARGET@v11_hash=db619923d516509311a15855d1fdcdff", "TARGET@v11_hash=66cb3e74e6ab7a5f421e24827efef916", "TARGET@v11_hash=15628a9c5d64c5381849dec64621464a", "TARGET@v11_hash=395f34bfca098653c1c1840c4c9a33e5", "TARGET@v11_hash=00249f38ccb9baca5fb7b72381c90717", "TARGET@v11_hash=b6b1cb21b2d9f739dd7f9787e895e2a2", "TARGET@v11_hash=476742fcaab3285b7efe8e93ac96c6de", "TARGET@v11_hash=cb94d398db63a603b22ea65db137df42", "TARGET@v11_hash=a08676522ae0b9e261fe7cb3dd5789d2", "TARGET@v11_hash=03cbd5fa07487c0d23ff01b321de7eab", "TARGET@v11_hash=cfd80e734725a9ed2f56a370c881a0ad", "TARGET@v11_hash=390c67660535e0814b078563f7130778", "TARGET@v11_hash=caa7a011f9d18bd131493ae44c472da5", "TARGET@v11_hash=66b517be2e44d512486cc8cc6b6abeff", "TARGET@v11_hash=fa842762080c6b00f7b0a8115105afb2", "TARGET@v11_hash=7687c97ea9452909fa80ee1b02a18103", "TARGET@v11_hash=3edaf11d8a61a09078d3e0e7d2c60e72", "TARGET@v11_hash=3c329086386050799c37238e98440d98", "TARGET@v11_hash=f947ba8b3aa2a9c0d529734a40d23869", "TARGET@v11_hash=570d11e1d1ed529f34bf967125e9461d", "TARGET@v11_hash=a3448bbc70028910a3e911895c7f8898", "TARGET@v11_hash=1ebcb76733dfe8eb144eb8f13ca3f085", "TARGET@v11_hash=e9c627cc5c8dc17a7621724694ac1efd", "TARGET@v11_hash=2b378bf224c820d4824c53a3a037d782", "TARGET@v11_hash=df5fbffd096203489090e62dbf48efbe", "TARGET@v11_hash=1c979ebf869824ce97476ba368476da5", "TARGET@v11_hash=45b8c8a2e0d6fc792ef6ac8d74fd8538", "TARGET@v11_hash=a3a915fdc63173795301776db489bf5a", "TARGET@v11_hash=746f9d2cd803ff02e2091040190f53df", "TARGET@v11_hash=e0d8944f1a67d77b4cf0d540968b3960", "TARGET@v11_hash=662637004ccda379ddc36370d858812b", "TARGET@v11_hash=2ea23463da6823b088b54776bcaa7828", "TARGET@v11_hash=f847983c0fda5cd810f6bf256f9f24d0", "TARGET@v11_hash=47a5d57e98243150707f74ef7826ac38", "TARGET@v11_hash=57704642f2175b1803af836e5858de92", "TARGET@v11_hash=22087579a94b22e5bff7c0ec66fc35a4", "TARGET@v11_hash=e04c9fc9b537a641eab57b972387029e", "TARGET@v11_hash=d7d9b5d538234ba864b8f9ccf4609413", "TARGET@v11_hash=092d9b1b4017cf31b4afc64fb1cae983", "TARGET@v11_hash=eb4460c7bec738cde160e605132f2dd3", "TARGET@v11_hash=8e5288edac27140ebd7851ab2731df0f", "TARGET@v11_hash=07363d1f273fe84c3539f86bcf44537e", "TARGET@v11_hash=b962d61e601d201b18443e13aace27c2", "TARGET@v11_hash=0e8b14cdf1d857b2a793b5612972c31a", "TARGET@v11_hash=c47ce2b83dd4d7b3ea9dc9ebad80ff4f", "TARGET@v11_hash=51f76f1b6a0b7b1c38f78f826da74033", "TARGET@v11_hash=1265f55e50be55c79a8bfff8643e2f8b", "TARGET@v11_hash=592aa5a3eb79a56f123ed5a9db1eef46"]}