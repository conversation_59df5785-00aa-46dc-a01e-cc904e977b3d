<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">J17 Múltiplo</string>
    <string name="READ_PHONE_STATE">Required by Topaz fraud prevention system to generate unique device fingerprints for transaction security validation. This permission enables device identification without accessing personal information.</string>
    <string name="ACCESS_COARSE_LOCATION">Used to validate transaction locations and detect suspicious geographic patterns. Helps prevent unauthorized access from unusual locations without precise tracking.</string>
    <string name="ACCESS_FINE_LOCATION">Required for enhanced transaction security when precise location validation is needed for high-value transfers and account access from new devices.</string>
    <string name="READ_EXTERNAL_STORAGE">Enables users to upload identity documents (RG, CPF, proof of income) for account verification and attach transaction receipts. Files are processed locally and transmitted securely.</string>
    <string name="GET_ACCOUNTS">Used to detect multiple account registrations on the same device for fraud prevention. Helps identify potential account takeover attempts without accessing account credentials.</string>
    <string name="READ_CONTACTS">Enables PIX transfer functionality by allowing users to select contacts for money transfers. Contact data is processed locally and never stored on servers.</string>
    <string name="READ_CALL_LOG">Required by Topaz security system to analyze device usage patterns and detect SIM swap fraud attempts. No call content or personal information is accessed.</string>
    <string name="BIND_NOTIFICATION_LISTENER_SERVICE">Enables Topaz fraud prevention to monitor for malicious apps that might intercept SMS tokens or banking notifications. Critical for preventing account takeover attacks.</string>
    <string name="BLUETOOTH">Required for secure proximity-based authentication and to detect paired devices for enhanced security validation during sensitive operations.</string>
    <string name="POST_NOTIFICATIONS">Essential for sending real-time transaction alerts, security warnings, and account activity notifications to protect users from unauthorized access.</string>
    <string name="BLUETOOTH_CONNECT">Enables secure device pairing for proximity-based authentication and detection of trusted devices during transaction authorization.</string>
    <string name="CAMERA">Required for document scanning (ID verification), QR code reading for PIX payments, and facial recognition for biometric authentication during transactions.</string>
    <string name="RECORD_AUDIO">Used for voice biometric authentication as an additional security layer for high-value transactions and account access verification.</string>
    <string name="MODIFY_AUDIO_SETTINGS">Allows optimization of audio settings during voice authentication to ensure accurate biometric verification and reduce false rejections.</string>
    <string name="VIDEO_CAPTURE">Enables video-based identity verification for account opening and high-value transaction authorization through liveness detection.</string>
    <string name="AUDIO_CAPTURE">Required for voice biometric enrollment and authentication, providing an additional security factor for sensitive banking operations.</string>
    <string name="INTERNET">Essential for secure communication with J17 Bank servers, real-time transaction processing, account balance updates, and encrypted data synchronization.</string>
    <string name="USE_BIOMETRIC">Enables fingerprint and facial recognition authentication for secure app access, transaction authorization, and account protection without storing biometric data locally.</string>
    <string name="RECEIVE_BOOT_COMPLETED">Allows Topaz security system to resume fraud monitoring after device restart, ensuring continuous protection against unauthorized access attempts.</string>
    <string name="ACCESS_WIFI_STATE">Required to detect network type and quality for optimal transaction processing. Helps prevent transaction failures due to poor connectivity and enables secure network validation.</string>
    <string name="ACCESS_NETWORK_STATE">Essential for monitoring network connectivity status to ensure secure transaction completion and prevent data loss during banking operations.</string>
    <string name="QUERY_ALL_PACKAGES">Critical security feature that enables Topaz system to detect malicious banking trojans, overlay attacks, and fraudulent apps that target financial institutions.</string>
    <string name="HIDE_OVERLAY_WINDOWS">Advanced security protection that prevents malicious apps from displaying fake login screens or transaction confirmations over the banking app interface.</string>
</resources>
