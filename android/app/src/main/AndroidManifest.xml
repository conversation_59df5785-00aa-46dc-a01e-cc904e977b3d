<manifest xmlns:tools="http://schemas.android.com/tools" xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-feature android:name="android.hardware.camera"/>
    <uses-feature android:name="android.hardware.camera.autofocus"/>

    <uses-permission android:name="android.permission.INTERNET"
                     android:description="@string/INTERNET"/>
    <uses-permission android:name="android.permission.USE_BIOMETRIC"
                     android:description="@string/USE_BIOMETRIC"/>

    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"
                     android:description="@string/RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"
                     android:description="@string/ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"
                     android:description="@string/ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
                     android:description="@string/QUERY_ALL_PACKAGES"/>
    <uses-permission android:name="android.permission.HIDE_OVERLAY_WINDOWS"
                     android:description="@string/HIDE_OVERLAY_WINDOWS"
                     tools:ignore="ProtectedPermissions"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"
                     android:description="@string/READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"
                     android:description="@string/ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"
                     android:description="@string/ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
                     android:description="@string/READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.GET_ACCOUNTS"
                     android:description="@string/GET_ACCOUNTS"/>
    <uses-permission android:name="android.permission.READ_CONTACTS"
                     android:description="@string/READ_CONTACTS"/>
    <uses-permission android:name="android.permission.BLUETOOTH"
                     android:description="@string/BLUETOOTH"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"
                     android:description="@string/POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.READ_CALL_LOG"
                     android:description="@string/READ_CALL_LOG"/>
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"
                     android:description="@string/BLUETOOTH_CONNECT"/>
    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
                     android:description="@string/BIND_NOTIFICATION_LISTENER_SERVICE"
                     tools:ignore="ProtectedPermissions"/>

    <uses-permission android:name="android.permission.CAMERA"
                     android:description="@string/CAMERA"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"
                     android:description="@string/RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"
                     android:description="@string/MODIFY_AUDIO_SETTINGS"/>
    <uses-permission android:name="android.permission.VIDEO_CAPTURE"
                     android:description="@string/VIDEO_CAPTURE"/>
    <uses-permission android:name="android.permission.AUDIO_CAPTURE"
                     android:description="@string/AUDIO_CAPTURE"/>

    <application
            android:label="J17 Múltiplo"
            tools:replace="android:label"
            android:name="${applicationName}"
            android:icon="@mipmap/ic_launcher">
        <activity
                android:name=".MainActivity"
                android:exported="true"
                android:launchMode="singleTop"
                android:taskAffinity=""
                android:theme="@style/LaunchTheme"
                android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
                android:hardwareAccelerated="true"
                android:windowSoftInputMode="adjustResize">
            <meta-data
                    android:name="io.flutter.embedding.android.NormalTheme"
                    android:resource="@style/NormalTheme"
            />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <meta-data
                android:name="flutterEmbedding"
                android:value="2"/>
    </application>
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>
</manifest>