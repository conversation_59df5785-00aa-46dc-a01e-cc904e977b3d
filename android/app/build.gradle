plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    id 'com.google.firebase.firebase-perf'
    id 'com.google.firebase.crashlytics'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "br.com.j17multiplo"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId = "br.com.j17multiplo"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        debug {
            storeFile file(keystore)
            keyAlias 'j17bank-mybank-develop'
            keyPassword 'develop'
            storePassword 'develop'
        }
        release {
            storeFile file(keystore)
            keyAlias 'j17multiplo'
            keyPassword 'kSJ9XPZ2GRJI87R6'
            storePassword 'kSJ9XPZ2GRJI87R6'
        }
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.release

            minifyEnabled true
            shrinkResources true

            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source = "../.."
}
