# J17 Múltiplo - Justificativa Técnica de Permissões

## Resumo Executivo

O aplicativo J17 Múltiplo é uma solução bancária completa que utiliza o sistema de segurança Topaz para prevenção de fraudes. As permissões solicitadas são essenciais para garantir a segurança das transações financeiras e proteger os usuários contra ameaças cibernéticas.

## Permissões Críticas e Justificativas Técnicas

### Android

#### 1. Permissões de Segurança Avançada

**READ_PHONE_STATE**
- **Finalidade**: Geração de fingerprint único do dispositivo
- **Implementação**: Utilizado pelo sistema Topaz para criar identificadores únicos sem acessar informações pessoais
- **Segurança**: Não acessa números de telefone ou dados pessoais, apenas características técnicas do dispositivo
- **Conformidade**: Essencial para prevenção de fraudes bancárias

**QUERY_ALL_PACKAGES**
- **Finalidade**: Detecção de malware bancário
- **Implementação**: Identifica aplicativos maliciosos conhecidos que atacam instituições financeiras
- **Segurança**: Protege contra trojans bancários, overlays maliciosos e ataques de phishing
- **Conformidade**: Permissão protegida justificada para aplicações de segurança financeira

**HIDE_OVERLAY_WINDOWS**
- **Finalidade**: Proteção contra ataques de overlay
- **Implementação**: Impede que aplicativos maliciosos sobreponham telas falsas sobre o app bancário
- **Segurança**: Previne captura de credenciais e confirmações de transação falsas
- **Conformidade**: Permissão protegida essencial para segurança bancária

#### 2. Permissões de Localização

**ACCESS_FINE_LOCATION / ACCESS_COARSE_LOCATION**
- **Finalidade**: Validação geográfica de transações
- **Implementação**: Detecta acessos suspeitos de localizações incomuns
- **Segurança**: Previne fraudes por acesso não autorizado
- **Privacidade**: Dados processados localmente, não armazenados permanentemente

#### 3. Permissões de Dados do Dispositivo

**READ_CALL_LOG**
- **Finalidade**: Detecção de SIM swap
- **Implementação**: Analisa padrões de uso para identificar troca fraudulenta de chip
- **Segurança**: Não acessa conteúdo de chamadas, apenas metadados de uso
- **Conformidade**: Crítico para prevenção de fraudes bancárias

**READ_CONTACTS**
- **Finalidade**: Funcionalidade PIX
- **Implementação**: Permite seleção de contatos para transferências
- **Privacidade**: Dados processados localmente, nunca enviados para servidores
- **Funcionalidade**: Melhora experiência do usuário em transferências

**GET_ACCOUNTS**
- **Finalidade**: Detecção de múltiplas contas
- **Implementação**: Identifica tentativas de criação de contas duplicadas
- **Segurança**: Não acessa credenciais, apenas detecta padrões suspeitos
- **Conformidade**: Prevenção de fraudes por múltiplas contas

#### 4. Permissões de Monitoramento

**BIND_NOTIFICATION_LISTENER_SERVICE**
- **Finalidade**: Proteção contra interceptação de tokens SMS
- **Implementação**: Monitora tentativas de captura de códigos de autenticação
- **Segurança**: Detecta aplicativos maliciosos que interceptam notificações bancárias
- **Conformidade**: Essencial para proteção de autenticação de dois fatores

#### 5. Permissões Multimídia

**CAMERA**
- **Finalidade**: Múltiplas funcionalidades de segurança
- **Implementação**: 
  - Escaneamento de documentos para verificação de identidade
  - Leitura de QR codes para PIX
  - Reconhecimento facial para autenticação biométrica
- **Segurança**: Dados processados localmente com criptografia

**RECORD_AUDIO / AUDIO_CAPTURE**
- **Finalidade**: Autenticação biométrica por voz
- **Implementação**: Verificação de identidade por padrões vocais
- **Privacidade**: Dados biométricos não armazenados permanentemente
- **Segurança**: Camada adicional de autenticação para transações de alto valor

### iOS

#### Permissões Específicas do iOS

**NSFaceIDUsageDescription**
- **Finalidade**: Autenticação biométrica segura
- **Implementação**: Utiliza Face ID nativo do iOS
- **Privacidade**: Dados biométricos permanecem no Secure Enclave
- **Conformidade**: Segue diretrizes de privacidade da Apple

**NSLocationWhenInUseUsageDescription**
- **Finalidade**: Validação geográfica de transações
- **Implementação**: Acesso apenas durante uso ativo do app
- **Privacidade**: Localização não armazenada permanentemente
- **Segurança**: Detecta acessos suspeitos de locais incomuns

## Medidas de Privacidade Implementadas

### Proteção de Dados
1. **Criptografia End-to-End**: Todos os dados sensíveis são criptografados
2. **Processamento Local**: Dados biométricos e pessoais processados localmente
3. **Não Armazenamento**: Informações sensíveis não são armazenadas permanentemente
4. **Anonização**: Dados de telemetria são anonimizados antes do envio

### Conformidade Regulatória
1. **LGPD**: Conformidade com Lei Geral de Proteção de Dados
2. **BACEN**: Atende regulamentações do Banco Central do Brasil
3. **PCI DSS**: Conformidade com padrões de segurança de cartões
4. **ISO 27001**: Implementação de controles de segurança da informação

## Alternativas Consideradas

### Permissões Removidas ou Reduzidas
1. **Localização em Background**: Removida para melhorar privacidade
2. **Acesso Total ao Armazenamento**: Limitado apenas a documentos específicos
3. **Microfone Sempre Ativo**: Restrito apenas durante autenticação

### Implementações Opcionais
1. **Funcionalidades Degradadas**: App funciona com permissões limitadas
2. **Solicitação Contextual**: Permissões solicitadas apenas quando necessárias
3. **Explicações Claras**: Interface explica o uso de cada permissão

## Casos de Uso Específicos

### Fluxo de Autenticação
1. **Biometria**: Face ID/Fingerprint para acesso inicial
2. **Localização**: Validação geográfica para novos dispositivos
3. **Dispositivo**: Fingerprint único para detecção de fraudes

### Fluxo de Transações
1. **Câmera**: QR codes para PIX, documentos para comprovantes
2. **Localização**: Validação de transações de alto valor
3. **Notificações**: Alertas em tempo real de atividades

### Fluxo de Segurança
1. **Monitoramento**: Detecção de aplicativos maliciosos
2. **Overlay Protection**: Prevenção de ataques de sobreposição
3. **SIM Swap Detection**: Identificação de troca fraudulenta de chip

## Conclusão

Todas as permissões solicitadas são essenciais para o funcionamento seguro do aplicativo bancário J17 Múltiplo. O sistema Topaz de prevenção de fraudes requer essas permissões para proteger efetivamente os usuários contra ameaças cibernéticas sofisticadas que visam especificamente aplicações financeiras.

As implementações seguem as melhores práticas de privacidade e segurança, com processamento local de dados sensíveis e conformidade com regulamentações nacionais e internacionais.
