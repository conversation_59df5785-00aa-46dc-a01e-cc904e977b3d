import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';

/// Componente universal de informações do recebedor com valor máximo
/// Pode ser usado em qualquer jornada de pagamento PIX
class RecipientInfoCard extends StatefulWidget {
  final String receiverName;
  final String receiverDocument;
  final double? maxValue;
  final bool showMaxValueEditor;
  final Function(double?)? onMaxValueChanged;
  final Function(bool)? onMaxValueToggleChanged;

  const RecipientInfoCard({
    super.key,
    required this.receiverName,
    required this.receiverDocument,
    this.maxValue,
    this.showMaxValueEditor = true,
    this.onMaxValueChanged,
    this.onMaxValueToggleChanged,
  });

  @override
  State<RecipientInfoCard> createState() => _RecipientInfoCardState();
}

class _RecipientInfoCardState extends State<RecipientInfoCard> {
  bool _isMaxValueEnabled = false;
  late TextEditingController _maxValueController;

  @override
  void initState() {
    super.initState();
    _isMaxValueEnabled = widget.maxValue != null;
    _maxValueController = TextEditingController(
      text: widget.maxValue != null ? _formatCurrency(widget.maxValue!) : '',
    );
  }

  @override
  void dispose() {
    _maxValueController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Card com informações do recebedor
        Container(
          margin: const EdgeInsets.only(bottom: J17Padding.regular),
          padding: const EdgeInsets.all(J17Padding.regular),
          decoration: BoxDecoration(
            color: J17ThemeColor.surfacesElevate.color(context),
            borderRadius: BorderRadius.circular(J17BorderRadius.small),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgIcon(
                icon: J17Icons.building,
                color: J17ThemeColor.textPrimaryColor,
                size: J17IconSizes.regSmall,
              ),
              const SizedBox(width: J17Padding.regular),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Informações do recebedor',
                      style: J17TextStyles.titleSmall(bold: true)
                          .textStyle(context),
                    ),
                    const SizedBox(height: J17Padding.small),
                    Text(
                      widget.receiverName,
                      style: J17TextStyles.titleMedium(bold: true)
                          .textStyle(context),
                    ),
                    Text(
                      'CNPJ: ${widget.receiverDocument}',
                      style: J17TextStyles.bodyMedium().textStyle(context),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Card com valor máximo (se habilitado)
        if (widget.showMaxValueEditor) ...[
          Container(
            margin: const EdgeInsets.only(bottom: J17Padding.regular),
            padding: const EdgeInsets.all(J17Padding.regular),
            decoration: BoxDecoration(
              color: J17ThemeColor.surfacesElevate.color(context),
              borderRadius: BorderRadius.circular(J17BorderRadius.small),
            ),
            child: Row(
              children: [
                SvgIcon(
                  icon: J17Icons.user,
                  color: J17ThemeColor.textPrimaryColor,
                  size: J17IconSizes.regSmall,
                ),
                const SizedBox(width: J17Padding.regular),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Informações do devedor',
                        style: J17TextStyles.titleSmall(bold: true)
                            .textStyle(context),
                      ),
                      const SizedBox(height: J17Padding.small),
                      Text(
                        'Matheus Chies de Souza',
                        style: J17TextStyles.titleMedium(bold: true)
                            .textStyle(context),
                      ),
                      Text(
                        'CPF: ***.805.***-48',
                        style: J17TextStyles.bodyMedium().textStyle(context),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Switch para editar valor máximo
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: J17Padding.regular,
              vertical: J17Padding.small,
            ),
            decoration: BoxDecoration(
              color: J17ThemeColor.surfacesElevate.color(context),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const SizedBox(width: J17Padding.regular),
                Expanded(
                  child: Text(
                    'Editar valor máximo',
                    style: J17TextStyles.bodyLarge().textStyle(context),
                  ),
                ),
                Switch(
                  value: _isMaxValueEnabled,
                  onChanged: (value) {
                    setState(() {
                      _isMaxValueEnabled = value;
                      if (!value) {
                        _maxValueController.clear();
                        widget.onMaxValueChanged?.call(null);
                      } else {
                        double defaultValue = widget.maxValue ?? 500.0;
                        _maxValueController.text =
                            _formatCurrency(defaultValue);
                        widget.onMaxValueChanged?.call(defaultValue);
                      }
                    });
                    widget.onMaxValueToggleChanged?.call(value);
                  },
                ),
              ],
            ),
          ),

          // Campo de valor máximo quando habilitado
          if (_isMaxValueEnabled) ...[
            const SizedBox(height: J17Padding.regular),
            Container(
              padding: const EdgeInsets.all(J17Padding.regular),
              decoration: BoxDecoration(
                color: J17ThemeColor.surfacesElevate.color(context),
                borderRadius: BorderRadius.circular(J17BorderRadius.small),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Valor máximo',
                    style: J17TextStyles.bodyLarge().textStyle(context),
                  ),
                  Text(
                    'R\$ ${widget.maxValue?.toStringAsFixed(2).replaceAll('.', ',') ?? '500,00'}',
                    style:
                        J17TextStyles.bodyLarge(bold: true).textStyle(context),
                  ),
                ],
              ),
            ),
          ],
        ],
      ],
    );
  }

  String _formatCurrency(double value) {
    return value.toStringAsFixed(2).replaceAll('.', ',');
  }
}
