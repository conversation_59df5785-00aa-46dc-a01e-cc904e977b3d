import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';

class CardEditarValorMaximo extends StatelessWidget {
  final bool enabled;
  final ValueChanged<bool> onChanged;
  final String value;
  final ValueChanged<String> onValueChanged;
  final String? errorText;
  final bool readOnly;

  const CardEditarValorMaximo({
    super.key,
    required this.enabled,
    required this.onChanged,
    required this.value,
    required this.onValueChanged,
    this.errorText,
    this.readOnly = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: J17ThemeColor.surfacesElevate.color(context),
        borderRadius: BorderRadius.circular(J17Padding.regular),
      ),
      padding: const EdgeInsets.all(J17Padding.regular),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Editar valor máximo',
                  style: J17TextStyles.titleMedium(
                    bold: true,
                  ).textStyle(context),
                ),
              ),
              Switch(
                value: enabled,
                onChanged: readOnly ? null : onChanged,
                activeColor: J17ThemeColor.actionPrimaryColor.color(context),
              ),
            ],
          ),
          if (enabled) ...[
            const SizedBox(height: J17Padding.large),
            TextField(
              enabled: !readOnly,
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: 'R\$ 00,00',
                hintStyle: J17TextStyles.titleLarge(bold: true)
                    .textStyle(context)
                    .copyWith(
                      color: J17ThemeColor.textTextErrors.color(context),
                    ),
                isDense: true,
                contentPadding: EdgeInsets.zero,
              ),
              style: J17TextStyles.titleLarge(bold: true)
                  .textStyle(context)
                  .copyWith(
                    color: J17ThemeColor.textTextErrors.color(context),
                  ),
              controller: TextEditingController(text: value),
              onChanged: onValueChanged,
            ),
            Container(
              height: J17Padding.two,
              color: J17ThemeColor.textTextErrors.color(context),
              margin: const EdgeInsets.only(
                top: J17Padding.two,
                bottom: J17Padding.smallest,
              ),
            ),
            if (errorText != null && errorText!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(
                  bottom: J17Padding.regular,
                ),
                child: Text(
                  errorText!,
                  style: J17TextStyles.bodyMedium().textStyle(context).copyWith(
                        color: J17ThemeColor.textTextErrors.color(context),
                      ),
                ),
              ),
            Text(
              'Caso o valor do pagamento recorrente seja superior ao valor máximo definido, o pagamento não será agendado e você será avisado.\n\nEsta autorização prevê tentativas de pagamentos após a data de vencimento, caso o pagamento original não possa ser realizado por insuficiência de saldo.\n\nPagamentos realizados após a data de vencimento podem resultar em juros e multas acrescidos ao pagamento seguinte.',
              style: J17TextStyles.bodyLarge().textStyle(context),
            ),
          ],
        ],
      ),
    );
  }
}
