import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';

/// Componente simples que pode ser usado diretamente no lugar do código existente
/// que usa _getInfoTitle() e _getInfoDescription()
class AuthorizationInfoComponent extends StatefulWidget {
  final String? jornada;
  final double? maxValue;
  final bool showMaxValueEditor;
  final Function(bool)? onMaxValueToggleChanged;

  const AuthorizationInfoComponent({
    super.key,
    this.jornada,
    this.maxValue,
    this.showMaxValueEditor = true,
    this.onMaxValueToggleChanged,
  });

  @override
  State<AuthorizationInfoComponent> createState() =>
      _AuthorizationInfoComponentState();
}

class _AuthorizationInfoComponentState
    extends State<AuthorizationInfoComponent> {
  bool _isMaxValueEnabled = false;

  @override
  void initState() {
    super.initState();
    _isMaxValueEnabled = widget.maxValue != null;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Card com informações do recebedor
        Container(
          margin: const EdgeInsets.only(bottom: J17Padding.regular),
          padding: const EdgeInsets.all(J17Padding.regular),
          decoration: BoxDecoration(
            color: J17ThemeColor.surfacesElevate.color(context),
            borderRadius: BorderRadius.circular(J17BorderRadius.small),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgIcon(
                icon: J17Icons.building,
                color: J17ThemeColor.textPrimaryColor,
                size: J17IconSizes.regSmall,
              ),
              const SizedBox(width: J17Padding.regular),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Informações do recebedor',
                      style: J17TextStyles.titleSmall(bold: true)
                          .textStyle(context),
                    ),
                    const SizedBox(height: J17Padding.small),
                    Text(
                      'Enerbras',
                      style: J17TextStyles.titleMedium(bold: true)
                          .textStyle(context),
                    ),
                    Text(
                      'CNPJ: 02.616.999/0002-25',
                      style: J17TextStyles.bodyMedium().textStyle(context),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Card com informações do devedor
        Container(
          margin: const EdgeInsets.only(bottom: J17Padding.regular),
          padding: const EdgeInsets.all(J17Padding.regular),
          decoration: BoxDecoration(
            color: J17ThemeColor.surfacesElevate.color(context),
            borderRadius: BorderRadius.circular(J17BorderRadius.small),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SvgIcon(
                icon: J17Icons.user,
                color: J17ThemeColor.textPrimaryColor,
                size: J17IconSizes.regSmall,
              ),
              const SizedBox(width: J17Padding.regular),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Informações do devedor',
                      style: J17TextStyles.titleSmall(bold: true)
                          .textStyle(context),
                    ),
                    const SizedBox(height: J17Padding.small),
                    Text(
                      'Matheus Chies de Souza',
                      style: J17TextStyles.titleMedium(bold: true)
                          .textStyle(context),
                    ),
                    Text(
                      'CPF: ***.805.***-48',
                      style: J17TextStyles.bodyMedium().textStyle(context),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Switch para editar valor máximo (se habilitado)
        if (widget.showMaxValueEditor) ...[
          Container(
            margin: const EdgeInsets.only(bottom: J17Padding.regular),
            padding: const EdgeInsets.symmetric(
              horizontal: J17Padding.regular,
              vertical: J17Padding.small,
            ),
            decoration: BoxDecoration(
              color: J17ThemeColor.surfacesElevate.color(context),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const SizedBox(width: J17Padding.regular),
                Expanded(
                  child: Text(
                    'Editar valor máximo',
                    style: J17TextStyles.bodyLarge().textStyle(context),
                  ),
                ),
                Switch(
                  value: _isMaxValueEnabled,
                  onChanged: (value) {
                    setState(() {
                      _isMaxValueEnabled = value;
                    });
                    widget.onMaxValueToggleChanged?.call(value);
                  },
                ),
              ],
            ),
          ),

          // Exibição do valor máximo quando habilitado
          if (_isMaxValueEnabled) ...[
            Container(
              margin: const EdgeInsets.only(bottom: J17Padding.regular),
              padding: const EdgeInsets.all(J17Padding.regular),
              decoration: BoxDecoration(
                color: J17ThemeColor.surfacesElevate.color(context),
                borderRadius: BorderRadius.circular(J17BorderRadius.small),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Valor máximo',
                    style: J17TextStyles.bodyLarge().textStyle(context),
                  ),
                  Text(
                    'R\$ ${_formatCurrency(widget.maxValue ?? 500.0)}',
                    style:
                        J17TextStyles.bodyLarge(bold: true).textStyle(context),
                  ),
                ],
              ),
            ),
          ],
        ],
      ],
    );
  }

  String _formatCurrency(double value) {
    return value.toStringAsFixed(2).replaceAll('.', ',');
  }
}
