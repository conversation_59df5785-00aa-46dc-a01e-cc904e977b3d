import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';
import 'package:j17_bank_mybank_mobile/view/components/list/base/base_row_component.dart';
import 'package:j17_bank_mybank_mobile/view/components/list/base/row_model.dart';

/// Model para o componente de informações do recebedor e devedor
class RecipientDebtorInfoUIModel extends RowModel {
  final String receiverName;
  final String receiverDocument;
  final String debtorName;
  final String debtorDocument;
  final double? maxValue;
  final bool showMaxValueEditor;

  RecipientDebtorInfoUIModel({
    required this.receiverName,
    required this.receiverDocument,
    required this.debtorName,
    required this.debtorDocument,
    this.maxValue,
    this.showMaxValueEditor = true,
  });
}

/// Componente que exibe informações do recebedor e devedor com valor máximo
class RecipientDebtorInfoItem
    extends BaseRowStatefulWidget<RecipientDebtorInfoUIModel> {
  const RecipientDebtorInfoItem({
    super.key,
    required super.model,
    super.listener,
  });

  @override
  State<RecipientDebtorInfoItem> createState() =>
      _RecipientDebtorInfoItemState();
}

class _RecipientDebtorInfoItemState extends State<RecipientDebtorInfoItem> {
  bool _isMaxValueEnabled = false;

  @override
  void initState() {
    super.initState();
    _isMaxValueEnabled = widget.model.maxValue != null;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: J17Padding.regular),
      child: Column(
        children: [
          // Card com informações do recebedor
          Container(
            margin: const EdgeInsets.only(bottom: J17Padding.regular),
            padding: const EdgeInsets.all(J17Padding.regular),
            decoration: BoxDecoration(
              color: J17ThemeColor.surfacesElevate.color(context),
              borderRadius: BorderRadius.circular(J17BorderRadius.small),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SvgIcon(
                  icon: J17Icons.building,
                  color: J17ThemeColor.textPrimaryColor,
                  size: J17IconSizes.regSmall,
                ),
                const SizedBox(width: J17Padding.regular),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Informações do recebedor',
                        style: J17TextStyles.titleSmall(bold: true)
                            .textStyle(context),
                      ),
                      const SizedBox(height: J17Padding.small),
                      Text(
                        widget.model.receiverName,
                        style: J17TextStyles.titleMedium(bold: true)
                            .textStyle(context),
                      ),
                      Text(
                        'CNPJ: ${widget.model.receiverDocument}',
                        style: J17TextStyles.bodyMedium().textStyle(context),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Card com informações do devedor
          Container(
            margin: const EdgeInsets.only(bottom: J17Padding.regular),
            padding: const EdgeInsets.all(J17Padding.regular),
            decoration: BoxDecoration(
              color: J17ThemeColor.surfacesElevate.color(context),
              borderRadius: BorderRadius.circular(J17BorderRadius.small),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SvgIcon(
                  icon: J17Icons.user,
                  color: J17ThemeColor.textPrimaryColor,
                  size: J17IconSizes.regSmall,
                ),
                const SizedBox(width: J17Padding.regular),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Informações do devedor',
                        style: J17TextStyles.titleSmall(bold: true)
                            .textStyle(context),
                      ),
                      const SizedBox(height: J17Padding.small),
                      Text(
                        widget.model.debtorName,
                        style: J17TextStyles.titleMedium(bold: true)
                            .textStyle(context),
                      ),
                      Text(
                        'CPF: ${widget.model.debtorDocument}',
                        style: J17TextStyles.bodyMedium().textStyle(context),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Switch para editar valor máximo (se habilitado)
          if (widget.model.showMaxValueEditor) ...[
            Container(
              margin: const EdgeInsets.only(bottom: J17Padding.regular),
              padding: const EdgeInsets.symmetric(
                horizontal: J17Padding.regular,
                vertical: J17Padding.small,
              ),
              decoration: BoxDecoration(
                color: J17ThemeColor.surfacesElevate.color(context),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  const SizedBox(width: J17Padding.regular),
                  Expanded(
                    child: Text(
                      'Editar valor máximo',
                      style: J17TextStyles.bodyLarge().textStyle(context),
                    ),
                  ),
                  Switch(
                    value: _isMaxValueEnabled,
                    onChanged: (value) {
                      setState(() {
                        _isMaxValueEnabled = value;
                      });
                    },
                  ),
                ],
              ),
            ),

            // Exibição do valor máximo quando habilitado
            if (_isMaxValueEnabled && widget.model.maxValue != null) ...[
              Container(
                margin: const EdgeInsets.only(bottom: J17Padding.regular),
                padding: const EdgeInsets.all(J17Padding.regular),
                decoration: BoxDecoration(
                  color: J17ThemeColor.surfacesElevate.color(context),
                  borderRadius: BorderRadius.circular(J17BorderRadius.small),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Valor máximo',
                      style: J17TextStyles.bodyLarge().textStyle(context),
                    ),
                    Text(
                      'R\$ ${_formatCurrency(widget.model.maxValue!)}',
                      style: J17TextStyles.bodyLarge(bold: true)
                          .textStyle(context),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ],
      ),
    );
  }

  String _formatCurrency(double value) {
    return value.toStringAsFixed(2).replaceAll('.', ',');
  }
}
