import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/view/pages/error_component/error_component_parametrization.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';

class TransactionalPasswordBlockedPage extends StatelessWidget {
  final String message;

  const TransactionalPasswordBlockedPage({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorComponentParametrization(
      showPrimaryButton: false,
      showCancelButton: true,
      pageTitle: 'Senha de transação',
      pageDescriptionText: message,
      pageIcon: J17Icons.danger,
      cancelButtonText: 'Voltar',
      cancelButtonOnPressed: () {
        Navigator.pop(context);
      },
    );
  }
}
