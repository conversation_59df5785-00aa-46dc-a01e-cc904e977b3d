import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/view/pages/error_component/error_component_parametrization.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';

class TransactionalPasswordRequiredPage extends StatelessWidget {
  final VoidCallback onDefinePassword;
  final VoidCallback onSkip;

  const TransactionalPasswordRequiredPage({
    super.key,
    required this.onDefinePassword,
    required this.onSkip,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorComponentParametrization(
      showPrimaryButton: true,
      showCancelButton: true,
      pageTitle: 'Senha de transação',
      pageDescriptionText:
          'Para sua segurança, é necessário cadastrar uma senha de transação para acessar o Pix. Você pode definir agora ou deixar para depois.',
      pageIcon: J17Icons.passwordCheck,
      buttonText: 'Definir senha',
      onPressed: onDefinePassword,
      cancelButtonText: 'Deixar para depois',
      cancelButtonOnPressed: onSkip,
    );
  }
}
