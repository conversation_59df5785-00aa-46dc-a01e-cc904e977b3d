import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';
import 'package:j17_bank_mybank_mobile/view/pages/device_registration/device_registration_tab.dart';

Future<bool> openDeviceRegistration(BuildContext context,
    {required String cpf}) async {
  final result = await Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => DeviceRegistrationPage(cpf: cpf),
    ),
  );

  return result ?? false;
}

class DeviceRegistrationPage extends StatelessWidget {
  const DeviceRegistrationPage({super.key, required this.cpf});

  final String cpf;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const SvgIcon(icon: J17Icons.arrowLeftCustom),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(
          "Código de segurança",
          style: J17TextStyles.headlineSmall().textStyle(context),
        ),
      ),
      body: SafeArea(
        child: DeviceRegistrationTab(
          cpf: cpf,
          onValidated: () {
            Navigator.pop(context, true);
          },
        ),
      ),
    );
  }
}
