import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';
import 'package:j17_bank_mybank_mobile/view/components/field/j17_otp_field.dart';
import 'package:j17_bank_mybank_mobile/view/components/form/form_builder.dart';
import 'package:j17_bank_mybank_mobile/view/components/form/validation.dart';
import 'package:j17_bank_mybank_mobile/view/components/loading/loading_modal.dart';
import 'package:j17_bank_mybank_mobile/view/pages/first_access/components/token_activation_error_page.dart';
import 'package:j17_bank_mybank_mobile/viewmodel/first_access_viewmodel.dart';
import 'package:j17_bank_mybank_mobile/viewmodel/security/device_authorizer_viewmodel.dart';
import 'package:provider/provider.dart';

import '../first_access/components/first_access_commands.dart';
import 'package:j17_bank_mybank_mobile/utils/secure_logger.dart';

class TokenActivationState {
  String? otp;
  bool? invalid;
}

class DeviceRegistrationTab extends StatefulWidget {
  const DeviceRegistrationTab(
      {super.key, required this.cpf, required this.onValidated});

  final String cpf;
  final VoidCallback onValidated;

  @override
  State<DeviceRegistrationTab> createState() => _DeviceRegistrationTabState();
}

class _DeviceRegistrationTabState extends State<DeviceRegistrationTab>
    with TickerProviderStateMixin {
  late TabController _controller;

  @override
  void initState() {
    super.initState();

    _controller = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    super.dispose();

    _controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: ChangeNotifierProvider(
              create: (_) => DeviceAuthorizerViewmodel(cpf: widget.cpf),
              builder: (context, _) {
                return TabBarView(
                  controller: _controller,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    _MessageStep(
                      controller: _controller,
                      onError: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                const TokenActivationErrorPage(),
                          ),
                        );
                      },
                    ),
                    _OtpStep(
                      onValidated: widget.onValidated,
                    ),
                  ],
                );
              }),
        ),
      ],
    );
  }
}

class _MessageStep extends StatelessWidget {
  const _MessageStep({
    required this.controller,
    required this.onError,
  });

  final TabController controller;
  final VoidCallback onError;

  @override
  Widget build(BuildContext context) {
    var viewmodel =
        Provider.of<DeviceAuthorizerViewmodel>(context, listen: false);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: J17Padding.large) +
          const EdgeInsets.only(bottom: J17Padding.large),
      child: Column(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SvgIcon(
                  icon: J17Icons.keySquare,
                  size: J17IconSizes.huge,
                ),
                const SizedBox(height: J17Padding.small),
                Text(
                  "Antes de acessar sua conta, é necessário ativar o token de segurança neste aparelho. Essa medida adiciona uma camada extra de proteção para todas as suas transações realizadas no aplicativo.",
                  textAlign: TextAlign.left,
                  style: J17TextStyles.bodyLarge().textStyle(context),
                ),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              var doLoading = loading(context, shouldShowErrorMessage: false);

              try {
                await doLoading.around(viewmodel.sendOtp());
                controller.animateTo(1);
              } catch (ex) {
                print("Erro ao registrar o token: $ex");
                onError();
              }
            },
            child: Text(
              "Ativar token de segurança",
              style: J17TextStyles.bodyLarge(
                color: J17ThemeColor.textLabelButtons,
              ).textStyle(context),
            ),
          ),
        ],
      ),
    );
  }
}

class _OtpStep extends StatelessWidget {
  const _OtpStep({required this.onValidated});

  final VoidCallback onValidated;

  @override
  Widget build(BuildContext context) {
    var viewmodel =
        Provider.of<DeviceAuthorizerViewmodel>(context, listen: false);

    return FormBuilder(
      initialState: TokenActivationState(),
      builder: (context, form, state, onChange) {
        return Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(horizontal: J17Padding.regular),
                child: Column(
                  children: [
                    Text(
                      "Para ativar o token de segurança, enviamos uma mensagem de texto (sms) para o seu número cadastrado.",
                      style: J17TextStyles.bodyLarge().textStyle(context),
                    ),
                    const SizedBox(height: J17Padding.large),
                    J17OtpFormField(
                      sender: viewmodel.sendOtp,
                      validator: validate([
                        notEmpty(),
                        _codigoValido(state),
                      ]),
                      onComplete: (value) {
                        // Habilita o botão "Continuar" quando o código está completo
                      },
                      onChanged: onChange((otp) {
                        state.otp = otp;
                        state.invalid = false;
                      }),
                    ),
                    const SizedBox(height: J17Padding.small),
                    TextButton(
                      onPressed: () async {
                        try {
                          await viewmodel.sendOtp();
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Código reenviado com sucesso!'),
                              backgroundColor: Colors.green,
                            ),
                          );
                        } catch (e) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Erro ao reenviar código: $e'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      },
                      child: Text(
                        'Reenviar código',
                        style: J17TextStyles.bodyMedium(
                          color: J17ThemeColor.textSecondaryColor,
                        ).textStyle(context),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            FirstAccessCommands(
              progressLabel: "Continuar",
              isEnabled: state.otp?.length == 6,
              onProgress: _submit(context, form, state),
            ),
          ],
        );
      },
    );
  }

  _submit(BuildContext context, FormState? form, TokenActivationState state) {
    var viewmodel =
        Provider.of<DeviceAuthorizerViewmodel>(context, listen: false);

    return () async {
      if (form!.validate()) {
        try {
          // Mostrar loading manualmente sem timeout para evitar que volte automaticamente
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => const LoadingModal(),
          );

          await viewmodel.validaOtp(state.otp!);

          // Fechar loading manualmente
          if (context.mounted) {
            Navigator.of(context).pop();
          }

          onValidated();
        } catch (ex) {
          // Fechar loading em caso de erro
          if (context.mounted) {
            Navigator.of(context).pop();
          }

          SecureLogger.error("Erro ao autorizar o token: $ex");

          // ✅ MELHORADO: Tratamento específico de erros
          String errorMessage;
          if (ex.toString().contains('timeout') ||
              ex.toString().contains('demorou muito')) {
            errorMessage =
                "O processo de verificação está demorando mais que o esperado. Aguarde alguns instantes e tente novamente.";
          } else if (ex.toString().contains('inválido') ||
              ex.toString().contains('expirado')) {
            errorMessage =
                "Código inválido ou expirado. Verifique e tente novamente.";
          } else if (ex.toString().contains('conexão') ||
              ex.toString().contains('internet')) {
            errorMessage =
                "Problema de conexão. Verifique sua internet e tente novamente.";
          } else {
            errorMessage = "Erro ao validar código. Tente novamente.";
          }

          // Mostrar mensagem de erro para o usuário
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(errorMessage),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 4),
              ),
            );
          }

          state.invalid = true;
          form.validate();
        }
      }
    };
  }

  _codigoValido(TokenActivationState state) {
    return (otp) {
      if (state.invalid ?? false) {
        return "Código inválido!";
      }
      return null;
    };
  }
}
