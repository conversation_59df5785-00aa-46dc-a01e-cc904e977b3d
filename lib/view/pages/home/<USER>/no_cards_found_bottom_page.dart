import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/view/components/card/bottom_card_general.dart';
import 'package:j17_bank_mybank_mobile/view/components/card/top_card_general.dart';
import 'package:j17_bank_mybank_mobile/view/components/dynamic_tile/dynamic_tile.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';

// ignore: camel_case_types
class NoCardsFound_BottomPage {
  static criar(
      {required BuildContext context, required PreferredSizeWidget appBar}) {
    return Scaffold(
      appBar: appBar,
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(J17Padding.regular),
        child: Safe<PERSON>rea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(height: J17Padding.regular),
              Opacity(
                opacity: 0.5,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Column(
                      children: [
                        TopCardGeneral(
                            showVisibilityIcon: false, isBlocked: true),
                        SizedBox(height: J17Padding.regular),
                        BottomCardGeneral(),
                      ],
                    ),
                    SizedBox(width: J17Padding.regular),
                    Expanded(
                      child: Image(
                        image: j17CardOne,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: J17Padding.regular),
              DynamicTile(
                title:
                    "Não há nenhum cartão contratado ou vinculado ao seu CPF.",
                leadingIcon: J17Icons.closeCircle,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
