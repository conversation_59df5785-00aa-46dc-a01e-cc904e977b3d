import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';
import 'package:j17_bank_mybank_mobile/utils/double_extension.dart';
import 'package:j17_bank_mybank_mobile/utils/string_util.dart';
import 'package:j17_bank_mybank_mobile/view/components/button/j17_button.dart';
import 'package:j17_bank_mybank_mobile/view/components/card/j17_card.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/authorization_confirmation_page.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/authorization_details_page.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/pix_automatic_additional_info_expansion.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:screenshot/screenshot.dart';
import 'package:j17_bank_mybank_mobile/utils/capture_theme.dart';
import 'package:j17_bank_mybank_mobile/view/components/loading/loading_modal.dart';

/// **JORNADA 1**: Página de comprovante do primeiro pagamento
///
/// Esta página é exibida quando o usuário completa a Jornada 1:
/// QR Code → Confirmação → Autorização → **Comprovante** (primeiro pagamento realizado)
class PixAutomaticFirstPaymentReceiptPage extends StatefulWidget {
  final double valor;
  final String receiverName;
  final String receiverDocument;
  final String receiverBank;
  final String receiverAgency;
  final String receiverAccount;
  final String payerName;
  final String payerDocument;
  final String payerBank;
  final String payerAgency;
  final String payerAccount;
  final String idTransacao;
  final String dataHora;
  final String descricaoContrato;
  final String frequencia;
  final String comprovante;

  const PixAutomaticFirstPaymentReceiptPage({
    Key? key,
    required this.valor,
    required this.receiverName,
    required this.receiverDocument,
    required this.receiverBank,
    required this.receiverAgency,
    required this.receiverAccount,
    required this.payerName,
    required this.payerDocument,
    required this.payerBank,
    required this.payerAgency,
    required this.payerAccount,
    required this.idTransacao,
    required this.dataHora,
    required this.descricaoContrato,
    required this.frequencia,
    required this.comprovante,
  }) : super(key: key);

  @override
  State<PixAutomaticFirstPaymentReceiptPage> createState() =>
      _PixAutomaticFirstPaymentReceiptPageState();
}

class _PixAutomaticFirstPaymentReceiptPageState
    extends State<PixAutomaticFirstPaymentReceiptPage> {
  final _controller = ScreenshotController();
  late CaptureThemeChanger _themeChanger;

  @override
  Widget build(BuildContext context) {
    _themeChanger = Provider.of<CaptureThemeChanger>(context);

    return Scaffold(
      backgroundColor: J17ThemeColor.surfacesBackground.color(context),
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: J17ThemeColor.surfacesBackground.color(context),
        title: Text(
          'Comprovante',
          style: J17TextStyles.titleLarge().textStyle(context),
        ),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const SvgIcon(icon: J17Icons.home),
            onPressed: () {
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(J17Padding.regular),
        child: Screenshot(
          controller: _controller,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Cabeçalho com ícone e título
              Container(
                padding: const EdgeInsets.all(J17Padding.large),
                child: Column(
                  children: [
                    SvgIcon(
                      icon: J17Icons.pixAuto,
                      size: 60,
                      color: J17ThemeColor.actionAuxiliary,
                    ),
                    const SizedBox(height: J17Padding.regular),
                    Text(
                      'Pagamento realizado com sucesso!',
                      style: J17TextStyles.titleLarge(bold: true)
                          .textStyle(context),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: J17Padding.small),
                    Text(
                      'Seu primeiro pagamento foi processado.',
                      style: J17TextStyles.bodyMedium().textStyle(context),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: J17Padding.regular),
              // Seção do valor
              Container(
                decoration: BoxDecoration(
                  color: J17ThemeColor.surfacesElevate.color(context),
                  borderRadius: BorderRadius.circular(J17BorderRadius.small),
                ),
                padding: const EdgeInsets.all(J17Padding.regular),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Valor',
                      style: J17TextStyles.titleSmall(bold: true)
                          .textStyle(context),
                    ),
                    const SizedBox(height: J17Padding.small),
                    Text(
                      (widget.valor is double && widget.valor != 0.0)
                          ? widget.valor.toString().formatToBrlCurrency
                          : 'R\$ 0,00',
                      style: J17TextStyles.titleLarge(bold: true)
                          .textStyle(context),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: J17Padding.regular),
              // Seção do recebedor
              Container(
                decoration: BoxDecoration(
                  color: J17ThemeColor.surfacesElevate.color(context),
                  borderRadius: BorderRadius.circular(J17BorderRadius.small),
                ),
                padding: const EdgeInsets.all(J17Padding.regular),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Quem recebeu',
                      style: J17TextStyles.titleSmall(bold: true)
                          .textStyle(context),
                    ),
                    const SizedBox(height: J17Padding.small),
                    Text('Nome: ${widget.receiverName}',
                        style: J17TextStyles.bodyMedium().textStyle(context)),
                    Text('Documento: ${widget.receiverDocument}',
                        style: J17TextStyles.bodyMedium().textStyle(context)),
                    Text('Banco: ${widget.receiverBank}',
                        style: J17TextStyles.bodyMedium().textStyle(context)),
                    Text('Agência: ${widget.receiverAgency}',
                        style: J17TextStyles.bodyMedium().textStyle(context)),
                    Text('Conta: ${widget.receiverAccount}',
                        style: J17TextStyles.bodyMedium().textStyle(context)),
                  ],
                ),
              ),
              const SizedBox(height: J17Padding.regular),
              // Seção do pagador
              Container(
                decoration: BoxDecoration(
                  color: J17ThemeColor.surfacesElevate.color(context),
                  borderRadius: BorderRadius.circular(J17BorderRadius.small),
                ),
                padding: const EdgeInsets.all(J17Padding.regular),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Quem enviou',
                      style: J17TextStyles.titleSmall(bold: true)
                          .textStyle(context),
                    ),
                    const SizedBox(height: J17Padding.small),
                    Text('Nome: ${widget.payerName}',
                        style: J17TextStyles.bodyMedium().textStyle(context)),
                    Text('Documento: ${widget.payerDocument}',
                        style: J17TextStyles.bodyMedium().textStyle(context)),
                    Text('Banco: ${widget.payerBank}',
                        style: J17TextStyles.bodyMedium().textStyle(context)),
                    Text('Agência: ${widget.payerAgency}',
                        style: J17TextStyles.bodyMedium().textStyle(context)),
                    Text('Conta: ${widget.payerAccount}',
                        style: J17TextStyles.bodyMedium().textStyle(context)),
                  ],
                ),
              ),
              const SizedBox(height: J17Padding.regular),
              // Seção da transação
              Container(
                decoration: BoxDecoration(
                  color: J17ThemeColor.surfacesElevate.color(context),
                  borderRadius: BorderRadius.circular(J17BorderRadius.small),
                ),
                padding: const EdgeInsets.all(J17Padding.regular),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Transação',
                      style: J17TextStyles.titleSmall(bold: true)
                          .textStyle(context),
                    ),
                    const SizedBox(height: J17Padding.small),
                    Text(
                      'ID da transação: ${widget.idTransacao}',
                      style: J17TextStyles.bodyMedium().textStyle(context),
                    ),
                    Text(
                      'Data/Hora: ${widget.dataHora}',
                      style: J17TextStyles.bodyMedium().textStyle(context),
                    ),
                    Text(
                      'Descrição: ${widget.descricaoContrato}',
                      style: J17TextStyles.bodyMedium().textStyle(context),
                    ),
                    Text(
                      'Frequência: ${widget.frequencia}',
                      style: J17TextStyles.bodyMedium().textStyle(context),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: J17Padding.regular),
              // Comprovante (se disponível)
              if (widget.comprovante.isNotEmpty)
                Container(
                  decoration: BoxDecoration(
                    color: J17ThemeColor.surfacesElevate.color(context),
                    borderRadius: BorderRadius.circular(J17BorderRadius.small),
                  ),
                  padding: const EdgeInsets.all(J17Padding.regular),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Comprovante',
                        style: J17TextStyles.titleSmall(bold: true)
                            .textStyle(context),
                      ),
                      const SizedBox(height: J17Padding.small),
                      SelectableText(
                        widget.comprovante,
                        style: J17TextStyles.bodyMedium().textStyle(context),
                      ),
                    ],
                  ),
                ),
              const SizedBox(height: J17Padding.regular),
              // Botões (não incluídos no screenshot)
              J17Button(
                text: 'Facilitar próximos pagamentos',
                onPressed: () => _showAdesaoBottomSheet(context),
                variant: J17ButtonVariant.primary,
              ),
              const SizedBox(height: J17Padding.regular),
              J17Button(
                text: 'Compartilhar comprovante',
                onPressed: () => _shareReceipt(context),
                variant: J17ButtonVariant.primary,
              ),
              const SizedBox(height: J17Padding.regular),
              J17Button(
                text: 'Voltar ao menu',
                onPressed: () =>
                    Navigator.popUntil(context, (route) => route.isFirst),
                variant: J17ButtonVariant.outline,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _shareReceipt(BuildContext context) async {
    await _themeChanger.withLightThemeForSharing(() async {
      // Aguarda um pouco para garantir que o layout foi renderizado
      await Future.delayed(
        const Duration(milliseconds: 500),
      );

      Uint8List? image = await loadingAround(
        context,
        _controller.capture(),
        shouldShowErrorMessage: false,
      );

      if (image != null) {
        try {
          var params = ShareParams(
            files: [XFile.fromData(image, mimeType: 'image/png')],
            fileNameOverrides: ['comprovante_pix_automatico.png'],
          );
          ShareResult result = await SharePlus.instance.share(params);
          print("Resultado do share: ${result.status.name}");
        } catch (e) {
          print("Erro ao compartilhar: $e");
        }
      }
    });
  }

  void _showAdesaoBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(J17BorderRadius.larger),
        ),
      ),
      backgroundColor: J17ThemeColor.surfacesBackground.color(context),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(J17Padding.larger),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: J17Padding.small),
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: J17ThemeColor.surfacesElevate.color(context),
                    borderRadius: BorderRadius.circular(J17BorderRadius.smaller),
                  ),
                ),
              ),
              const SizedBox(height: J17Padding.large),
              Text(
                'Quer facilitar os próximos pagamentos?',
                style: J17TextStyles.titleLarge().textStyle(context),
                textAlign: TextAlign.left,
              ),
              const SizedBox(height: J17Padding.large),
              Text(
                'Você pode efetuar os próximos pagamentos da conta de energia elétrica por meio do Pix Automático.',
                style: J17TextStyles.bodyLarge().textStyle(context),
                textAlign: TextAlign.left,
              ),
              const SizedBox(height: J17Padding.larger),
              J17Button(
                text: 'Aderir',
                onPressed: () {
                  Navigator.pop(context); // Fecha o BottomSheet
                  _navigateToConfirmation(context);
                },
                variant: J17ButtonVariant.primary,
              ),
              const SizedBox(height: J17Padding.regular),
              J17Button(
                text: 'Cancelar',
                onPressed: () {
                  Navigator.pop(context); // Fecha o BottomSheet
                  Navigator.popUntil(context, (route) => route.isFirst);
                },
                variant: J17ButtonVariant.outline,
              ),
              const SizedBox(height: J17Padding.small),
            ],
          ),
        );
      },
    );
  }

  void _navigateToConfirmation(BuildContext context) {
    final details = AuthorizationDetails(
      value: widget.valor,
      originalValue: widget.valor,
      receiverName: widget.receiverName,
      receiverDocument: widget.receiverDocument,
      payerName: widget.payerName,
      payerDocument: widget.payerDocument,
      paymentDescription: widget.descricaoContrato,
      clientCode: '',
      paymentDate: '',
      nextPayments: widget.frequencia,
      authorizationTerm: '',
    );
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => AuthorizationConfirmationPage(
          details: details,
          jornada: 'JORNADA_4',
          onConfirm: () {
            // Após confirmação, navega para a tela de autorização
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => AuthorizationDetailsPage(
                  details: details,
                  isEditMode: true,
                  idConta: '', // Preencher conforme necessário
                  idRecorrencia: 'REC_${DateTime.now().millisecondsSinceEpoch}',
                  codigoMunicipioIbgePagador:
                      1234567, // Preencher conforme necessário
                  jornada: 'JORNADA_4',
                ),
              ),
            );
          },
          onCancel: () {
            Navigator.popUntil(context, (route) => route.isFirst);
          },
        ),
      ),
    );
  }
}
