import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_automatic_authorization_data.dart';

import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/pix_automatic_additional_info_expansion.dart';
import 'package:j17_bank_mybank_mobile/utils/string_util.dart';
import 'package:j17_bank_mybank_mobile/view/components/dynamic_tile/dynamic_tile.dart';

class PixAutomaticHistoryDetailPage extends StatelessWidget {
  final PixAutomaticAuthorization authorization;

  const PixAutomaticHistoryDetailPage({
    super.key,
    required this.authorization,
  });

  @override
  Widget build(BuildContext context) {
    final statusInfo =
        getStatusInfo(authorization.situacaoPagamentoRecorrente, context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Pix Automático',
          style: J17TextStyles.headlineSmall().textStyle(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(J17Padding.regular),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: J17Padding.regular),
            Text(
              'R\$ ${authorization.valor.toStringAsFixed(2)}',
              style: J17TextStyles.titleLarge(bold: true).textStyle(context),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: J17Padding.regular),
            Container(
              padding: const EdgeInsets.symmetric(
                  horizontal: J17Padding.large, vertical: J17Padding.smaller),
              decoration: BoxDecoration(
                color: statusInfo['color'],
                borderRadius: BorderRadius.circular(J17BorderRadius.small),
              ),
              child: Text(
                statusInfo['label'],
                style: J17TextStyles.labelLarge()
                    .textStyle(context)
                    .copyWith(color: statusInfo['textColor']),
              ),
            ),
            // DynamicTile com mensagem específica baseada no status
            if (_shouldShowStatusMessage(
                authorization.situacaoPagamentoRecorrente)) ...[
              const SizedBox(height: J17Padding.large),
              DynamicTile(
                title: _getStatusMessage(
                    authorization.situacaoPagamentoRecorrente),
                leadingIcon: J17Icons.infoCircle,
                margin: EdgeInsets.all(0),
              ),
            ],
            const SizedBox(height: J17Padding.large),
            // Informações do recebedor
            _InfoBlock(
              title: 'Informações do recebedor',
              icon: J17Icons.building,
              name: authorization.recebedor.nome,
              document: 'CNPJ: ${authorization.recebedor.cnpj}',
            ),
            const SizedBox(height: J17Padding.large),
            // Informações do devedor
            _InfoBlock(
              title: 'Informações do devedor',
              icon: J17Icons.user,
              name: authorization.devedor.nome,
              document:
                  'CPF/CNPJ: ${authorization.devedor.cpfCnpj.toString().formatedAsCpfCnpj}',
            ),
            const SizedBox(height: J17Padding.large),
            // Resumo dos dados do pagamento
            PixAutomaticAdditionalInfoExpansion(
              paymentDescription: authorization.descricaoContrato,
              clientCode: authorization.numeroContrato,
              paymentDate: authorization.dataHoraInicialRecorrencia,
              nextPayments: authorization.tipoFrequencia.name,
              authorizationTerm: authorization.dataHoraFinalRecorrencia,
            ),
          ],
        ),
      ),
    );
  }

  Map<String, dynamic> getStatusInfo(
      SituacaoPagamentoRecorrente status, BuildContext context) {
    switch (status) {
      case SituacaoPagamentoRecorrente.AUTORIZADO:
      case SituacaoPagamentoRecorrente.ACEITA:
      case SituacaoPagamentoRecorrente.ACEITA_USUARIO_PAGADOR:
        return {
          'label': 'Ativo',
          'color': J17ThemeColor.flagActiveBG.color(context),
          'textColor': J17ThemeColor.textFlagsActive.color(context)
        };
      case SituacaoPagamentoRecorrente.PENDENTE_USUARIO_PAGADOR:
        return {
          'label': 'Pendente',
          'color': J17ThemeColor.flagWaitingBG.color(context),
          'textColor': J17ThemeColor.flagWaitingText.color(context)
        };
      case SituacaoPagamentoRecorrente.EM_PROCESSAMENTO:
        return {
          'label': 'Processando',
          'color': J17ThemeColor.flagProcessingBG.color(context),
          'textColor': J17ThemeColor.flagProcessingText.color(context)
        };
      case SituacaoPagamentoRecorrente.REJEITADO:
        return {
          'label': 'Rejeitado',
          'color': J17ThemeColor.flagRejectedBG.color(context),
          'textColor': J17ThemeColor.flagRejectedText.color(context)
        };
      case SituacaoPagamentoRecorrente.CANCELADO:
        return {
          'label': 'Cancelado',
          'color': J17ThemeColor.flagCanceledBG.color(context),
          'textColor': J17ThemeColor.textFlagsCancelled.color(context)
        };
      case SituacaoPagamentoRecorrente.EXPIRADO:
        return {
          'label': 'Expirado',
          'color': J17ThemeColor.flagExpiredBG.color(context),
          'textColor': J17ThemeColor.flagExpiredText.color(context)
        };
      case SituacaoPagamentoRecorrente.CANCELADA:
        return {
          'label': 'Cancelada',
          'color': J17ThemeColor.flagCanceledBG.color(context),
          'textColor': J17ThemeColor.textFlagsCancelled.color(context)
        };
      case SituacaoPagamentoRecorrente.ACEITA_REJEICAO_PSP_RECEBEDOR:
      case SituacaoPagamentoRecorrente.ACEITA_REJEITADA_PSP_RECEBEDOR:
        return {
          'label': 'Rejeitada',
          'color': J17ThemeColor.flagRejectedBG.color(context),
          'textColor': J17ThemeColor.flagRejectedText.color(context)
        };
      case SituacaoPagamentoRecorrente.AGUARDANDO_REGISTRO_VALIDACAO:
        return {
          'label': 'Aguardando validação',
          'color': J17ThemeColor.flagWaitingBG.color(context),
          'textColor': J17ThemeColor.flagWaitingText.color(context)
        };
      case SituacaoPagamentoRecorrente.RECEBIDA_AGUARDANDO_VALIDACAO:
        return {
          'label': 'Aguardando validação',
          'color': J17ThemeColor.flagWaitingBG.color(context),
          'textColor': J17ThemeColor.flagWaitingText.color(context)
        };
      case SituacaoPagamentoRecorrente.REJEICAO_USUARIO_PAGADOR:
        return {
          'label': 'Rejeitado',
          'color': J17ThemeColor.flagRejectedBG.color(context),
          'textColor': J17ThemeColor.flagRejectedText.color(context)
        };
      case SituacaoPagamentoRecorrente.REJEITADA:
        return {
          'label': 'Rejeitada',
          'color': J17ThemeColor.flagRejectedBG.color(context),
          'textColor': J17ThemeColor.flagRejectedText.color(context)
        };
      case SituacaoPagamentoRecorrente.EXPIRADO:
        return {
          'label': 'Expirado',
          'color': J17ThemeColor.flagExpiredBG.color(context),
          'textColor': J17ThemeColor.flagExpiredText.color(context)
        };
      case SituacaoPagamentoRecorrente.CANCELADA:
        return {
          'label': 'Cancelada',
          'color': J17ThemeColor.flagCanceledBG.color(context),
          'textColor': J17ThemeColor.textFlagsCancelled.color(context)
        };
      case SituacaoPagamentoRecorrente.ACEITA_REJEICAO_PSP_RECEBEDOR:
      case SituacaoPagamentoRecorrente.ACEITA_REJEITADA_PSP_RECEBEDOR:
        return {
          'label': 'Rejeitada',
          'color': J17ThemeColor.flagRejectedBG.color(context),
          'textColor': J17ThemeColor.flagRejectedText.color(context)
        };
      case SituacaoPagamentoRecorrente.AGUARDANDO_REGISTRO_VALIDACAO:
        return {
          'label': 'Aguardando validação',
          'color': J17ThemeColor.flagWaitingBG.color(context),
          'textColor': J17ThemeColor.flagWaitingText.color(context)
        };
      case SituacaoPagamentoRecorrente.RECEBIDA_AGUARDANDO_VALIDACAO:
        return {
          'label': 'Aguardando validação',
          'color': J17ThemeColor.flagWaitingBG.color(context),
          'textColor': J17ThemeColor.flagWaitingText.color(context)
        };
      case SituacaoPagamentoRecorrente.REJEICAO_USUARIO_PAGADOR:
        return {
          'label': 'Rejeitado',
          'color': J17ThemeColor.flagRejectedBG.color(context),
          'textColor': J17ThemeColor.flagRejectedText.color(context)
        };
      case SituacaoPagamentoRecorrente.REJEITADA:
        return {
          'label': 'Rejeitada',
          'color': J17ThemeColor.flagRejectedBG.color(context),
          'textColor': J17ThemeColor.flagRejectedText.color(context)
        };
    }
  }

  /// Verifica se deve mostrar mensagem de status
  bool _shouldShowStatusMessage(SituacaoPagamentoRecorrente status) {
    return status == SituacaoPagamentoRecorrente.CANCELADO ||
        status == SituacaoPagamentoRecorrente.CANCELADA ||
        status == SituacaoPagamentoRecorrente.REJEITADO ||
        status == SituacaoPagamentoRecorrente.REJEITADA ||
        status == SituacaoPagamentoRecorrente.ACEITA_REJEICAO_PSP_RECEBEDOR ||
        status == SituacaoPagamentoRecorrente.ACEITA_REJEITADA_PSP_RECEBEDOR ||
        status == SituacaoPagamentoRecorrente.REJEICAO_USUARIO_PAGADOR;
  }

  /// Retorna a mensagem específica para cada status
  String _getStatusMessage(SituacaoPagamentoRecorrente status) {
    switch (status) {
      case SituacaoPagamentoRecorrente.CANCELADO:
      case SituacaoPagamentoRecorrente.CANCELADA:
        return 'A autorização foi CANCELADA por você (pagador).';
      case SituacaoPagamentoRecorrente.REJEITADO:
      case SituacaoPagamentoRecorrente.REJEITADA:
        return 'A autorização foi REJEITADA por você (pagador).';
      case SituacaoPagamentoRecorrente.ACEITA_REJEICAO_PSP_RECEBEDOR:
      case SituacaoPagamentoRecorrente.ACEITA_REJEITADA_PSP_RECEBEDOR:
        return 'A autorização foi REJEITADA pelo PSP (Prestador de Serviços de Pagamento).';
      case SituacaoPagamentoRecorrente.REJEICAO_USUARIO_PAGADOR:
        return 'A autorização foi REJEITADA por você (pagador).';
      default:
        return 'Status da autorização: ${status.name}';
    }
  }
}

class _InfoBlock extends StatelessWidget {
  final String title;
  final String name;
  final String document;
  final String icon;

  const _InfoBlock({
    required this.title,
    required this.name,
    required this.document,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: J17ThemeColor.surfacesElevate.color(context),
        borderRadius: BorderRadius.circular(J17BorderRadius.small),
      ),
      padding: const EdgeInsets.all(J17Padding.regular),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgIcon(icon: icon),
          const SizedBox(width: J17Padding.regSmall),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: J17TextStyles.labelSmall().textStyle(context),
                ),
                Text(
                  name,
                  style: J17TextStyles.labelLarge(
                    bold: true,
                  ).textStyle(context),
                ),
                Text(
                  document,
                  style: J17TextStyles.labelSmall().textStyle(context),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
