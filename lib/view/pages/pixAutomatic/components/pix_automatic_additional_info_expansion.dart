import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/string_util.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';

class PixAutomaticAdditionalInfoExpansion extends StatefulWidget {
  final String? paymentDescription;
  final String? clientCode;
  final String? paymentDate;
  final String? nextPayments;
  final String? authorizationTerm;
  final bool initiallyExpanded;

  const PixAutomaticAdditionalInfoExpansion({
    super.key,
    this.paymentDescription,
    this.clientCode,
    this.paymentDate,
    this.nextPayments,
    this.authorizationTerm,
    this.initiallyExpanded = true,
  });

  @override
  State<PixAutomaticAdditionalInfoExpansion> createState() =>
      _PixAutomaticAdditionalInfoExpansionState();
}

class _PixAutomaticAdditionalInfoExpansionState
    extends State<PixAutomaticAdditionalInfoExpansion> {
  bool isExpanded = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: J17ThemeColor.surfacesElevate.color(context),
        borderRadius: BorderRadius.circular(J17BorderRadius.small),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () {
              setState(() {
                isExpanded = !isExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.all(J17Padding.regular),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Informações adicionais',
                    style: J17TextStyles.labelLarge(
                      bold: true,
                    ).textStyle(context),
                  ),
                  SvgIcon(
                    icon: isExpanded ? J17Icons.arrowUp1 : J17Icons.arrowDown,
                    size: J17IconSizes.regSmall,
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded) // Usa a variável de estado para controlar a visibilidade
            Padding(
              padding: const EdgeInsets.only(
                left: J17Padding.regular,
                right: J17Padding.regular,
                bottom: J17Padding.regular,
              ),
              child: Column(
                crossAxisAlignment:
                    CrossAxisAlignment.start, // Alinha os itens à esquerda
                children: [
                  if (widget.paymentDescription != null &&
                      widget.paymentDescription!.isNotEmpty)
                    _buildDetailColumn(context, 'Objeto de pagamento',
                        widget.paymentDescription!, false),
                  if (widget.nextPayments != null &&
                      widget.nextPayments!.isNotEmpty)
                    _buildDetailColumn(
                        context, 'Recorrência', applyTitleCaseIfAllCaps(widget.nextPayments!), false),
                  if (widget.paymentDate != null &&
                      widget.paymentDate!.isNotEmpty &&
                      widget.authorizationTerm != null &&
                      widget.authorizationTerm!.isNotEmpty)
                    _buildDetailRow(
                        context,
                        'Prazo de autorização',
                        formatPaymentDate(widget.authorizationTerm!),
                        'Pagamento em',
                        formatPaymentDate(widget.paymentDate!),
                        false),
                  if (widget.clientCode != null &&
                      widget.clientCode!.isNotEmpty)
                    _buildDetailColumn(
                        context, 'Código do cliente', widget.clientCode!, true),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailColumn(
      BuildContext context, String label, String value, bool isLastLabel) {
    return Padding(
      padding: EdgeInsets.only(bottom: isLastLabel ? 0 : J17Padding.regular),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: J17TextStyles.labelLarge().textStyle(context),
            maxLines: J17OtherSizes.regularMaxLines,
            textAlign: TextAlign.left,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(
            height: J17Padding.small,
          ),
          Text(
            value,
            style: J17TextStyles.bodyMedium(
              bold: true,
            ).textStyle(context),
            textAlign: TextAlign.right,
            maxLines: null,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String labelAuthorization,
    String valueAuthorization,
    String labelPayment,
    String valuePayment,
    bool isLastLabel,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: isLastLabel ? 0 : J17Padding.regular),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    labelAuthorization,
                    style: J17TextStyles.labelLarge().textStyle(context),
                    maxLines: J17OtherSizes.regularMaxLines,
                    textAlign: TextAlign.left,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(
                    height: J17Padding.small,
                  ),
                  Text(
                    valueAuthorization,
                    style: J17TextStyles.bodyMedium(
                      bold: true,
                    ).textStyle(context),
                    textAlign: TextAlign.right,
                    maxLines: null,
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    labelPayment,
                    style: J17TextStyles.labelLarge().textStyle(context),
                    maxLines: J17OtherSizes.regularMaxLines,
                    textAlign: TextAlign.left,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(
                    height: J17Padding.small,
                  ),
                  Text(
                    valuePayment,
                    style: J17TextStyles.bodyMedium(
                      bold: true,
                    ).textStyle(context),
                    textAlign: TextAlign.right,
                    maxLines: null,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
