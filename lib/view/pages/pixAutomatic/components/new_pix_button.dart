import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';

class NewPixButton extends StatelessWidget {
  final VoidCallback onPressed;
  const NewPixButton({
    required this.onPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      onPressed: onPressed,
      child: Text(
        'Fazer novo Pix',
        style: J17TextStyles.bodyLarge().textStyle(context),
      ),
    );
  }
}
