import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';

class PixAutomaticInfoCard extends StatelessWidget {
  final String message;
  final Color? backgroundColor;
  final J17ThemeColor? iconColor;

  const PixAutomaticInfoCard({
    super.key,
    required this.message,
    this.backgroundColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    final Color bgColor =
        backgroundColor ?? J17ThemeColor.surfacesElevate.color(context);
    return Container(
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(J17Padding.small),
      ),
      padding: const EdgeInsets.all(J17Padding.regular),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgIcon(
            icon: J17Icons.infoCircle,
            color: iconColor ?? J17ThemeColor.textHighlightPrimaryColor,
          ),
          const SizedBox(width: J17Padding.regSmall),
          Expanded(
            child: Text(
              message,
              style: J17TextStyles.bodyMedium().textStyle(context),
            ),
          ),
        ],
      ),
    );
  }
}
