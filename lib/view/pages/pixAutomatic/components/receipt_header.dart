import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/auto_pix_data.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';
import 'package:j17_bank_mybank_mobile/view/components/dynamic_tile/dynamic_tile.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pix/automatic/components/auto_pix_select_label_with_color.dart';

class ReceiptHeader extends StatelessWidget {
  final dynamic valor;
  final String? title;
  final String? icon;
  final bool isPixReceipt; // ✅ NOVO: Controla o título do comprovante
  final ActiveAutorizationAutoPix? activeAutorization;
  final bool isSchedule;
  const ReceiptHeader({
    required this.valor,
    this.icon,
    this.title,
    this.isPixReceipt = false, // ✅ NOVO: Padrão é comprovante de autorização
    this.activeAutorization,
    this.isSchedule = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    Map<String, dynamic> labelwithColor = activeAutorization == null
        ? {}
        : AutoPixSelectLabelWithColor.selectLabelwithColor(
            activeAutorization!, context);
    String titulo = activeAutorization?.name.toLowerCase() ?? 'confimada';
    return Column(
      children: [
        SvgIcon(
          icon: icon ?? J17Icons.pix,
          size: J17IconSizes.extraLarge,
        ),
        const SizedBox(height: J17Padding.regular),
        Text(
          title ??
              (isPixReceipt
                  ? 'Pix realizado!'
                  : isSchedule
                      ? 'Pix agendado!'
                      : 'Autorização ${titulo == 'cancelado' ? 'cancelada' : titulo == 'processando' ? 'solicitada' : titulo}!'),
          style: J17TextStyles.titleLarge().textStyle(context),
        ),
        const SizedBox(height: J17Padding.small),
        Text(
          (valor is String) ? valor : 'R\$ ${_formatValue(valor)}',
          style: J17TextStyles.titleLarge(
            bold: true,
          ).textStyle(context),
        ),
        SizedBox(
          height: J17Padding.regular,
        ),
        if (!isPixReceipt && activeAutorization != null) ...[
          Center(
            child: Container(
              padding: const EdgeInsets.all(J17Padding.smallest),
              decoration: BoxDecoration(
                color: labelwithColor["color"],
                borderRadius: BorderRadius.circular(J17BorderRadius.smaller),
              ),
              child: Text(
                labelwithColor["label"] ?? "",
                style: labelwithColor["labelStyle"],
              ),
            ),
          ),
          const SizedBox(height: J17Padding.regular),
          if (activeAutorization == ActiveAutorizationAutoPix.Processando &&
              isSchedule)
            DynamicTile(
              title: 'Seu pedido de cancelamento está sendo processado',
              leadingIcon: J17Icons.infoCircle,
              margin: EdgeInsets.all(0),
            ),
          if (activeAutorization == ActiveAutorizationAutoPix.Cancelado)
            DynamicTile(
              title: 'Seu pedido de cancelamento foi concluído.',
              leadingIcon: J17Icons.infoCircle,
              margin: EdgeInsets.all(0),
            ),
        ],
      ],
    );
  }

  static String _formatValue(dynamic valor) {
    if (valor == null) return '0,00';
    if (valor is double) return valor.toStringAsFixed(2).replaceAll('.', ',');
    if (valor is int)
      return (valor / 100).toStringAsFixed(2).replaceAll('.', ',');
    if (valor is String) {
      final v = double.tryParse(valor.replaceAll(',', '.'));
      if (v != null) return v.toStringAsFixed(2).replaceAll('.', ',');
    }
    return valor.toString();
  }
}
