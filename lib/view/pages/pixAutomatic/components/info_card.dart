import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/string_util.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';

class InfoCard extends StatefulWidget {
  final String title;
  final String icon;
  final String name;
  final String document;

  const InfoCard({
    Key? key,
    required this.title,
    required this.icon,
    required this.name,
    required this.document,
  }) : super(key: key);

  @override
  State<InfoCard> createState() => _InfoCardState();
}

class _InfoCardState extends State<InfoCard> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: J17Padding.large),
      child: Container(
        decoration: BoxDecoration(
          color: J17ThemeColor.surfacesElevate.color(context),
          borderRadius: BorderRadius.circular(J17BorderRadius.small),
        ),
        padding: const EdgeInsets.all(J17Padding.regular),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgIcon(
              icon: widget.icon,
            ),
            const SizedBox(width: J17Padding.regular),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  widget.title,
                  style: J17TextStyles.titleSmall(
                    bold: true,
                  ).textStyle(context),
                ),
                const SizedBox(height: J17Padding.smallest),
                Text(
                  widget.name,
                  style: J17TextStyles.titleMedium(
                    bold: true,
                  ).textStyle(context),
                ),
                const SizedBox(height: J17Padding.smallest),
                Row(
                  children: [
                    Text(
                      widget.document.length == 11 ? 'CPF: ' : 'CNPJ: ',
                      style: J17TextStyles.bodySmall().textStyle(context),
                    ),
                    Text(
                      widget.document.formatedAsCpfMaskCnpj,
                      style: J17TextStyles.bodySmall().textStyle(context),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
