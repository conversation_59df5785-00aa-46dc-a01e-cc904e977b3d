import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';

class InfoRowSpaced extends StatelessWidget {
  final String labelLeading;
  final String valueLeading;
  final String labelTrailing;
  final String valueTrailing;
  const InfoRowSpaced({
    required this.labelLeading,
    required this.valueLeading,
    required this.labelTrailing,
    required this.valueTrailing,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              labelLeading,
              style: J17TextStyles.bodyMedium(
                color: J17ThemeColor.textSecondaryColor,
              ).textStyle(context),
            ),
            const SizedBox(
              height: J17Padding.small,
            ),
            Text(
              valueLeading,
              style: J17TextStyles.bodyMedium(
                bold: true,
              ).textStyle(context),
              maxLines: J17OtherSizes.regularMaxLines,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        Spacer(),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              labelTrailing,
              style: J17TextStyles.bodyMedium(
                color: J17ThemeColor.textSecondaryColor,
              ).textStyle(context),
            ),
            const SizedBox(
              height: J17Padding.small,
            ),
            Text(
              valueTrailing,
              style: J17TextStyles.bodyMedium(
                bold: true,
              ).textStyle(context),
              maxLines: J17OtherSizes.regularMaxLines,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ],
    );
  }
}

class InfoRowDoubleColumn extends StatelessWidget {
  final String labelLeading;
  final String valueLeading;
  final String labelTrailing;
  final String valueTrailing;
  const InfoRowDoubleColumn({
    required this.labelLeading,
    required this.valueLeading,
    required this.labelTrailing,
    required this.valueTrailing,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              labelLeading,
              style: J17TextStyles.bodyMedium(
                color: J17ThemeColor.textSecondaryColor,
              ).textStyle(context),
            ),
            const SizedBox(
              height: J17Padding.small,
            ),
            Text(
              valueLeading,
              style: J17TextStyles.bodyMedium(
                bold: true,
              ).textStyle(context),
              maxLines: J17OtherSizes.regularMaxLines,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        SizedBox(width: J17Padding.large),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              labelTrailing,
              style: J17TextStyles.bodyMedium(
                color: J17ThemeColor.textSecondaryColor,
              ).textStyle(context),
            ),
            const SizedBox(
              height: J17Padding.small,
            ),
            Text(
              valueTrailing,
              style: J17TextStyles.bodyMedium(
                bold: true,
              ).textStyle(context),
              maxLines: J17OtherSizes.regularMaxLines,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ],
    );
  }
}
