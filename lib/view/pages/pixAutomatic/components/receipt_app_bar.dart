import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';

class ReceiptAppBar extends StatelessWidget implements PreferredSizeWidget {
  const ReceiptAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: IconButton(
        icon: const SvgIcon(icon: J17Icons.arrowLeftCustom),
        onPressed: () => Navigator.pop(context),
      ),
      title: const Text('Comprovante'),
      centerTitle: true,
      actions: [
        IconButton(
          icon: const Icon(Icons.home_outlined),
          onPressed: () {}, // TODO: implementar navegação home
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
