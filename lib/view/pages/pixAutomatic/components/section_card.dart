import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';

class SectionCard extends StatelessWidget {
  final String title;
  final List<Widget> children;
  const SectionCard({
    required this.title,
    required this.children,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: J17ThemeColor.surfacesElevate.color(context),
        borderRadius: BorderRadius.circular(J17BorderRadius.small),
      ),
      padding: const EdgeInsets.all(J17Padding.regular),
      margin: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: J17TextStyles.labelLarge(
              bold: true,
            ).textStyle(context),
          ),
          Divider(
            color: J17ThemeColor.dividerColor.color(context),
          ),
          ...children,
        ],
      ),
    );
  }
}
