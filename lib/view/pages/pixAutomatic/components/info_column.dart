import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';

class InfoColumn extends StatelessWidget {
  final String? label;
  final String value;
  final bool isTitle;
  const InfoColumn({
    this.label,
    required this.value,
    this.isTitle = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (isTitle) {
      return Text(
        value,
        style: J17TextStyles.bodyMedium(
          bold: true,
        ).textStyle(context),
      );
    }
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null)
          Text(
            label!,
            style: J17TextStyles.bodyMedium(
              color: J17ThemeColor.textSecondaryColor,
            ).textStyle(context),
          ),
        if (label != null)
          const SizedBox(
            height: J17Padding.small,
          ),
        Text(
          value,
          style: J17TextStyles.bodyMedium(
            bold: true,
          ).textStyle(context),
          maxLines: J17OtherSizes.regularMaxLines,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}
