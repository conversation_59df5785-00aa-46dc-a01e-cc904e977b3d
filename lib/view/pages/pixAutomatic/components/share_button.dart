import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';

class ShareButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool disabled;
  const ShareButton({
    required this.onPressed,
    this.disabled = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: disabled
            ? J17ThemeColor.actionDisabled.color(context)
            : J17ThemeColor.actionPrimaryColor.color(context),
        minimumSize: const Size.fromHeight(J17IconSizes.extraLarge),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(J17BorderRadius.small),
        ),
      ),
      onPressed: onPressed,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Compartilhar comprovante',
            style: J17TextStyles.bodyLarge(
              color: disabled
                  ? J17ThemeColor.textDisabled
                  : J17ThemeColor.textLabelButtons,
            ).textStyle(context),
          ),
          SizedBox(width: J17Padding.small),
          SvgIcon(
            icon: J17Icons.exportIcon,
            color: disabled
                ? J17ThemeColor.textDisabled
                : J17ThemeColor.textLabelButtons,
            size: J17IconSizes.regSmall,
          )
        ],
      ),
    );
  }
}
