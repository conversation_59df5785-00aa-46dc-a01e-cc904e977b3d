import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/auto_pix_data.dart';
import 'package:j17_bank_mybank_mobile/utils/capture_theme.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';
import 'package:j17_bank_mybank_mobile/utils/string_util.dart';
import 'package:j17_bank_mybank_mobile/view/components/button/j17_button.dart';
import 'package:j17_bank_mybank_mobile/view/components/loading/loading_modal.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/authorization_details_page.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/authorization_confirmation_page.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/info_column.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/info_rows.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/new_pix_button.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/receipt_header.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/section_card.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/share_button.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/pending_authorizations_page.dart';
import 'package:j17_bank_mybank_mobile/viewmodel/pix/automatic/pending_authorizations_viewmodel.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:j17_bank_mybank_mobile/view/components/custom_page/bottom_page.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_decodifica_qrcode_data.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/pix_automatic_receipt_page.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_automatic_authorization_data.dart';
import 'package:j17_bank_mybank_mobile/model/repository/pix/automatic/pix_automatic_repository.dart';

/// **JORNADA 3 e 4**: Página de comprovante com suporte a 2 layouts diferentes
/// 1. Comprovante de PIX realizado (primeiro comprovante)
/// 2. Comprovante de autorização (segundo comprovante)
class PixAutomaticQRCodeReceiptPage extends StatefulWidget {
  final dynamic decodeResponse; // Dados da decodificação do QR Code
  final Map<String, dynamic>? authResponse; // Resposta da API de autorização
  final bool
      showBottomSheetOnStart; // Se deve mostrar o BottomSheet automaticamente
  final String? jornada; // Jornada do PIX automático
  final String? idConta; // ID da conta
  final int? codigoMunicipioIbgePagador; // Código do município IBGE do pagador
  final bool
      isPixReceipt; // Se é o primeiro comprovante (PIX realizado) ou segundo (autorização)

  const PixAutomaticQRCodeReceiptPage({
    super.key,
    required this.decodeResponse,
    this.authResponse,
    this.showBottomSheetOnStart = false,
    this.jornada,
    this.idConta,
    this.codigoMunicipioIbgePagador,
    this.isPixReceipt = true, // Por padrão é o primeiro comprovante
  });

  @override
  State<PixAutomaticQRCodeReceiptPage> createState() =>
      _PixAutomaticQRCodeReceiptPageState();
}

class _PixAutomaticQRCodeReceiptPageState
    extends State<PixAutomaticQRCodeReceiptPage> {
  bool _showFab = false;
  bool _showFabLabel = false;
  final PixAutomaticRepository _repository = PixAutomaticRepository();
  final ScreenshotController _screenshotController = ScreenshotController();
  late CaptureThemeChanger _themeChanger;

  @override
  void initState() {
    super.initState();

    debugPrint('🎯 [RECEIPT] ==========================================');
    debugPrint('🎯 [RECEIPT] Iniciando PixAutomaticQRCodeReceiptPage');
    debugPrint('🎯 [RECEIPT] Jornada: ${widget.jornada}');
    debugPrint(
        '🎯 [RECEIPT] showBottomSheetOnStart: ${widget.showBottomSheetOnStart}');
    debugPrint('🎯 [RECEIPT] isPixReceipt: ${widget.isPixReceipt}');
    debugPrint(
        '🎯 [RECEIPT] decodeResponse tipo: ${widget.decodeResponse.runtimeType}');
    debugPrint(
        '🎯 [RECEIPT] authResponse tipo: ${widget.authResponse.runtimeType}');
    debugPrint('🎯 [RECEIPT] ==========================================');

    if (widget.jornada == 'JORNADA_4' && widget.showBottomSheetOnStart) {
      debugPrint('🎯 [RECEIPT] Jornada 4: Mostrando BottomSheet de adesão');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _showAdesaoBottomSheet();
        }
      });
    } else if (widget.jornada == 'JORNADA_3' && widget.showBottomSheetOnStart) {
      debugPrint('🎯 [RECEIPT] Jornada 3: Mostrando BottomSheet padrão');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _showDefaultBottomSheet();
        }
      });
    } else if (widget.showBottomSheetOnStart) {
      debugPrint('🎯 [RECEIPT] Outras jornadas: Mostrando BottomSheet padrão');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _showBottomSheet();
        }
      });
    } else {
      debugPrint('🎯 [RECEIPT] Nenhum BottomSheet será mostrado');
    }
  }

  void _showBottomSheet() {
    // Para Jornada 4, abre diretamente o BottomPage de adesão
    if (widget.jornada == 'JORNADA_4') {
      _showAdesaoBottomSheet();
    } else if (widget.jornada == 'JORNADA_3') {
      // ✅ CORRIGIDO: Jornada 3 usa o BottomPage padrão
      _showDefaultBottomSheet();
    } else {
      // BottomPage padrão para outras jornadas
      _showDefaultBottomSheet();
    }
  }

  // ✅ REMOVIDO: BottomSheet específico da Jornada 3 não é mais necessário
  // A navegação para o segundo comprovante agora é feita no _showDefaultBottomSheet

  void _showAdesaoBottomSheet() async {
    setState(() => _showFab = false);
    await showModalBottomSheet(
      context: context,
      isDismissible: true,
      enableDrag: true,
      shape: RoundedRectangleBorder(
        borderRadius:
            BorderRadius.vertical(top: Radius.circular(J17BorderRadius.small)),
      ),
      backgroundColor: J17ThemeColor.surfacesBackground.color(context),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(J17Padding.regular),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: J17Padding.small),
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: J17ThemeColor.surfacesElevate.color(context),
                    borderRadius:
                        BorderRadius.circular(J17BorderRadius.smaller),
                  ),
                ),
              ),
              const SizedBox(height: J17Padding.large),
              Text(
                'Quer facilitar os próximos pagamentos?',
                style: J17TextStyles.titleLarge().textStyle(context),
                textAlign: TextAlign.left,
              ),
              const SizedBox(height: J17Padding.large),
              Text(
                'Você pode efetuar os próximos pagamentos da conta de energia elétrica por meio do Pix Automático.',
                style: J17TextStyles.bodyLarge().textStyle(context),
                textAlign: TextAlign.left,
              ),
              const SizedBox(height: J17Padding.large),
              J17Button(
                text: 'Aderir',
                onPressed: () {
                  Navigator.pop(context); // Fecha o BottomSheet

                  // ✅ CORREÇÃO: Jornada 4 - Navega para tela de confirmação
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AuthorizationConfirmationPage(
                        details: widget.decodeResponse,
                        jornada: 'JORNADA_4',
                        onConfirm: () {
                          // Navega para a tela de autorização
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => AuthorizationDetailsPage(
                                details: widget.decodeResponse,
                                jornada: 'JORNADA_4',
                                idConta: widget.idConta ?? '',
                                idRecorrencia: '',
                                codigoMunicipioIbgePagador:
                                    widget.codigoMunicipioIbgePagador ?? 0,
                                isEditMode: false,
                                isActiveAuthorization: false,
                              ),
                            ),
                          );
                        },
                        onCancel: () {
                          Navigator.pop(context);
                        },
                      ),
                    ),
                  );
                },
                variant: J17ButtonVariant.primary,
              ),
              const SizedBox(height: J17Padding.large),
              J17Button(
                text: 'Cancelar',
                onPressed: () {
                  Navigator.pop(context); // Fecha o BottomSheet
                  Navigator.popUntil(context, (route) => route.isFirst);
                },
                variant: J17ButtonVariant.outline,
              ),
              const SizedBox(height: J17Padding.small),
            ],
          ),
        );
      },
    );
    setState(() => _showFab = true);
  }

  void _showDefaultBottomSheet() async {
    setState(() => _showFab = false);
    await BottomPage.abrir(
      context: context,
      bodyContent: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            'Deseja visualizar o comprovante?',
            style: J17TextStyles.headlineSmall().textStyle(context),
          ),
          const SizedBox(height: J17Padding.regular),
          Text(
            'O primeiro pagamento foi realizado e o comprovante já está disponível.',
            style: J17TextStyles.bodyMedium().textStyle(context),
          ),
          const SizedBox(height: J17Padding.large),
          J17Button(
            text: 'Ver comprovante',
            onPressed: () {
              Navigator.of(context).pop(); // Fecha o bottom sheet

              // ✅ NOVO: Navegação específica para Jornada 3
              if (widget.jornada == 'JORNADA_3') {
                // Navega para o segundo comprovante (PIX realizado)
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PixAutomaticQRCodeReceiptPage(
                      decodeResponse: widget.decodeResponse,
                      authResponse: widget.authResponse,
                      showBottomSheetOnStart: false,
                      jornada: 'JORNADA_3',
                      idConta: widget.idConta,
                      codigoMunicipioIbgePagador:
                          widget.codigoMunicipioIbgePagador,
                      isPixReceipt:
                          true, // ✅ NOVO: Segundo comprovante (PIX realizado)
                    ),
                  ),
                );
              } else {
                // Para outras jornadas, usa o método antigo
                _showPixReceipt();
              }
            },
          ),
          const SizedBox(height: J17Padding.regular),
          J17Button(
            text: 'Cancelar',
            variant: J17ButtonVariant.outline,
            onPressed: () {
              Navigator.of(context).pop(); // Fecha o bottom sheet
            },
          ),
        ],
      ),
    );
    setState(() => _showFab = true);
  }

  void _navigateToConfirmation() {
    // Extrai os dados do decodeResponse para criar AuthorizationDetails
    final details = _createAuthorizationDetails();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => AuthorizationConfirmationPage(
          details: details,
          jornada: 'JORNADA_4',
          onConfirm: () {
            // Após confirmação, navega para a tela de autorização
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => AuthorizationDetailsPage(
                  details: details,
                  isEditMode: true,
                  idConta: widget.idConta ?? '',
                  idRecorrencia: details.idRecorrencia ?? '',
                  codigoMunicipioIbgePagador:
                      widget.codigoMunicipioIbgePagador ?? 0,
                  jornada: 'JORNADA_4',
                ),
              ),
            );
          },
          onCancel: () {
            Navigator.popUntil(context, (route) => route.isFirst);
          },
        ),
      ),
    );
  }

  AuthorizationDetails _createAuthorizationDetails() {
    // Extrai dados do decodeResponse para criar AuthorizationDetails
    final valor = _extractValueFromResponse();
    final receiver = _extractReceiverFullInfo();
    final payer = _extractPayerFullInfo();

    // Extrai dados adicionais do QR Code decodificado
    String paymentDescription = '';
    String clientCode = '';
    String paymentDate = '';
    String nextPayments = '';
    String authorizationTerm = '';
    String idRecorrencia = ''; // Extrair do decode

    // Tenta extrair dados do decodeResponse
    if (widget.jornada == 'JORNADA_4') {
      // NÃO preencher paymentDescription automaticamente na jornada 4
      paymentDescription = '';
      // Preencher os demais campos normalmente se possível
      if (widget.decodeResponse != null) {
        try {
          if (widget.decodeResponse is PixDecodificaQrcodeData) {
            final decode = widget.decodeResponse as PixDecodificaQrcodeData;
            if (decode.decodificacaoQrcodeDTO != null) {
              final dto = decode.decodificacaoQrcodeDTO!;
              if (dto.dadosQrCodeComposto != null) {
                final composto = dto.dadosQrCodeComposto!;
                clientCode = composto.nrContrato ?? 'Contrato Automático';
                paymentDate =
                    composto.dtInicialRecorrencia ?? 'Data não informada';
                nextPayments = composto.tpFrequencia ?? 'MENSAL';
                authorizationTerm =
                    composto.dtFinalRecorrencia ?? 'Prazo não informado';
                idRecorrencia = composto.idRecorrencia ?? '';
              }
            }
          }
        } catch (e) {
          // fallback: mantém campos vazios ou padrão
        }
      }
    } else {
      // Jornadas anteriores: lógica antiga
      if (widget.decodeResponse != null) {
        try {
          if (widget.decodeResponse is PixDecodificaQrcodeData) {
            final decode = widget.decodeResponse as PixDecodificaQrcodeData;
            if (decode.decodificacaoQrcodeDTO != null) {
              final dto = decode.decodificacaoQrcodeDTO!;
              if (dto.dadosQrCodeDinamico != null) {
                final dinamico = dto.dadosQrCodeDinamico!;
                paymentDescription =
                    dinamico.solicitacaoPagador ?? 'Pagamento PIX Automático';
              }
              if (dto.dadosQrCodeComposto != null) {
                final composto = dto.dadosQrCodeComposto!;
                paymentDescription =
                    composto.descContrato ?? paymentDescription;
                clientCode = composto.nrContrato ?? 'Contrato Automático';
                paymentDate =
                    composto.dtInicialRecorrencia ?? 'Data não informada';
                nextPayments = composto.tpFrequencia ?? 'MENSAL';
                authorizationTerm =
                    composto.dtFinalRecorrencia ?? 'Prazo não informado';
                idRecorrencia = composto.idRecorrencia ?? '';
              }
            }
          } else if (widget.decodeResponse is AuthorizationDetails) {
            final details = widget.decodeResponse as AuthorizationDetails;
            paymentDescription =
                details.paymentDescription ?? 'Pagamento PIX Automático';
            clientCode = details.clientCode ?? 'Contrato Automático';
            paymentDate = details.paymentDate ?? 'Data não informada';
            nextPayments = details.nextPayments ?? 'MENSAL';
            authorizationTerm =
                details.authorizationTerm ?? 'Prazo não informado';
            idRecorrencia = details.idRecorrencia ?? '';
          }
        } catch (e) {
          paymentDescription = 'Pagamento PIX Automático';
          clientCode = 'Contrato Automático';
          paymentDate = 'Data não informada';
          nextPayments = 'MENSAL';
          authorizationTerm = 'Prazo não informado';
        }
      } else {
        paymentDescription = 'Pagamento PIX Automático';
        clientCode = 'Contrato Automático';
        paymentDate = 'Data não informada';
        nextPayments = 'MENSAL';
        authorizationTerm = 'Prazo não informado';
      }
    }

    return AuthorizationDetails(
      value: valor,
      originalValue: valor,
      receiverName: (receiver['nome'] ?? 'Recebedor não informado').toString(),
      receiverDocument:
          (receiver['documento'] ?? 'Documento não informado').toString(),
      payerName: (payer['nome'] ?? 'Pagador não informado').toString(),
      payerBank: (payer['banco'] ?? 'Banco não informado').toString(),
      payerAgency: (payer['agencia'] ?? 'Agência não informada').toString(),
      payerAccount: (payer['conta'] ?? 'Conta não informada').toString(),
      payerDocument:
          (payer['documento'] ?? 'Documento não informado').toString(),
      payerAccountType: (payer['tipoConta'] ?? 'Tipo de conta não informado'),
      paymentDescription: paymentDescription,
      clientCode: clientCode,
      paymentDate: paymentDate,
      nextPayments: nextPayments,
      authorizationTerm: authorizationTerm,
      decodeResponse: widget.decodeResponse,
      idRecorrencia:
          idRecorrencia, // Adicionar idRecorrencia extraído do decode
    );
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('🎯 [RECEIPT] ==========================================');
    debugPrint('🎯 [RECEIPT] Renderizando página de comprovante');
    debugPrint('🎯 [RECEIPT] Jornada: ${widget.jornada}');
    debugPrint('🎯 [RECEIPT] isPixReceipt: ${widget.isPixReceipt}');
    debugPrint(
        '🎯 [RECEIPT] showBottomSheetOnStart: ${widget.showBottomSheetOnStart}');
    debugPrint('🎯 [RECEIPT] ==========================================');

    final valor = _extractValueFromResponse();
    final receiver = _extractReceiverFullInfo();
    final payer = _extractPayerFullInfo();
    final pixId = _extractPixIdFromResponse();

    debugPrint('🎯 [RECEIPT] Dados extraídos:');
    debugPrint('🎯 [RECEIPT] - Valor: $valor');
    debugPrint('🎯 [RECEIPT] - Receiver: $receiver');
    debugPrint('🎯 [RECEIPT] - Payer: $payer');
    debugPrint('🎯 [RECEIPT] - PixId: $pixId');

    // ✅ NOVO: Determina o layout baseado no tipo de comprovante
    final isPixReceipt = widget.isPixReceipt;
    final isJornada4 = widget.jornada == 'JORNADA_4';

    return Scaffold(
      backgroundColor: J17ThemeColor.surfacesBackground.color(context),
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: J17ThemeColor.surfacesBackground.color(context),
        title: Text(
          'Comprovante',
          style: J17TextStyles.titleLarge().textStyle(context),
        ),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const SvgIcon(icon: J17Icons.home2),
            onPressed: () {
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(J17Padding.regular),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // ✅ NOVO: Header diferente para PIX realizado vs autorização
              ReceiptHeader(
                icon: isPixReceipt ? J17Icons.pix : J17Icons.pixAuto,
                valor: valor,
                isPixReceipt:
                    isPixReceipt, // ✅ NOVO: Passa flag para título diferente
                activeAutorization: ActiveAutorizationAutoPix.Processando,
              ),
              const SizedBox(height: J17Padding.regular),
              // ✅ NOVO: Informação de autorização apenas para comprovante de autorização
              if (!isPixReceipt) ...[
                Container(
                  padding: const EdgeInsets.all(J17Padding.regular),
                  decoration: BoxDecoration(
                    color: J17ThemeColor.surfacesElevate.color(context),
                    borderRadius: BorderRadius.circular(J17BorderRadius.small),
                  ),
                  child: Row(
                    children: [
                      SvgIcon(icon: J17Icons.infoCircle),
                      const SizedBox(width: J17Padding.regSmaller),
                      Flexible(
                        child: Text(
                          'Sua autorização pode demorar alguns minutos para ser confirmada.',
                          maxLines: J17OtherSizes.regularMaxLines,
                          overflow: TextOverflow.ellipsis,
                          style: J17TextStyles.bodyMedium().textStyle(context),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: J17Padding.large),
              ],
              // Transação -----------------------------------------------
              SectionCard(
                title: 'Transação',
                children: [
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    label: 'ID da transação',
                    value: pixId ?? '-',
                  ),
                ],
              ),
              const SizedBox(height: J17Padding.large),
              // Quem Recebeu -----------------------------------------------
              SectionCard(
                title: 'Quem recebeu',
                children: [
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    value: receiver['nome'] ?? '-',
                    isTitle: true,
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    label: receiver['documento'].toString().length <= 11
                        ? 'CPF'
                        : 'CNPJ',
                    value: '${(receiver['documento'] ?? '').toString()}',
                  ),
                  SizedBox(height: J17Padding.regular),
                ],
              ),
              const SizedBox(height: J17Padding.large),
              // Quem enviou -----------------------------------------------
              SectionCard(
                title: 'Quem enviou',
                children: [
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    value: (payer['nome'] ?? '').toString(),
                    isTitle: true,
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    label: 'Banco',
                    value: '${(payer['banco'] ?? '').toString()}',
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoRowDoubleColumn(
                    labelLeading: 'Agência',
                    valueLeading:
                        '${(payer['agencia'] ?? '').toString()}' ?? '-',
                    labelTrailing: 'Conta',
                    valueTrailing:
                        '${(payer['conta'] ?? '').toString()}' ?? '-',
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    label: payer['documento'].toString().length <= 11
                        ? 'CPF'
                        : 'CNPJ',
                    value: '${(payer['documento'] ?? '').toString()}' ?? '-',
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    label: 'Tipo de conta',
                    value: applyTitleCaseIfAllCaps(
                            payer['tipoConta'].toString()) ??
                        '-',
                  ),
                  SizedBox(height: J17Padding.regular),
                ],
              ),
              const SizedBox(height: J17Padding.large),

              // ✅ NOVO: Informações importantes apenas para comprovante de autorização
              if (!isPixReceipt) ...[
                Container(
                  decoration: BoxDecoration(
                    color: J17ThemeColor.surfacesElevate.color(context),
                    borderRadius: BorderRadius.circular(J17BorderRadius.small),
                  ),
                  padding: const EdgeInsets.all(J17Padding.regular),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Ao acessar o Pix Automático você pode:',
                        style: J17TextStyles.bodyMedium().textStyle(context),
                      ),
                      const SizedBox(height: J17Padding.small),
                      Text(
                        '• Desabilitar o recebimento de notificações ou o uso de limite de crédito para pagamentos por meio do Pix Automático;',
                        style: J17TextStyles.bodyMedium().textStyle(context),
                      ),
                      Text(
                        '• Alterar o valor máximo do pagamento; ou',
                        style: J17TextStyles.bodyMedium().textStyle(context),
                      ),
                      Text(
                        '• Solicitar o cancelamento dessa autorização a qualquer momento.',
                        style: J17TextStyles.bodyMedium().textStyle(context),
                      ),
                      const SizedBox(height: J17Padding.small),
                      Divider(
                        color: J17ThemeColor.dividerColor.color(context),
                        thickness: 1,
                      ),
                      Text(
                        'Você será notificado quando os pagamentos recorrentes, sujeitos à disponibilidade de limite do Pix Automático, forem agendados.',
                        style: J17TextStyles.bodyMedium().textStyle(context),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: J17Padding.large),
              ],

              // ✅ NOVO: Botões diferentes para PIX vs autorização
              if (isPixReceipt) ...[
                // Botões para comprovante de PIX realizado
                ShareButton(onPressed: () => _shareReceipt(context)),
                const SizedBox(height: J17Padding.large),
                NewPixButton(
                  onPressed: () {
                    Navigator.popUntil(context, (route) => route.isFirst);
                  },
                ),
              ] else ...[
                // Botões para comprovante de autorização
                ShareButton(onPressed: () => _shareReceipt(context)),
                const SizedBox(height: J17Padding.large),
                NewPixButton(
                  onPressed: () {
                    // Para Jornada 1, navega para autorizações pendentes
                    if (widget.jornada == 'JORNADA_1') {
                      Navigator.of(context).pushAndRemoveUntil(
                        MaterialPageRoute(
                          builder: (context) => ChangeNotifierProvider(
                            create: (context) =>
                                PendingAuthorizationsViewModel(),
                            child: const PendingAuthorizationsPage(),
                          ),
                        ),
                        (route) => route.isFirst,
                      );
                    } else {
                      // Para outras jornadas, volta para o menu principal
                      Navigator.popUntil(context, (route) => route.isFirst);
                    }
                  },
                ),
              ],
            ],
          ),
        ),
      ),
      floatingActionButton: _showFab
          ? Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: _showFabLabel
                      ? Padding(
                          padding:
                              const EdgeInsets.only(bottom: J17Padding.small),
                          child: Row(
                            children: [
                              Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: J17Padding.small,
                                  vertical: J17Padding.smaller,
                                ),
                                decoration: BoxDecoration(
                                  color: J17ThemeColor.actionAuxiliary
                                      .color(context),
                                  borderRadius: BorderRadius.circular(
                                      J17BorderRadius.small),
                                ),
                                child: Text(
                                  isJornada4 ? 'Pix Automático' : 'Comprovante',
                                  style: J17TextStyles.bodyLarge(
                                    color: J17ThemeColor.fabTextColor,
                                  ).textStyle(context),
                                ),
                              ),
                              SizedBox(width: J17Padding.small),
                              FloatingActionButton(
                                mini: true,
                                backgroundColor: J17ThemeColor.actionAuxiliary
                                    .color(context),
                                key: const ValueKey('fab_label'),
                                heroTag: 'fab_label',
                                child: SvgIcon(
                                  icon: isJornada4
                                      ? J17Icons.pixAuto
                                      : J17Icons.documentText,
                                  color: J17ThemeColor.fabTextColor,
                                  size: J17IconSizes.regSmall,
                                ),
                                onPressed: () {
                                  setState(() => _showFabLabel = false);
                                  if (widget.jornada == 'JORNADA_4') {
                                    _showAdesaoBottomSheet();
                                  } else if (widget.jornada == 'JORNADA_3') {
                                    _showDefaultBottomSheet();
                                  }
                                },
                              ),
                            ],
                          ),
                        )
                      : FloatingActionButton(
                          mini: true,
                          key: const ValueKey('fab_icon'),
                          heroTag: 'fab_icon',
                          child: SvgIcon(
                            icon: isJornada4
                                ? J17Icons.pixAuto
                                : J17Icons.documentText,
                            color: J17ThemeColor.fabTextColor,
                            size: J17IconSizes.regSmall,
                          ),
                          onPressed: () {
                            setState(() => _showFabLabel = true);
                          },
                        ),
                ),
              ],
            )
          : null,
    );
  }

  double _extractValueFromResponse() {
    debugPrint('🎯 [RECEIPT] ==========================================');
    debugPrint('🎯 [RECEIPT] Extraindo valor da resposta');
    debugPrint('🎯 [RECEIPT] Jornada: ${widget.jornada}');
    debugPrint(
        '🎯 [RECEIPT] decodeResponse tipo: ${widget.decodeResponse.runtimeType}');
    debugPrint(
        '🎯 [RECEIPT] authResponse tipo: ${widget.authResponse.runtimeType}');
    debugPrint('🎯 [RECEIPT] authResponse: $widget.authResponse');
    debugPrint('🎯 [RECEIPT] ==========================================');

    // ✅ NOVO: Suporte específico para Jornada 1 - dados da listagem de autorizações
    if (widget.jornada == 'JORNADA_1' && widget.decodeResponse != null) {
      try {
        // Para Jornada 1, o decodeResponse pode ser um PixAutomaticAuthorization
        if (widget.decodeResponse is PixAutomaticAuthorization) {
          final auth = widget.decodeResponse as PixAutomaticAuthorization;
          // Para pagamento FIXO, usa o valor; para VARIAVEL, usa pisoValorMaximo
          if (auth.tipoPagamentoRecorrente == 'VARIAVEL') {
            debugPrint(
                '🎯 [RECEIPT] Jornada 1 - Pagamento VARIAVEL: ${auth.pisoValorMaximo}');
            return auth.pisoValorMaximo;
          } else {
            debugPrint(
                '🎯 [RECEIPT] Jornada 1 - Pagamento FIXO: ${auth.valor}');
            return auth.valor;
          }
        }
      } catch (e) {
        debugPrint('🎯 [RECEIPT] ❌ Erro ao extrair valor Jornada 1: $e');
      }
    }

    // ✅ NOVO: Suporte específico para Jornada 2 - dados do decode do QR Code
    if (widget.jornada == 'JORNADA_2' && widget.decodeResponse != null) {
      try {
        debugPrint('🎯 [RECEIPT] ✅ Processando valor Jornada 2...');
        if (widget.decodeResponse is PixDecodificaQrcodeData) {
          final decode = widget.decodeResponse as PixDecodificaQrcodeData;
          debugPrint(
              '🎯 [RECEIPT] ✅ PixDecodificaQrcodeData encontrado para valor');

          if (decode.decodificacaoQrcodeDTO?.dadosQrCodeComposto != null) {
            final dadosComposto =
                decode.decodificacaoQrcodeDTO!.dadosQrCodeComposto!;
            final valor = dadosComposto.valor ?? 0.0;
            final tipoPagamento =
                dadosComposto.tipoPagamentoRecorrente ?? 'FIXO';

            debugPrint(
                '🎯 [RECEIPT] ✅ Valor extraído - Valor: $valor, Tipo: $tipoPagamento');

            // Para pagamento FIXO, usa o valor; para VARIAVEL, usa pisoValorMaximo
            if (tipoPagamento == 'VARIAVEL') {
              final pisoValor = dadosComposto.pisoValorMaximo ?? 0.0;
              debugPrint(
                  '🎯 [RECEIPT] Jornada 2 - Pagamento VARIAVEL: $pisoValor');
              return pisoValor;
            } else {
              debugPrint('🎯 [RECEIPT] Jornada 2 - Pagamento FIXO: $valor');
              return valor;
            }
          }
        }
      } catch (e) {
        debugPrint('🎯 [RECEIPT] ❌ Erro ao extrair valor Jornada 2: $e');
      }
    }

    // ✅ NOVO: Prioriza dados da resposta da API de resposta-pagamento-recorrente (Jornada 1)
    if (widget.jornada == 'JORNADA_1' && widget.authResponse != null) {
      try {
        // A resposta da API de resposta-pagamento-recorrente pode conter dados específicos
        if (widget.authResponse!['valor'] != null) {
          final valor = (widget.authResponse!['valor'] as num).toDouble();
          debugPrint('🎯 [RECEIPT] Jornada 1 - Valor da API: $valor');
          return valor;
        }
      } catch (e) {
        debugPrint('🎯 [RECEIPT] ❌ Erro ao extrair valor da API Jornada 1: $e');
        // Ignora erro
      }
    }

    // Para Jornadas 3 e 4, prioriza o valor da API de envio de PIX
    if (widget.authResponse != null &&
        (widget.jornada == 'JORNADA_3' || widget.jornada == 'JORNADA_4')) {
      try {
        // A resposta da API de envio de PIX contém o valor real do PIX realizado
        if (widget.authResponse!['valor'] != null) {
          final valor = (widget.authResponse!['valor'] as num).toDouble();
          debugPrint('🎯 [RECEIPT] Jornadas 3/4 - Valor da API: $valor');
          return valor;
        }
        if (widget.authResponse!['valorPix'] != null) {
          final valor = (widget.authResponse!['valorPix'] as num).toDouble();
          debugPrint('🎯 [RECEIPT] Jornadas 3/4 - ValorPix da API: $valor');
          return valor;
        }
      } catch (e) {
        debugPrint(
            '🎯 [RECEIPT] ❌ Erro ao extrair valor da API Jornadas 3/4: $e');
        // Ignora erro
      }
    }

    // Para outras jornadas, prioriza o valor original da autorização
    if (widget.decodeResponse is AuthorizationDetails) {
      final valor =
          (widget.decodeResponse as AuthorizationDetails).originalValue;
      debugPrint('🎯 [RECEIPT] AuthorizationDetails - Valor original: $valor');
      return valor;
    }

    // Fallback para API apenas se não houver AuthorizationDetails
    if (widget.authResponse != null) {
      try {
        if (widget.authResponse!['valor'] != null) {
          final valor = (widget.authResponse!['valor'] as num).toDouble();
          debugPrint('🎯 [RECEIPT] Fallback - Valor da API: $valor');
          return valor;
        }
        if (widget.authResponse!['valorPix'] != null) {
          final valor = (widget.authResponse!['valorPix'] as num).toDouble();
          debugPrint('🎯 [RECEIPT] Fallback - ValorPix da API: $valor');
          return valor;
        }
      } catch (e) {
        debugPrint('🎯 [RECEIPT] ❌ Erro no fallback: $e');
        // Ignora erro
      }
    }

    debugPrint('🎯 [RECEIPT] ❌ Nenhum valor encontrado, retornando 0.0');
    return 0.0;
  }

  Map<String, String> _extractReceiverFullInfo() {
    // ✅ DEBUG: Log para verificar o tipo de decodeResponse
    print('[DEBUG][RECEIPT] ==========================================');
    print('[DEBUG][RECEIPT] Iniciando _extractReceiverFullInfo()');
    print(
        '[DEBUG][RECEIPT] Tipo de decodeResponse: ${widget.decodeResponse.runtimeType}');
    print('[DEBUG][RECEIPT] Jornada: ${widget.jornada}');
    print('[DEBUG][RECEIPT] ==========================================');

    // ✅ NOVO: Suporte específico para Jornada 1 - dados da listagem de autorizações
    if (widget.jornada == 'JORNADA_1' && widget.decodeResponse != null) {
      try {
        // Para Jornada 1, o decodeResponse pode ser um PixAutomaticAuthorization
        if (widget.decodeResponse is PixAutomaticAuthorization) {
          final auth = widget.decodeResponse as PixAutomaticAuthorization;
          print('[DEBUG][RECEIPT] ✅ PixAutomaticAuthorization encontrado');
          print('[DEBUG][RECEIPT] Recebedor nome: ${auth.recebedor.nome}');
          print('[DEBUG][RECEIPT] Recebedor cnpj: ${auth.recebedor.cnpj}');

          return {
            'nome': auth.recebedor.nome.isNotEmpty
                ? auth.recebedor.nome
                : 'Recebedor não informado',
            'documento': auth.recebedor.cnpj.toString(),
          };
        } else {
          print(
              '[DEBUG][RECEIPT] ❌ decodeResponse NÃO é PixAutomaticAuthorization');
        }
      } catch (e) {
        print('[DEBUG][RECEIPT] ❌ Erro ao extrair dados do recebedor: $e');
        // Ignora erro silenciosamente
      }
    }

    // ✅ NOVO: Suporte específico para Jornada 2 - dados do decode do QR Code
    if (widget.jornada == 'JORNADA_2' && widget.decodeResponse != null) {
      try {
        print('[DEBUG][RECEIPT] ✅ Processando Jornada 2...');
        if (widget.decodeResponse is PixDecodificaQrcodeData) {
          final decode = widget.decodeResponse as PixDecodificaQrcodeData;
          print('[DEBUG][RECEIPT] ✅ PixDecodificaQrcodeData encontrado');

          if (decode.decodificacaoQrcodeDTO?.dadosQrCodeComposto != null) {
            final dadosComposto =
                decode.decodificacaoQrcodeDTO!.dadosQrCodeComposto!;
            final nome =
                dadosComposto.recebedor?.nome ?? 'Recebedor não informado';
            final documento = dadosComposto.recebedor?.cnpj?.toString() ??
                'Documento não informado';

            print(
                '[DEBUG][RECEIPT] ✅ Dados extraídos - Nome: $nome, Documento: $documento');

            return {
              'nome': nome,
              'documento': documento,
            };
          }
        }
      } catch (e) {
        print(
            '[DEBUG][RECEIPT] ❌ Erro ao extrair dados do recebedor Jornada 2: $e');
        // Ignora erro silenciosamente
      }
    }

    // ✅ NOVO: Suporte específico para Jornada 3 - dados do AuthorizationDetails
    print('[DEBUG][RECEIPT] 🔍 Verificando se é Jornada 3...');
    print('[DEBUG][RECEIPT] widget.jornada: ${widget.jornada}');
    print(
        '[DEBUG][RECEIPT] widget.decodeResponse != null: ${widget.decodeResponse != null}');

    if (widget.jornada == 'JORNADA_3' && widget.decodeResponse != null) {
      print('[DEBUG][RECEIPT] ✅ Condição Jornada 3 atendida!');
      try {
        print('[DEBUG][RECEIPT] ✅ Processando Jornada 3...');
        print('[DEBUG][RECEIPT] Verificando se é AuthorizationDetails...');
        print(
            '[DEBUG][RECEIPT] widget.decodeResponse is AuthorizationDetails: ${widget.decodeResponse is AuthorizationDetails}');

        if (widget.decodeResponse is AuthorizationDetails) {
          final details = widget.decodeResponse as AuthorizationDetails;
          print('[DEBUG][RECEIPT] ✅ AuthorizationDetails encontrado');
          print('[DEBUG][RECEIPT] Recebedor nome: ${details.receiverName}');
          print(
              '[DEBUG][RECEIPT] Recebedor documento: ${details.receiverDocument}');

          final result = {
            'nome': details.receiverName.isNotEmpty
                ? details.receiverName
                : 'Recebedor não informado',
            'documento': details.receiverDocument.isNotEmpty
                ? details.receiverDocument
                : 'Documento não informado',
          };

          print('[DEBUG][RECEIPT] ✅ Retornando dados do recebedor: $result');
          return result;
        } else {
          print('[DEBUG][RECEIPT] ❌ decodeResponse NÃO é AuthorizationDetails');
        }
      } catch (e) {
        print(
            '[DEBUG][RECEIPT] ❌ Erro ao extrair dados do recebedor Jornada 3: $e');
        // Ignora erro silenciosamente
      }
    } else {
      print('[DEBUG][RECEIPT] ❌ Condição Jornada 3 NÃO atendida');
    }

    // Tenta extrair da resposta da API de envio de PIX (Jornadas 3 e 4)
    if (widget.authResponse != null) {
      try {
        // Para Jornadas 3 e 4, a resposta vem da API de envio de PIX
        if (widget.jornada == 'JORNADA_3' || widget.jornada == 'JORNADA_4') {
          // A resposta contém dados do PIX realizado
          final nome = widget.authResponse!['recebedor']?['nome'] ??
              widget.authResponse!['nomeRecebedor'] ??
              'Recebedor não informado';
          final documento = widget.authResponse!['recebedor']?['cnpj'] ??
              widget.authResponse!['documentoRecebedor'] ??
              'Documento não informado';
          return {'nome': nome, 'documento': documento};
        } else {
          // Para outras jornadas, usa a lógica anterior
          final nome = widget.authResponse!['recebedor']?['nome'] ??
              widget.authResponse!['nomeRecebedor'] ??
              'Recebedor não informado';
          final documento = widget.authResponse!['recebedor']?['cnpj'] ??
              widget.authResponse!['documentoRecebedor'] ??
              'Documento não informado';
          return {'nome': nome, 'documento': documento};
        }
      } catch (e) {
        // Ignora erro silenciosamente
      }
    }

    // Tenta extrair dados do decode do QR Code
    if (widget.decodeResponse != null) {
      try {
        if (widget.decodeResponse is PixDecodificaQrcodeData) {
          final decode = widget.decodeResponse as PixDecodificaQrcodeData;
          if (decode.decodificacaoQrcodeDTO != null) {
            final dto = decode.decodificacaoQrcodeDTO!;

            String nome = 'Recebedor não informado';
            String documento = 'Documento não informado';
            String chavePix = '';

            // Jornada 4: Prioriza dados do QR Code Dinâmico
            if (dto.dadosQrCodeDinamico != null) {
              final dinamico = dto.dadosQrCodeDinamico!;
              nome = dinamico.nomeRecebedor ?? nome;
              documento = dinamico.cpfCnpjRecebedor ?? documento;
              chavePix = dinamico.chave ?? '';
            }

            // Jornada 4: Depois usa dados do QR Code Composto
            if (dto.dadosQrCodeComposto != null) {
              final composto = dto.dadosQrCodeComposto!;
              if (nome == 'Recebedor não informado' &&
                  composto.recebedor?.nome != null) {
                nome = composto.recebedor!.nome!;
              }
              if (documento == 'Documento não informado' &&
                  composto.recebedor?.cnpj != null) {
                documento = composto.recebedor!.cnpj.toString();
              }
              if (chavePix.isEmpty && composto.chave != null) {
                chavePix = composto.chave!;
              }
            }

            // Jornada 4: Por último, usa dados do QR Code Estático
            if (dto.dadosQrCodeEstatico != null) {
              final estatico = dto.dadosQrCodeEstatico!;
              if (chavePix.isEmpty && estatico.chave != null) {
                chavePix = estatico.chave!;
              }
            }

            return {
              'nome': nome,
              'documento': documento,
              'chavePix': chavePix,
            };
          }
        }
      } catch (e) {
        // Ignora erro silenciosamente
      }
    }

    // Fallback para dados da decodificação
    if (widget.decodeResponse is AuthorizationDetails) {
      final d = widget.decodeResponse as AuthorizationDetails;
      return {
        'nome': d.receiverName.isNotEmpty
            ? d.receiverName
            : 'Recebedor não informado',
        'documento': d.receiverDocument.isNotEmpty
            ? d.receiverDocument
            : 'Documento não informado',
      };
    }

    return {
      'nome': 'Recebedor não informado',
      'documento': 'Documento não informado'
    };
  }

  Map<String, String> _extractPayerFullInfo() {
    print('[DEBUG][RECEIPT] ==========================================');
    print('[DEBUG][RECEIPT] Iniciando _extractPayerFullInfo()');
    print('[DEBUG][RECEIPT] ==========================================');

    // ✅ NOVO: Suporte específico para Jornada 1 - dados da listagem de autorizações
    if (widget.jornada == 'JORNADA_1' && widget.decodeResponse != null) {
      try {
        // Para Jornada 1, o decodeResponse pode ser um PixAutomaticAuthorization
        if (widget.decodeResponse is PixAutomaticAuthorization) {
          final auth = widget.decodeResponse as PixAutomaticAuthorization;
          return {
            'nome': auth.pagador.nome.isNotEmpty
                ? auth.pagador.nome
                : 'Pagador não informado',
            'banco': auth.pagador.ispb.toString(),
            'agencia': auth.pagador.numeroAgencia,
            'conta': auth.pagador.numeroConta,
            'documento': auth.pagador.cpfCnpj.toString(),
            'tipoConta': auth.pagador.tipoConta.name,
          };
        }
      } catch (e) {
        // Ignora erro silenciosamente
      }
    }

    // ✅ NOVO: Suporte específico para Jornada 2 - dados do decode do QR Code
    if (widget.jornada == 'JORNADA_2' && widget.decodeResponse != null) {
      try {
        print('[DEBUG][RECEIPT] ✅ Processando pagador Jornada 2...');
        if (widget.decodeResponse is PixDecodificaQrcodeData) {
          final decode = widget.decodeResponse as PixDecodificaQrcodeData;
          print(
              '[DEBUG][RECEIPT] ✅ PixDecodificaQrcodeData encontrado para pagador');

          if (decode.decodificacaoQrcodeDTO?.dadosQrCodeComposto != null) {
            final dadosComposto =
                decode.decodificacaoQrcodeDTO!.dadosQrCodeComposto!;
            final nome = dadosComposto.devedor?.nome ?? 'Pagador não informado';
            final documento = dadosComposto.devedor?.cpfCnpj?.toString() ??
                'Documento não informado';
            final tipoPessoa =
                dadosComposto.devedor?.tipoPessoa ?? 'PESSOA_FISICA';

            print(
                '[DEBUG][RECEIPT] ✅ Dados do pagador extraídos - Nome: $nome, Documento: $documento');

            return {
              'nome': nome,
              'banco': 'Banco não informado',
              'agencia': '----',
              'conta': '----',
              'documento': documento,
              'tipoConta': tipoPessoa == 'PESSOA_FISICA'
                  ? 'Conta Corrente'
                  : 'Conta Corrente',
            };
          }
        }
      } catch (e) {
        print(
            '[DEBUG][RECEIPT] ❌ Erro ao extrair dados do pagador Jornada 2: $e');
        // Ignora erro silenciosamente
      }
    }

    // ✅ NOVO: Suporte específico para Jornada 3 - dados do AuthorizationDetails
    print('[DEBUG][RECEIPT] 🔍 Verificando se é Jornada 3 (pagador)...');
    print('[DEBUG][RECEIPT] widget.jornada: ${widget.jornada}');
    print(
        '[DEBUG][RECEIPT] widget.decodeResponse != null: ${widget.decodeResponse != null}');

    if (widget.jornada == 'JORNADA_3' && widget.decodeResponse != null) {
      print('[DEBUG][RECEIPT] ✅ Condição Jornada 3 atendida! (pagador)');
      try {
        print('[DEBUG][RECEIPT] ✅ Processando pagador Jornada 3...');
        print(
            '[DEBUG][RECEIPT] Verificando se é AuthorizationDetails (pagador)...');
        print(
            '[DEBUG][RECEIPT] widget.decodeResponse is AuthorizationDetails: ${widget.decodeResponse is AuthorizationDetails}');

        if (widget.decodeResponse is AuthorizationDetails) {
          final details = widget.decodeResponse as AuthorizationDetails;
          print(
              '[DEBUG][RECEIPT] ✅ AuthorizationDetails encontrado para pagador');
          print('[DEBUG][RECEIPT] Pagador nome: ${details.payerName}');
          print('[DEBUG][RECEIPT] Pagador documento: ${details.payerDocument}');
          print('[DEBUG][RECEIPT] Pagador banco: ${details.payerBank}');
          print('[DEBUG][RECEIPT] Pagador agencia: ${details.payerAgency}');
          print('[DEBUG][RECEIPT] Pagador conta: ${details.payerAccount}');

          final result = {
            'nome': details.payerName?.isNotEmpty == true
                ? details.payerName!
                : 'Pagador não informado',
            'banco': details.payerBank?.isNotEmpty == true
                ? details.payerBank!
                : 'Banco não informado',
            'agencia': details.payerAgency?.isNotEmpty == true
                ? details.payerAgency!
                : '----',
            'conta': details.payerAccount?.isNotEmpty == true
                ? details.payerAccount!
                : '----',
            'documento': details.payerDocument?.isNotEmpty == true
                ? details.payerDocument!
                : 'Documento não informado',
            'tipoConta': details.payerAccountType?.isNotEmpty == true
                ? details.payerAccountType!
                : 'Tipo de conta não informado',
          };

          print('[DEBUG][RECEIPT] ✅ Retornando dados do pagador: $result');
          return result;
        } else {
          print(
              '[DEBUG][RECEIPT] ❌ decodeResponse NÃO é AuthorizationDetails (pagador)');
        }
      } catch (e) {
        print(
            '[DEBUG][RECEIPT] ❌ Erro ao extrair dados do pagador Jornada 3: $e');
        // Ignora erro silenciosamente
      }
    } else {
      print('[DEBUG][RECEIPT] ❌ Condição Jornada 3 NÃO atendida (pagador)');
    }

    // Tenta extrair da resposta da API de envio de PIX (Jornadas 3 e 4)
    if (widget.authResponse != null) {
      try {
        // Para Jornadas 3 e 4, a resposta vem da API de envio de PIX
        if (widget.jornada == 'JORNADA_3' || widget.jornada == 'JORNADA_4') {
          // A resposta contém dados do PIX realizado
          final nome = widget.authResponse!['pagador']?['nome'] ??
              'Pagador não informado';
          final banco = widget.authResponse!['pagador']?['banco'] ??
              "Banco não informado";
          final agencia =
              widget.authResponse!['pagador']?['numeroAgencia'] ?? '----';
          final conta =
              widget.authResponse!['pagador']?['numeroConta'] ?? '----';
          final documento = widget.authResponse!['pagador']?['cpfCnpj'] ??
              'Documento não informado';
          return {
            'nome': nome,
            'banco': banco,
            'agencia': agencia,
            'conta': conta,
            'documento': documento
          };
        } else {
          // Para outras jornadas, usa a lógica anterior
          final nome = widget.authResponse!['recebedor']?['nome'] ??
              widget.authResponse!['nomeRecebedor'] ??
              'Recebedor não informado';
          final documento = widget.authResponse!['recebedor']?['cnpj'] ??
              widget.authResponse!['documentoRecebedor'] ??
              'Documento não informado';
          return {'nome': nome, 'documento': documento};
        }
      } catch (e) {
        // Ignora erro silenciosamente
      }
    }
    // Fallback para dados da decodificação
    if (widget.decodeResponse is AuthorizationDetails) {
      final d = widget.decodeResponse as AuthorizationDetails;
      return {
        'nome': (d.payerName?.isNotEmpty == true
                ? d.payerName
                : 'Pagador não informado') ??
            'Pagador não informado',
        'banco': (d.payerBank?.isNotEmpty == true
                ? d.payerBank
                : 'Banco não informado') ??
            'Banco não informado',
        'agencia':
            (d.payerAgency?.isNotEmpty == true ? d.payerAgency : '----') ??
                '----',
        'conta':
            (d.payerAccount?.isNotEmpty == true ? d.payerAccount : '----') ??
                '----',
        'documento': (d.payerDocument?.isNotEmpty == true
                ? d.payerDocument
                : 'Documento não informado') ??
            'Documento não informado',
        'tipoConta': (d.payerAccountType?.isNotEmpty == true
                ? d.payerAccountType
                : 'Tipo de conta não informado') ??
            'Tipo de conta não informado',
      };
    }
    return {
      'nome': 'Pagador não informado',
      'banco': 'Banco não informado',
      'tipoConta': 'Tipo de conta não informado',
      'agencia': '----',
      'conta': '----',
      'documento': 'Documento não informado'
    };
  }

  String _extractPixIdFromResponse() {
    // ✅ NOVO: Suporte específico para Jornada 1 - dados da listagem de autorizações
    if (widget.jornada == 'JORNADA_1' && widget.decodeResponse != null) {
      try {
        // Para Jornada 1, o decodeResponse pode ser um PixAutomaticAuthorization
        if (widget.decodeResponse is PixAutomaticAuthorization) {
          final auth = widget.decodeResponse as PixAutomaticAuthorization;
          // Usa o ID da recorrência como ID da transação
          return auth.idRecorrencia;
        }
      } catch (e) {
        // Ignora erro
      }
    }

    // ✅ NOVO: Prioriza dados da resposta da API de resposta-pagamento-recorrente (Jornada 1)
    if (widget.jornada == 'JORNADA_1' && widget.authResponse != null) {
      try {
        // A resposta da API de resposta-pagamento-recorrente pode conter IDs específicos
        if (widget.authResponse!['idRecorrencia'] != null) {
          return widget.authResponse!['idRecorrencia'].toString();
        }
        if (widget.authResponse!['nsu'] != null) {
          return 'NSU_${widget.authResponse!['nsu']}';
        }
      } catch (e) {
        // Ignora erro
      }
    }

    if (widget.authResponse != null) {
      try {
        // Para Jornadas 3 e 4, a resposta da API de envio de PIX contém o ID real do PIX
        if (widget.jornada == 'JORNADA_3' || widget.jornada == 'JORNADA_4') {
          // Prioriza idFimAFim da API de envio de PIX
          if (widget.authResponse!['idFimAFim'] != null) {
            return widget.authResponse!['idFimAFim'].toString();
          }
          if (widget.authResponse!['nsu'] != null) {
            return widget.authResponse!['nsu'].toString();
          }
        } else {
          // Para outras jornadas, usa a lógica anterior
          if (widget.authResponse!['idPix'] != null) {
            return widget.authResponse!['idPix'].toString();
          }
          if (widget.authResponse!['nsu'] != null) {
            return widget.authResponse!['nsu'].toString();
          }
          if (widget.authResponse!['endToEndId'] != null) {
            return widget.authResponse!['endToEndId'].toString();
          }
        }
      } catch (e) {
        // Ignora erro
      }
    }

    return 'PIX_${DateTime.now().millisecondsSinceEpoch}';
  }

  Future<void> _shareReceipt(BuildContext context) async {
    await _themeChanger.withLightThemeForSharing(() async {
      // Aguarda um pouco para garantir que o layout foi renderizado
      await Future.delayed(
        const Duration(milliseconds: 500),
      );

      Uint8List? image = await loadingAround(
        context,
        _screenshotController.capture(),
        shouldShowErrorMessage: false,
      );

      if (image != null) {
        try {
          var params = ShareParams(
            files: [XFile.fromData(image, mimeType: 'image/png')],
            fileNameOverrides: ['comprovante_pix_automatico.png'],
          );
          ShareResult result = await SharePlus.instance.share(params);
          print("Resultado do share: ${result.status.name}");
        } catch (e) {
          print("Erro ao compartilhar: $e");
        }
      }
    });
  }

  void _navigateToNewAuthorization(BuildContext context) {
    // Para Jornada 1, navega para autorizações pendentes
    // Para outras jornadas, volta para o menu principal
    if (widget.jornada == 'JORNADA_1') {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (context) => PendingAuthorizationsViewModel(),
            child: const PendingAuthorizationsPage(),
          ),
        ),
        (route) => route.isFirst,
      );
    } else {
      // Navega de volta para o menu do Pix Automático para uma nova autorização
      Navigator.popUntil(context, (route) => route.isFirst);
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/'
        '${dateTime.month.toString().padLeft(2, '0')}/'
        '${dateTime.year} às '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// ✅ NOVO: Extrai o ID da autorização da resposta da API
  String _extractAuthIdFromResponse() {
    // ✅ NOVO: Prioriza dados da resposta da API de resposta-pagamento-recorrente (Jornada 1)
    if (widget.jornada == 'JORNADA_1' && widget.authResponse != null) {
      try {
        // A resposta da API de resposta-pagamento-recorrente pode conter IDs específicos
        if (widget.authResponse!['idRequisicaoJdPi'] != null) {
          return widget.authResponse!['idRequisicaoJdPi'].toString();
        }
        if (widget.authResponse!['codigo'] != null) {
          return 'AUTH_${widget.authResponse!['codigo']}';
        }
      } catch (e) {
        // Ignora erro
      }
    }

    if (widget.authResponse != null) {
      try {
        // Para Jornadas 3 e 4, a resposta da API de envio de PIX pode conter dados de autorização
        if (widget.jornada == 'JORNADA_3' || widget.jornada == 'JORNADA_4') {
          // Para Jornada 3, pode ter dados de autorização na resposta
          if (widget.authResponse!['idAutorizacao'] != null) {
            return widget.authResponse!['idAutorizacao'].toString();
          }
          if (widget.authResponse!['idRecorrencia'] != null) {
            return widget.authResponse!['idRecorrencia'].toString();
          }
          if (widget.authResponse!['codigoRecorrencia'] != null) {
            return widget.authResponse!['codigoRecorrencia'].toString();
          }
          // Para Jornada 4, usa o idFimAFim como ID de autorização
          if (widget.authResponse!['idFimAFim'] != null) {
            return 'AUTH_${widget.authResponse!['idFimAFim']}';
          }
        } else {
          // Para outras jornadas, usa a lógica anterior
          if (widget.authResponse!['idAutorizacao'] != null) {
            return widget.authResponse!['idAutorizacao'].toString();
          }
          if (widget.authResponse!['idRecorrencia'] != null) {
            return widget.authResponse!['idRecorrencia'].toString();
          }
          if (widget.authResponse!['codigoRecorrencia'] != null) {
            return widget.authResponse!['codigoRecorrencia'].toString();
          }
        }
      } catch (e) {
        // Ignora erro
      }
    }

    return 'AUTH_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// ✅ NOVO: Extrai a mensagem de status da resposta da API
  String _extractStatusMessage() {
    // ✅ NOVO: Prioriza dados da resposta da API de resposta-pagamento-recorrente (Jornada 1)
    if (widget.jornada == 'JORNADA_1' && widget.authResponse != null) {
      try {
        // A resposta da API de resposta-pagamento-recorrente pode conter mensagem de status
        if (widget.authResponse!['mensagem'] != null) {
          return widget.authResponse!['mensagem'].toString();
        }
        if (widget.authResponse!['codigo'] != null) {
          final codigo = widget.authResponse!['codigo'].toString();
          if (codigo == '200') {
            return 'Autorização aceita com sucesso';
          } else {
            return 'Código de resposta: $codigo';
          }
        }
      } catch (e) {
        // Ignora erro
      }
    }

    // Fallback para outras jornadas
    if (widget.authResponse != null) {
      try {
        if (widget.authResponse!['mensagem'] != null) {
          return widget.authResponse!['mensagem'].toString();
        }
        if (widget.authResponse!['status'] != null) {
          return widget.authResponse!['status'].toString();
        }
      } catch (e) {
        // Ignora erro
      }
    }

    return 'Autorização processada';
  }

  /// ✅ NOVO: Extrai a data/hora da requisição da resposta da API
  String _extractRequestDateTime() {
    // ✅ NOVO: Prioriza dados da resposta da API de resposta-pagamento-recorrente (Jornada 1)
    if (widget.jornada == 'JORNADA_1' && widget.authResponse != null) {
      try {
        // A resposta da API de resposta-pagamento-recorrente pode conter data/hora da requisição
        if (widget.authResponse!['dataHoraRequisicaoJdPi'] != null) {
          final dateTimeStr =
              widget.authResponse!['dataHoraRequisicaoJdPi'].toString();
          try {
            final dateTime = DateTime.parse(dateTimeStr);
            return _formatDateTime(dateTime);
          } catch (e) {
            return dateTimeStr;
          }
        }
      } catch (e) {
        // Ignora erro
      }
    }

    // Fallback para outras jornadas
    if (widget.authResponse != null) {
      try {
        if (widget.authResponse!['dataHoraCriacao'] != null) {
          final dateTimeStr =
              widget.authResponse!['dataHoraCriacao'].toString();
          try {
            final dateTime = DateTime.parse(dateTimeStr);
            return _formatDateTime(dateTime);
          } catch (e) {
            return dateTimeStr;
          }
        }
      } catch (e) {
        // Ignora erro
      }
    }

    return _formatDateTime(DateTime.now());
  }

  /// ✅ NOVO: Extrai o código de origem da resposta da API
  String _extractOriginCode() {
    // ✅ NOVO: Prioriza dados da resposta da API de resposta-pagamento-recorrente (Jornada 1)
    if (widget.jornada == 'JORNADA_1' && widget.authResponse != null) {
      try {
        // A resposta da API de resposta-pagamento-recorrente pode conter código de origem
        if (widget.authResponse!['codigoOrigem'] != null) {
          return widget.authResponse!['codigoOrigem'].toString();
        }
      } catch (e) {
        // Ignora erro
      }
    }

    return 'N/A';
  }

  /// ✅ NOVO: Extrai o NSU da resposta da API
  String _extractNSU() {
    // ✅ NOVO: Prioriza dados da resposta da API de resposta-pagamento-recorrente (Jornada 1)
    if (widget.jornada == 'JORNADA_1' && widget.authResponse != null) {
      try {
        // A resposta da API de resposta-pagamento-recorrente pode conter NSU
        if (widget.authResponse!['nsu'] != null) {
          return widget.authResponse!['nsu'].toString();
        }
      } catch (e) {
        // Ignora erro
      }
    }

    // Fallback para outras jornadas
    if (widget.authResponse != null) {
      try {
        if (widget.authResponse!['nsu'] != null) {
          return widget.authResponse!['nsu'].toString();
        }
      } catch (e) {
        // Ignora erro
      }
    }

    return 'N/A';
  }

  /// ✅ NOVO: Extrai a data de referência da resposta da API
  String _extractReferenceDate() {
    // ✅ NOVO: Prioriza dados da resposta da API de resposta-pagamento-recorrente (Jornada 1)
    if (widget.jornada == 'JORNADA_1' && widget.authResponse != null) {
      try {
        // A resposta da API de resposta-pagamento-recorrente pode conter data de referência
        if (widget.authResponse!['dataReferencia'] != null) {
          final dateStr = widget.authResponse!['dataReferencia'].toString();
          try {
            final date = DateTime.parse(dateStr);
            return '${date.day.toString().padLeft(2, '0')}/'
                '${date.month.toString().padLeft(2, '0')}/'
                '${date.year}';
          } catch (e) {
            return dateStr;
          }
        }
      } catch (e) {
        // Ignora erro
      }
    }

    return 'N/A';
  }

  /// ✅ NOVO: Extrai informações adicionais da resposta da API
  String _extractAdditionalInfo() {
    // ✅ NOVO: Prioriza dados da resposta da API de resposta-pagamento-recorrente (Jornada 1)
    if (widget.jornada == 'JORNADA_1' && widget.authResponse != null) {
      try {
        // A resposta da API de resposta-pagamento-recorrente pode conter informações adicionais
        if (widget.authResponse!['informacoesAdicionais'] != null) {
          final info = widget.authResponse!['informacoesAdicionais']
              as Map<String, dynamic>?;
          if (info != null && info['senhaTransacao'] != null) {
            final senha = info['senhaTransacao'] as Map<String, dynamic>?;
            if (senha != null && senha['id'] != null) {
              return 'ID Senha: ${senha['id']}';
            }
          }
        }
      } catch (e) {
        // Ignora erro
      }
    }

    // Fallback para informação padrão
    return 'Informações adicionais não disponíveis';
  }

  // Ajuste: Recebe flag para Jornada 4
  void _showPixReceipt({bool jornada4 = false}) {
    final valor = _extractValueFromResponse();
    final receiver = _extractReceiverFullInfo();
    final payer = _extractPayerFullInfo();
    final dataPagamento = DateTime.now();

    final receipt = <String, dynamic>{
      'valor': valor,
      'recebedor': receiver['nome'] ?? '',
      'cnpjRecebedor': receiver['documento'] ?? '',
      'pagador': payer['nome'] ?? '',
      'cpfPagador': payer['documento'] ?? '',
      'dataPagamento':
          '${dataPagamento.day.toString().padLeft(2, '0')}/${dataPagamento.month.toString().padLeft(2, '0')}/${dataPagamento.year}',
      'codigoCliente': receiver['conta'] ?? '',
      'descricao': widget.decodeResponse?.paymentDescription ??
          'Pagamento PIX Automático',
    };

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => PixAutomaticReceiptPage(receipt: receipt),
      ),
    ).then((_) {
      if (jornada4) {
        _showAdesaoBottomSheet();
      }
    });
  }
}
