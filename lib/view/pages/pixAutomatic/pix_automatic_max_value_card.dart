import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/view/components/field/format/format_currency.dart';

class PixAutomaticMaxValueCard extends StatelessWidget {
  final bool isEnabled;
  final bool maxValueEnabled;
  final TextEditingController maxValueController;
  final String? errorText;
  final VoidCallback? onSwitchChanged;
  final VoidCallback? onFieldChanged;

  const PixAutomaticMaxValueCard({
    super.key,
    required this.isEnabled,
    required this.maxValueEnabled,
    required this.maxValueController,
    this.errorText,
    this.onSwitchChanged,
    this.onFieldChanged,
  });

  @override
  Widget build(BuildContext context) {
    if (!isEnabled) {
      return const SizedBox.shrink();
    }
    return Container(
      decoration: BoxDecoration(
        color: J17ThemeColor.surfacesElevate.color(context),
        borderRadius: BorderRadius.circular(J17Padding.small),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              right: J17Padding.regular,
              left: J17Padding.regular,
              top: J17Padding.small,
              bottom: J17Padding.small,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Editar valor máximo',
                  style: J17TextStyles.labelLarge(
                    bold: true,
                  ).textStyle(context),
                ),
                Switch(
                  value: maxValueEnabled,
                  onChanged: onSwitchChanged != null
                      ? (_) => onSwitchChanged!()
                      : null,
                ),
              ],
            ),
          ),
          if (maxValueEnabled) ...[
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: J17Padding.regular,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextFormField(
                    controller: maxValueController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      textInputFormatterCurrencyLimited,
                    ],
                    decoration: InputDecoration(
                      prefixStyle: J17TextStyles.bodyLarge(
                        color: J17ThemeColor.textSecondaryColor,
                      ).textStyle(context),
                      hintText: 'R\$ 0,00',
                      errorText: errorText,
                      errorMaxLines: 2,
                    ),
                    style: J17TextStyles.titleLarge(
                      color: errorText == null
                          ? J17ThemeColor.textPrimaryColor
                          : J17ThemeColor.textTextErrors,
                      bold: true,
                    ).textStyle(context),
                    onChanged: onFieldChanged != null
                        ? (_) => onFieldChanged!()
                        : null,
                  ),
                  const SizedBox(height: J17Padding.regular),
                  Text(
                    'Caso o valor do pagamento recorrente seja superior ao valor máximo definido, o pagamento não será agendado e você será avisado.',
                    style: J17TextStyles.bodyMedium().textStyle(context),
                  ),
                  const SizedBox(height: J17Padding.regular),
                  Text(
                    'Esta autorização prevê tentativas de pagamentos após a data de vencimento, caso o pagamento original não possa ser realizado por insuficiência de saldo.',
                    style: J17TextStyles.bodyMedium().textStyle(context),
                  ),
                  const SizedBox(height: J17Padding.regular),
                  Text(
                    'Pagamentos realizados após a data de vencimento podem resultar em juros e multas acrescidos ao pagamento seguinte.',
                    style: J17TextStyles.bodyMedium().textStyle(context),
                  ),
                  const SizedBox(height: J17Padding.regular),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
