import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/string_util.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';
import 'package:j17_bank_mybank_mobile/view/components/button/j17_button.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/authorization_details_page.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/pix_automatic_additional_info_expansion.dart';

/// **JORNADAS 2, 3 e 4**: Página de confirmação das informações
///
/// Esta página exibe as informações decodificadas do QR Code e permite ao usuário
/// confirmar se deseja prosseguir com a autorização (Jornada 2), pagamento + autorização (Jornada 3)
/// ou com a adesão (Jornada 4).
class AuthorizationConfirmationPage extends StatefulWidget {
  final AuthorizationDetails details;
  final String jornada;
  final VoidCallback onConfirm;
  final VoidCallback onCancel;

  const AuthorizationConfirmationPage({
    super.key,
    required this.details,
    required this.jornada,
    required this.onConfirm,
    required this.onCancel,
  });

  @override
  State<AuthorizationConfirmationPage> createState() =>
      _AuthorizationConfirmationPageState();
}

class _AuthorizationConfirmationPageState
    extends State<AuthorizationConfirmationPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: J17ThemeColor.surfacesBackground.color(context),
      appBar: AppBar(
        backgroundColor: J17ThemeColor.surfacesBackground.color(context),
        title: Text(
          'Confirme as informações',
          style: J17TextStyles.titleLarge().textStyle(context),
        ),
        centerTitle: true,
        elevation: 0,
        leading: BackButton(),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(J17Padding.regular),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildPagadorCard(),
            const SizedBox(height: 12),
            PixAutomaticAdditionalInfoExpansion(
              paymentDescription: widget.details.paymentDescription,
              clientCode: widget.details.clientCode,
              paymentDate: widget.details.paymentDate,
              nextPayments: widget.details.nextPayments,
              authorizationTerm: widget.details.authorizationTerm,
            ),
            const SizedBox(height: 12),
            _buildPagamentoAutorizacaoCard(),
            if (widget.jornada == 'JORNADA_3') ...[
              const SizedBox(height: 12),
              _buildJornada3InfoCard(context),
            ],
            const SizedBox(height: 24),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildPagadorCard() {
    return Container(
      decoration: BoxDecoration(
        color: J17ThemeColor.surfacesElevate.color(context),
        borderRadius: BorderRadius.circular(J17BorderRadius.small),
      ),
      padding: const EdgeInsets.all(J17Padding.regular),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recebedor',
            style: J17TextStyles.titleSmall(
                    bold: true, color: J17ThemeColor.textPrimaryColor)
                .textStyle(context),
          ),
          const SizedBox(height: J17Padding.small),
          Text(
            widget.details.receiverName,
            style: J17TextStyles.bodyLarge(
                    bold: true, color: J17ThemeColor.textPrimaryColor)
                .textStyle(context),
          ),
          if (widget.details.receiverDocument.isNotEmpty &&
              widget.details.receiverDocument != '-')
            Text(
              _formatDocument(widget.details.receiverDocument),
              style: J17TextStyles.bodyMedium(
                      color: J17ThemeColor.textSecondaryColor)
                  .textStyle(context),
            ),
        ],
      ),
    );
  }

  Widget _buildPagamentoAutorizacaoCard() {
    String title;
    String description;
    Color cardColor;

    switch (widget.jornada) {
      case 'JORNADA_1':
        title = 'Autorização pendente';
        description =
            'Você tem uma autorização pendente para pagamentos recorrentes. '
            'Revise as informações e continue para autorizar.';
        cardColor = Colors.orange.withOpacity(0.1);
        break;
      case 'JORNADA_2':
        title = 'Autorização pendente';
        description =
            'Você tem uma autorização pendente para pagamentos recorrentes. '
            'Revise as informações e continue para autorizar.';
        cardColor = Colors.orange.withOpacity(0.1);
        break;
      case 'JORNADA_3':
        title = 'Pagamento + Autorização';
        description =
            'Você realizará um pagamento imediato e autorizará pagamentos recorrentes futuros '
            'para este recebedor. Confirme as informações antes de prosseguir.';
        cardColor = Colors.blue.withOpacity(0.1);
        break;
      case 'JORNADA_4':
        title = 'Adesão ao Pix Automático';
        description =
            'Após realizar este pagamento, você poderá aderir ao Pix Automático '
            'para facilitar pagamentos futuros para este recebedor.';
        cardColor = Colors.green.withOpacity(0.1);
        break;
      default:
        title = 'Pagamento Pix Automático';
        description =
            'Confirme as informações antes de prosseguir com o pagamento.';
        cardColor = Colors.orange.withOpacity(0.1);
        break;
    }

    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(J17BorderRadius.small),
        border: Border.all(
          color: J17ThemeColor.surfacesBorderRadius.color(context),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(J17Padding.regular),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: J17TextStyles.titleSmall(
                    bold: true, color: J17ThemeColor.textPrimaryColor)
                .textStyle(context),
          ),
          const SizedBox(height: J17Padding.small),
          Text(
            description,
            style: J17TextStyles.bodyMedium(
                    color: J17ThemeColor.textSecondaryColor)
                .textStyle(context),
          ),
        ],
      ),
    );
  }

  Widget _buildJornada3InfoCard(BuildContext context) {
    final valor = widget.details?.value ?? 0.0;
    return Container(
      decoration: BoxDecoration(
        color: J17ThemeColor.surfacesElevate.color(context),
        borderRadius: BorderRadius.circular(J17BorderRadius.small),
      ),
      padding: const EdgeInsets.all(J17Padding.regular),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(right: 12.0, top: 2.0),
            child: SvgIcon(
              icon: J17Icons.infoCircle,
              color: J17ThemeColor.textSecondaryColor,
              size: J17IconSizes.regLarge,
            ),
          ),
          Expanded(
            child: Text(
              'O primeiro pagamento no valor ${valor.toStringAsFixed(2).formatToBrlCurrency} será efetuado imediatamente, dando sequência ao processo de autorização do Pix Automático.',
              style: J17TextStyles.bodyMedium(
                      color: J17ThemeColor.textSecondaryColor)
                  .textStyle(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        J17Button(
          text: 'Confirmar',
          onPressed: widget.onConfirm,
          variant: J17ButtonVariant.primary,
        ),
        const SizedBox(height: J17Padding.regular),
        J17Button(
          text: 'Cancelar',
          onPressed: () =>
              Navigator.popUntil(context, (route) => route.isFirst),
          variant: J17ButtonVariant.outline,
        ),
      ],
    );
  }

  String _formatDocument(String document) {
    // Remove todos os caracteres não numéricos
    final cleanDoc = document.replaceAll(RegExp(r'[^0-9]'), '');

    if (cleanDoc.length == 11) {
      // CPF: 000.000.000-00
      return '${cleanDoc.substring(0, 3)}.${cleanDoc.substring(3, 6)}.${cleanDoc.substring(6, 9)}-${cleanDoc.substring(9)}';
    } else if (cleanDoc.length == 14) {
      // CNPJ: 00.000.000/0000-00
      return '${cleanDoc.substring(0, 2)}.${cleanDoc.substring(2, 5)}.${cleanDoc.substring(5, 8)}/${cleanDoc.substring(8, 12)}-${cleanDoc.substring(12)}';
    }

    // Retorna o documento original se não conseguir formatar
    return document;
  }
}
