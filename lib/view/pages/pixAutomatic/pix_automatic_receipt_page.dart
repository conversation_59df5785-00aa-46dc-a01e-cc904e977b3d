import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/capture_theme.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';
import 'package:j17_bank_mybank_mobile/view/components/loading/loading_modal.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/info_rows.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/section_card.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/info_column.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/share_button.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/new_pix_button.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/receipt_header.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';

class PixAutomaticReceiptPage extends StatefulWidget {
  final Map<String, dynamic> receipt;

  const PixAutomaticReceiptPage({
    super.key,
    required this.receipt,
  });

  @override
  State<PixAutomaticReceiptPage> createState() =>
      _PixAutomaticReceiptPageState();
}

class _PixAutomaticReceiptPageState extends State<PixAutomaticReceiptPage> {
  final ScreenshotController _screenshotController = ScreenshotController();
  late CaptureThemeChanger _themeChanger;

  @override
  Widget build(BuildContext context) {
    _themeChanger = Provider.of<CaptureThemeChanger>(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Comprovante',
          style: J17TextStyles.headlineSmall().textStyle(context),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const SvgIcon(
              icon: J17Icons.home2,
            ),
            onPressed: () {
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(J17Padding.regular),
        child: Screenshot(
          controller: _screenshotController,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              ReceiptHeader(valor: widget.receipt['valor']),
              const SizedBox(height: J17Padding.large),
              SectionCard(
                title: 'Transação',
                children: [
                  SizedBox(height: J17Padding.regular),
                  InfoRowSpaced(
                    labelLeading: 'Data',
                    valueLeading: widget.receipt['dataPagamento'] ?? '-',
                    labelTrailing: 'Horário',
                    valueTrailing: widget.receipt['horaPagamento'] ?? '-',
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    label: 'ID da transação',
                    value: widget.receipt['idTransacao'] ?? '-',
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    label: 'Descrição',
                    value: widget.receipt['descricao'] ?? '-',
                  ),
                  SizedBox(height: J17Padding.regular),
                ],
              ),
              const SizedBox(height: J17Padding.large),
              SectionCard(
                title: 'Quem recebeu',
                children: [
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    value: widget.receipt['recebedor'] ?? '-',
                    isTitle: true,
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    label: 'Banco',
                    value: widget.receipt['bancoRecebedor'] ?? '-',
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoRowDoubleColumn(
                    labelLeading: 'Agência',
                    valueLeading: widget.receipt['agenciaRecebedor'] ?? '-',
                    labelTrailing: 'Conta',
                    valueTrailing: widget.receipt['contaRecebedor'] ?? '-',
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    label: 'Chave Pix',
                    value: widget.receipt['chavePixRecebedor'] ?? '-',
                  ),
                  SizedBox(height: J17Padding.regular),
                ],
              ),
              const SizedBox(height: J17Padding.large),
              SectionCard(
                title: 'Quem enviou',
                children: [
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    value: widget.receipt['pagador'] ?? '-',
                    isTitle: true,
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    label: 'Banco',
                    value: widget.receipt['bancoPagador'] ?? '-',
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoRowDoubleColumn(
                    labelLeading: 'Agência',
                    valueLeading: widget.receipt['agenciaPagador'] ?? '-',
                    labelTrailing: 'Conta',
                    valueTrailing: widget.receipt['contaPagador'] ?? '-',
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    label: 'CPF',
                    value: widget.receipt['cpfPagador'] ?? '-',
                  ),
                  SizedBox(height: J17Padding.regular),
                  InfoColumn(
                    label: 'Tipo de conta',
                    value: widget.receipt['tipoContaPagador'] ?? '-',
                  ),
                  SizedBox(height: J17Padding.regular),
                ],
              ),
              // const SizedBox(height: J17Padding.large), //TODO: Implementar ajuda e descomentar
              // const HelpDynamicTile(
              //   margin: 0.0,
              // ),
              const SizedBox(height: J17Padding.large),
              ShareButton(
                  onPressed: () => _shareReceipt(context)),
              const SizedBox(height: J17Padding.large),
              NewPixButton(
                onPressed: () {
                  Navigator.popUntil(context, (route) => route.isFirst);
                },
              ),
              const SizedBox(height: J17Padding.regular),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _shareReceipt(BuildContext context) async {
    await _themeChanger.withLightThemeForSharing(() async {
      // Aguarda um pouco para garantir que o layout foi renderizado
      await Future.delayed(
        const Duration(milliseconds: 500),
      );

      Uint8List? image = await loadingAround(
        context,
        _screenshotController.capture(),
        shouldShowErrorMessage: false,
      );

      if (image != null) {
        try {
          var params = ShareParams(
            files: [XFile.fromData(image, mimeType: 'image/png')],
            fileNameOverrides: ['comprovante_pix_automatico.png'],
          );
          ShareResult result = await SharePlus.instance.share(params);
          print("Resultado do share: ${result.status.name}");
        } catch (e) {
          print("Erro ao compartilhar: $e");
        }
      }
    });
  }
}
