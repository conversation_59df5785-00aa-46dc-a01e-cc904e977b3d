import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/view/components/app_bar/app_bar_component.dart';
import 'package:j17_bank_mybank_mobile/view/components/button/j17_button.dart';
import 'package:j17_bank_mybank_mobile/view/components/loading/loading_modal.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/authorization_details_page.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/pix_automatic_qrcode_receipt_page.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_decodifica_qrcode_data.dart';
import 'package:j17_bank_mybank_mobile/model/client/pix/automatic/pix_automatic_client.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_automatic_send_qrcode_jornada3_data.dart';

/// **JORNADA 4**: Página de resumo específica para Jornada 4
/// Exibe os dados do pagamento e permite confirmar antes de efetuar o PIX
class PixAutomaticJornada4ResumePage extends StatefulWidget {
  final AuthorizationDetails details;
  final String idConta;
  final int codigoMunicipioIbgePagador;

  const PixAutomaticJornada4ResumePage({
    super.key,
    required this.details,
    required this.idConta,
    required this.codigoMunicipioIbgePagador,
  });

  @override
  State<PixAutomaticJornada4ResumePage> createState() =>
      _PixAutomaticJornada4ResumePageState();
}

class _PixAutomaticJornada4ResumePageState
    extends State<PixAutomaticJornada4ResumePage> {
  bool _isLoading = false;
  final PixAutomaticClient _client = PixAutomaticClient();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: J17ThemeColor.surfacesBackground.color(context),
      appBar: AppBarComponent(
        isLeadingVisible: true,
        onLeadingPressed: () => Navigator.pop(context),
        title: Text(
          'Resumo do pagamento',
          style: J17TextStyles.titleLarge().textStyle(context),
        ),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: const EdgeInsets.all(J17Padding.regular),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildValueSection(),
                const SizedBox(height: J17Padding.large),
                _buildReceiverSection(),
                const SizedBox(height: J17Padding.large),
                _buildPayerSection(),
                const SizedBox(height: J17Padding.large),
                _buildContractSection(),
                const SizedBox(height: J17Padding.large),
                _buildRecurrenceSection(),
                const SizedBox(height: J17Padding.large),
              ],
            ),
          ),
          if (_isLoading) const LoadingModal(),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildValueSection() {
    return Container(
      padding: const EdgeInsets.all(J17Padding.regular),
      decoration: BoxDecoration(
        color: J17ThemeColor.surfacesElevate.color(context),
        borderRadius: BorderRadius.circular(J17Padding.small),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Valor do pagamento',
            style: J17TextStyles.bodyLarge().textStyle(context),
          ),
          const SizedBox(height: J17Padding.small),
          Text(
            'R\$ ${widget.details.value.toStringAsFixed(2).replaceAll('.', ',')}',
            style: J17TextStyles.titleLarge(bold: true).textStyle(context),
          ),
        ],
      ),
    );
  }

  Widget _buildReceiverSection() {
    return Container(
      padding: const EdgeInsets.all(J17Padding.regular),
      decoration: BoxDecoration(
        color: J17ThemeColor.surfacesElevate.color(context),
        borderRadius: BorderRadius.circular(J17Padding.small),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recebedor',
            style: J17TextStyles.bodyLarge().textStyle(context),
          ),
          const SizedBox(height: J17Padding.small),
          Text(
            widget.details.receiverName,
            style: J17TextStyles.bodyMedium(bold: true).textStyle(context),
          ),
          Text(
            widget.details.receiverDocument,
            style: J17TextStyles.bodyMedium().textStyle(context),
          ),
        ],
      ),
    );
  }

  Widget _buildPayerSection() {
    return Container(
      padding: const EdgeInsets.all(J17Padding.regular),
      decoration: BoxDecoration(
        color: J17ThemeColor.surfacesElevate.color(context),
        borderRadius: BorderRadius.circular(J17Padding.small),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pagador',
            style: J17TextStyles.bodyLarge().textStyle(context),
          ),
          const SizedBox(height: J17Padding.small),
          Text(
            widget.details.payerName,
            style: J17TextStyles.bodyMedium(bold: true).textStyle(context),
          ),
          if (widget.details.payerDocument != null)
            Text(
              widget.details.payerDocument!,
              style: J17TextStyles.bodyMedium().textStyle(context),
            ),
        ],
      ),
    );
  }

  Widget _buildContractSection() {
    return Container(
      padding: const EdgeInsets.all(J17Padding.regular),
      decoration: BoxDecoration(
        color: J17ThemeColor.surfacesElevate.color(context),
        borderRadius: BorderRadius.circular(J17Padding.small),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Contrato',
            style: J17TextStyles.bodyLarge().textStyle(context),
          ),
          const SizedBox(height: J17Padding.small),
          Text(
            widget.details.paymentDescription ?? '',
            style: J17TextStyles.bodyMedium(bold: true).textStyle(context),
          ),
          if (widget.details.clientCode?.isNotEmpty == true)
            Text(
              'Número: ${widget.details.clientCode!}',
              style: J17TextStyles.bodyMedium().textStyle(context),
            ),
        ],
      ),
    );
  }

  Widget _buildRecurrenceSection() {
    return Container(
      padding: const EdgeInsets.all(J17Padding.regular),
      decoration: BoxDecoration(
        color: J17ThemeColor.surfacesElevate.color(context),
        borderRadius: BorderRadius.circular(J17Padding.small),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recorrência',
            style: J17TextStyles.bodyLarge().textStyle(context),
          ),
          const SizedBox(height: J17Padding.small),
          Text(
            'Frequência: ${widget.details.nextPayments}',
            style: J17TextStyles.bodyMedium().textStyle(context),
          ),
          Text(
            'Data de início: ${widget.details.paymentDate}',
            style: J17TextStyles.bodyMedium().textStyle(context),
          ),
          Text(
            'Tipo: ${widget.details.tipoPagamentoRecorrente}',
            style: J17TextStyles.bodyMedium().textStyle(context),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      color: J17ThemeColor.surfacesBackground.color(context),
      padding: const EdgeInsets.all(J17Padding.regular),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          J17Button(
            text: 'Pagar',
            onPressed: _isLoading ? null : _onPayPressed,
            variant: J17ButtonVariant.primary,
          ),
          const SizedBox(height: J17Padding.regular),
          J17Button(
            text: 'Cancelar',
            onPressed: _isLoading ? null : () => Navigator.pop(context),
            variant: J17ButtonVariant.outline,
          ),
          const SizedBox(height: J17Padding.larger),
        ],
      ),
    );
  }

  Future<void> _onPayPressed() async {
    setState(() {
      _isLoading = true;
    });

    try {
      print('[JORNADA_4_RESUME] Iniciando envio de PIX...');

      // Extrai dados do decode para montar a requisição
      final decode = widget.details.decodeResponse;
      if (decode == null || decode is! PixDecodificaQrcodeData) {
        throw Exception('Dados de decodificação inválidos');
      }

      final dto = decode.decodificacaoQrcodeDTO;
      if (dto == null) {
        throw Exception('DTO de decodificação não encontrado');
      }

      // Obtém o idFimAFim do decodificador
      final idFimAFim = dto.fimAFimId ?? '';
      if (idFimAFim.isEmpty) {
        throw Exception('ID Fim a Fim não encontrado no QR Code');
      }

      print('[JORNADA_4_RESUME] 🔍 idFimAFim: "$idFimAFim"');

      // Extrai dados do QR Code Dinâmico
      String? chavePix;
      int ispb = 0;
      String cnpj = '';
      String nomeRecebedor = '';
      String descricaoContrato = '';

      if (dto.dadosQrCodeDinamico != null) {
        final dinamico = dto.dadosQrCodeDinamico!;
        chavePix = dinamico.chave;
        ispb = dinamico.ispb is int
            ? dinamico.ispb as int
            : int.tryParse(dinamico.ispb.toString()) ?? 0;
        cnpj = dinamico.cpfCnpjRecebedor ?? '';
        nomeRecebedor = dinamico.nomeRecebedor ?? '';
        descricaoContrato = dinamico.solicitacaoPagador ?? '';
      }

      // Extrai dados do QR Code Composto
      String? idRecorrencia;
      String tipoFrequencia = 'MENSAL';
      String dataInicialRecorrencia = '';
      String dataFinalRecorrencia = '';
      String numeroContrato = '';

      if (dto.dadosQrCodeComposto != null) {
        final composto = dto.dadosQrCodeComposto!;
        idRecorrencia = composto.idRecorrencia;
        tipoFrequencia = composto.tpFrequencia ?? 'MENSAL';
        dataInicialRecorrencia = composto.dtInicialRecorrencia ?? '';
        dataFinalRecorrencia = composto.dtFinalRecorrencia ?? '';
        numeroContrato = composto.nrContrato ?? '';

        // Complementa dados se não foram definidos pelo Dinâmico
        if (ispb == 0 && composto.recebedor != null) {
          ispb = composto.recebedor!.ispb ?? 0;
          cnpj = composto.recebedor!.cnpj?.toString() ?? '';
          nomeRecebedor = composto.recebedor!.nome ?? '';
        }
      }

      // Monta a requisição para o endpoint de PIX
      final request = PixAutomaticSendQRCodeJornada3Request(
        descricao: descricaoContrato,
        valor: widget.details.tipoPagamentoRecorrente == 'FIXO'
            ? widget.details.originalValue
            : 0.0,
        idConta: widget.idConta,
        idDispositivo: 'DEVICE_${DateTime.now().millisecondsSinceEpoch}',
        syncId: 'SYNC_${DateTime.now().millisecondsSinceEpoch}',
        valorPix: widget.details.tipoPagamentoRecorrente == 'FIXO'
            ? widget.details.originalValue
            : 0.0,
        pisoValorMaximo: widget.details.tipoPagamentoRecorrente == 'VARIAVEL'
            ? widget.details.originalValue
            : 0.0,
        valorMaximoPagador: 0.0,
        descricaoPix: descricaoContrato,
        codigoMunicipioIbgePagador: widget.codigoMunicipioIbgePagador,
        recebedor: PagadorRecebedorPixDTO(
          ispb: ispb,
          tipoPessoa: cnpj.length == 14
              ? TipoPessoaPagador.PESSOA_JURIDICA
              : TipoPessoaPagador.PESSOA_FISICA,
          cpfCnpj: cnpj,
          nome: nomeRecebedor,
          agencia: '0001',
          tipoConta: TipoContaPagador.CONTA_CORRENTE,
          numeroConta: '000000',
        ),
        chavePix: chavePix ?? '',
        idRecorrencia: idRecorrencia ?? '',
        tipoFrequencia: TipoPeriodicidadeEnum.values.firstWhere(
          (e) => e.name.toUpperCase() == tipoFrequencia.toUpperCase(),
          orElse: () => TipoPeriodicidadeEnum.MENSAL,
        ),
        dataInicialRecorrencia: dataInicialRecorrencia,
        dataFinalRecorrencia: dataFinalRecorrencia,
        numeroContrato: numeroContrato,
        descricaoContrato: descricaoContrato,
        dataHoraCriacaoRecorrencia: DateTime.now().toIso8601String(),
        dataHoraAutorizacaoRecorrencia: DateTime.now().toIso8601String(),
        dataHoraConfirmacaoLiquidacao: null,
        idFimAFim: idFimAFim,
      );

      print('[JORNADA_4_RESUME] Enviando requisição...');
      final pixResponse = await _client.enviarPixQRCodeJornada4(request);

      print('[JORNADA_4_RESUME] ✅ PIX enviado com sucesso!');
      print('[JORNADA_4_RESUME] Response: ${pixResponse.toJson()}');

      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // ✅ CORREÇÃO: Navega para o comprovante do PIX (ReceiptPage normal)
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PixAutomaticQRCodeReceiptPage(
            decodeResponse: widget.details,
            authResponse: pixResponse.toJson(),
            showBottomSheetOnStart: true, // Abre o BottomSheet automaticamente
            jornada: 'JORNADA_4',
            idConta: widget.idConta,
            codigoMunicipioIbgePagador: widget.codigoMunicipioIbgePagador,
            isPixReceipt: true, // Primeiro comprovante (PIX realizado)
          ),
        ),
      );
    } catch (e) {
      print('[JORNADA_4_RESUME] ❌ Erro no envio do PIX: $e');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao processar pagamento: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
