import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/auto_pix_data.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/auto_pix_schedule.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/auto_pix_schedule_cancel.dart';
import 'package:j17_bank_mybank_mobile/utils/capture_theme.dart';
import 'package:j17_bank_mybank_mobile/utils/string_util.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';
import 'package:j17_bank_mybank_mobile/view/components/button/j17_button.dart';
import 'package:j17_bank_mybank_mobile/view/components/card/j17_menu_card_button.dart';
import 'package:j17_bank_mybank_mobile/view/components/dynamic_tile/help_dynamic_tile.dart';
import 'package:j17_bank_mybank_mobile/view/components/loading/loading_modal.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pix/automatic/components/auto_pix_cancel_modal.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pix/automatic/components/auto_pix_receipt_card_dynamic_component.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/components/receipt_header.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/pix_automatic_menu_page.dart';
import 'package:j17_bank_mybank_mobile/viewmodel/account/account_viewmodel.dart';
import 'package:j17_bank_mybank_mobile/viewmodel/pix/automatic/auto_pix_cancel_viewmodel.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';

// ignore: must_be_immutable
class AutoPixScheduleReceipt extends StatefulWidget {
  ConteudoDTO conteudo;
  AutoPixScheduleReceipt({super.key, required this.conteudo});

  @override
  State<AutoPixScheduleReceipt> createState() => _AutoPixScheduleReceiptState();
}

class _AutoPixScheduleReceiptState extends State<AutoPixScheduleReceipt> {
  final _controller = ScreenshotController();
  late CaptureThemeChanger _themeChanger;
  bool _cancelou = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _themeChanger = Provider.of<CaptureThemeChanger>(context);
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const SvgIcon(icon: J17Icons.arrowLeftCustom),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        title: Text(
          "Comprovante",
          style: J17TextStyles.titleLarge().textStyle(context),
        ),
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        child: Screenshot(
          controller: _controller,
          child: Padding(
            padding: const EdgeInsets.all(J17Padding.regular),
            child: Column(
              children: [
                const SizedBox(
                  height: J17Padding.large,
                ),
                ReceiptHeader(
                  title: _cancelou ? 'Agendamento cancelado!' : null,
                  valor: widget.conteudo.objetoPagamento?.valorPagamento
                      .toString()
                      .formatToBrlCurrency,
                  isSchedule: true,
                  icon: _cancelou
                      ? J17Icons.calendarRemove
                      : J17Icons.calendarTick,
                ),
                AutoPixReceiptCardDynamicComponent.create(titleValue: {
                  "Data de agendamento": AutoPixReceiptCardDynamicComponentItem(
                      item: widget.conteudo.dataAgendamento, showHour: true),
                  "Data de Pagamento": AutoPixReceiptCardDynamicComponentItem(
                      item: widget
                          .conteudo.objetoPagamento?.dataPrevistaPagamento,
                      showInFront: true),
                  "ID da transação": AutoPixReceiptCardDynamicComponentItem(
                      item: widget.conteudo.endToEndIdentificador),
                  "Descrição": AutoPixReceiptCardDynamicComponentItem(
                      item: widget.conteudo.objetoPagamento?.descricao),
                }, header: "Transação", context: context),
                SizedBox(
                  height: J17Padding.regLarge,
                ),
                AutoPixReceiptCardDynamicComponent.create(titleValue: {
                  "": AutoPixReceiptCardDynamicComponentItem(
                      item: widget.conteudo.nomeRecebedor),
                  "Banco": AutoPixReceiptCardDynamicComponentItem(
                      item: widget.conteudo.recebedorInstituicaoFinanceira),
                  "Agência;Conta": AutoPixReceiptCardDynamicComponentItem(
                      item:
                          "${widget.conteudo.recebedorAgencia};${widget.conteudo.recebedorConta}"),
                }, header: "Quem recebeu", context: context),
                SizedBox(
                  height: J17Padding.regLarge,
                ),
                AutoPixReceiptCardDynamicComponent.create(titleValue: {
                  "": AutoPixReceiptCardDynamicComponentItem(
                      item: widget.conteudo.pagador?.nome),
                  "Banco": AutoPixReceiptCardDynamicComponentItem(
                      item:
                          widget.conteudo.pagador?.nomeInstituicaoFinanaceira),
                  "Agência;Conta": AutoPixReceiptCardDynamicComponentItem(
                      item:
                          "${widget.conteudo.pagador?.numeroAgencia};${widget.conteudo.pagador?.numeroConta}"),
                  "CPF": AutoPixReceiptCardDynamicComponentItem(
                      item:
                          widget.conteudo.pagador?.cpfCnpj?.formatedAsCpfMask),
                  "Tipo de conta": AutoPixReceiptCardDynamicComponentItem(
                      item: widget.conteudo.pagador?.tipoConta),
                }, header: "Quem enviou", context: context),
                SizedBox(
                  height: J17Padding.regLarge,
                ),
                if (!_cancelou)
                  ListTile(
                    minVerticalPadding: J17Padding.regular,
                    tileColor: J17ThemeColor.surfacesElevate.color(context),
                    shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(J17BorderRadius.small)),
                    title: Text(
                      'O agendamento pode ser cancelado até as 23:59 do dia anterior à data agendada, e sua efetivação dependerá de disponibilidade de saldo e limite diário do Pix Agendado na data do pagamento.',
                      style: J17TextStyles.bodyMedium().textStyle(context),
                    ),
                  ),
                SizedBox(
                  height: J17Padding.regLarge,
                ),
                /*HelpDynamicTile(
                  margin: 0,
                ),
                SizedBox(
                  height: J17Padding.regLarge,
                ),*/
                if (!_cancelou)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      J17MenuCardButton(
                        onPressed: () => _shareReceipt(context),
                        label: 'Compartilhar comprovante',
                        icon: J17Icons.exportIcon,
                      ),
                      SizedBox(
                        width: J17Padding.large,
                      ),
                      J17MenuCardButton(
                        onPressed: () async {
                          bool resposta =
                              await AutoPixCancelModal.cancelamentoAction(
                            context: context,
                          );
                          if (resposta) {
                            var result = await loadingAround(
                                context,
                                AutoPixCancelViewmodel().scheduleCancel(
                                    AutoPixScheduleCancel(
                                        idConta: context
                                            .read<AccountViewModel>()
                                            .account
                                            ?.idAccount,
                                        ispbDestinatario:
                                            widget.conteudo.recebedorIspb,
                                        identificadorConciliacaoRecebedor:
                                            widget.conteudo
                                                .idConciliacaoRecebedor,
                                        motivoCancelamento: "",
                                        endToEndIdentificador: widget
                                            .conteudo.endToEndIdentificador)),
                                shouldShowErrorMessage: true);
                            if (result.situacaoCancelamento == "ACEITO") {
                              setState(() {
                                _cancelou = true;
                                _scrollToTop();
                              });
                            }
                          }
                        },
                        label: 'Cancelar agendamento',
                        icon: J17Icons.closeCircle,
                        iconColor: J17ThemeColor.textTextDelete,
                        textColor: J17ThemeColor.textTextDelete,
                      ),
                    ],
                  ),
                if (_cancelou)
                  J17Button(
                    text: 'Compartilhar comprovante',
                    onPressed: () => _shareReceipt(context),
                    variant: J17ButtonVariant.primary,
                    trailingIcon: J17Icons.exportIcon,
                    trailingIconSize: J17IconSizes.small,
                  ),
                const SizedBox(height: J17Padding.regular),
                if (_cancelou)
                  J17Button(
                    text: 'Voltar ao menu',
                    onPressed: () =>
                        Navigator.of(context).pushReplacement(MaterialPageRoute(
                      builder: (context) => PixAutomaticMenuPage(),
                    )),
                    variant: J17ButtonVariant.outline,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _scrollToTop() {
    _scrollController.animateTo(0,
        duration: const Duration(milliseconds: 100), curve: Curves.easeInOut);
  }

  Future<void> _shareReceipt(BuildContext context) async {
    await _themeChanger.withLightThemeForSharing(() async {
      // Aguarda um pouco para garantir que o layout foi renderizado
      await Future.delayed(const Duration(milliseconds: 500));

      Uint8List? image = await loadingAround(
        context,
        _controller.capture(),
        shouldShowErrorMessage: false,
      );

      if (image != null) {
        try {
          var params = ShareParams(
            files: [XFile.fromData(image, mimeType: 'image/png')],
            fileNameOverrides: ['comprovante_agendamento_cancelado.png'],
          );
          ShareResult result = await SharePlus.instance.share(params);
          print("Resultado do share: ${result.status.name}");
        } catch (e) {
          print("Erro ao compartilhar: $e");
        }
      }
    });
  }
}
