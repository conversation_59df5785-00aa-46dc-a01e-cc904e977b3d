import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pix/automatic/auto_pix_max_value.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pix/automatic/auto_pix_config_notif_and_limit_use.dart';

class AutoPixAuthorizationSettingsPage extends StatelessWidget {
  const AutoPixAuthorizationSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: J17ThemeColor.surfacesBackground.color(context),
      appBar: AppBar(
        backgroundColor: J17ThemeColor.surfacesBackground.color(context),
        title: Text(
          "Configurações das autorizações",
          maxLines: J17OtherSizes.regularMaxLines,
          style: J17TextStyles.titleLarge().textStyle(context),
        ),
        // actions: [
        //   IconButton(
        //     onPressed: () {
        //       // TODO: Implementar ajuda
        //     },
        //     icon: SvgIcon(
        //       icon: J17Icons.messageQuestion,
        //     ),
        //   ),
        // ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(J17Padding.regular),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Edite os parâmetros de todas as suas autorizações em um único lugar.",
              style: J17TextStyles.bodyMedium().textStyle(context),
            ),
            const SizedBox(height: J17Padding.large),
            Expanded(
              child: ListView(
                children: [
                  _buildSettingsItem(
                    context,
                    title: "Valor máximo",
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AutoPixMaxValue(),
                        ),
                      );
                    },
                  ),
                  _buildDivider(context),
                  // _buildSettingsItem(
                  //   context,
                  //   title: "Uso de limite de crédito",
                  //   onTap: () {
                  //     Navigator.push(
                  //       context,
                  //       MaterialPageRoute(
                  //         builder: (context) =>
                  //             const AutoPixConfigNotifAndLimits(
                  //           settingsNotification: false,
                  //         ),
                  //       ),
                  //     );
                  //   },
                  // ),
                  // _buildDivider(context),
                  _buildSettingsItem(
                    context,
                    title: "Notificações de agendamento",
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              const AutoPixConfigNotifAndLimits(
                            settingsNotification: true,
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsItem(
    BuildContext context, {
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.only(
          top: J17Padding.regular,
          bottom: J17Padding.regSmall,
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: J17TextStyles.labelLarge().textStyle(context),
              ),
            ),
            SvgIcon(
              icon: J17Icons.arrowRight1,
              size: J17IconSizes.regSmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Divider(
      color: J17ThemeColor.dividerColor.color(context),
      height: 1,
    );
  }
}
