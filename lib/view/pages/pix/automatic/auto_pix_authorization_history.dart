import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';
import 'package:j17_bank_mybank_mobile/view/components/dynamic_tile/dynamic_tile.dart';
import 'package:j17_bank_mybank_mobile/view/components/loading/loading_modal.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pix/automatic/components/auto_pix_history_filter_enum.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pix/automatic/components/pix_automatic_authorization_item.dart';
import 'package:j17_bank_mybank_mobile/viewmodel/pix/automatic/auto_pix_authorization_history_viewmodel.dart';
import 'package:provider/provider.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pixAutomatic/pix_automatic_history_detail_page.dart';
import 'package:intl/intl.dart';
import 'package:j17_bank_mybank_mobile/view/pages/account/components/custom_filter_chip.dart';
import 'package:j17_bank_mybank_mobile/view/pages/home/<USER>/bottom_page_error_custom.dart';

/// Wrapper que fornece o Provider para a tela de histórico
class AutoPixAuthorizationHistoryPage extends StatelessWidget {
  const AutoPixAuthorizationHistoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) {
        final viewModel = AutoPixAuthorizationHistoryViewModel();
        viewModel.loadAllAuthorizations();
        return viewModel;
      },
      child: const AutoPixAuthorizationHistory(),
    );
  }
}

class AutoPixAuthorizationHistory extends StatefulWidget {
  const AutoPixAuthorizationHistory({super.key});

  @override
  State<AutoPixAuthorizationHistory> createState() =>
      _AutoPixAuthorizationHistoryState();
}

class _AutoPixAuthorizationHistoryState
    extends State<AutoPixAuthorizationHistory> {
  final ScrollController _scrollController = ScrollController();
  bool _showFab = false;

  // Filtros locais do State
  AutoPixHistoryPeriod? _periodoSelecionado;
  final Set<String> _statusSelecionado = {};
  DateTime? _dataInicioSelecionada;
  DateTime? _dataFimSelecionada;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.offset > 300 && !_showFab) {
      setState(() {
        _showFab = true;
      });
    } else if (_scrollController.offset <= 300 && _showFab) {
      setState(() {
        _showFab = false;
      });
    }
  }

  /// Calcula o número de filtros ativos
  int get _activeFilterCount {
    int count = 0;
    if (_statusSelecionado.isNotEmpty) count += _statusSelecionado.length;
    if (_periodoSelecionado != null) count++;
    if (_dataInicioSelecionada != null || _dataFimSelecionada != null) count++;
    return count;
  }

  /// Aplica todos os filtros selecionados
  void _aplicarFiltros() {
    final viewModel = context.read<AutoPixAuthorizationHistoryViewModel>();

    // Se há filtro de data, recarrega da API
    if (_dataInicioSelecionada != null || _dataFimSelecionada != null) {
      viewModel
          .fetchWithFilter(
        dataInicio: _dataInicioSelecionada,
        dataFim: _dataFimSelecionada,
      )
          .then((_) {
        // Após carregar dados filtrados, aplica filtros locais
        _aplicarFiltrosLocais(viewModel);
      });
    } else {
      // Se não há filtro de data, recarrega dados originais
      viewModel.refresh().then((_) {
        // Após recarregar, aplica filtros locais
        _aplicarFiltrosLocais(viewModel);
      });
    }
  }

  /// Aplica filtros locais (status e ordenação)
  void _aplicarFiltrosLocais(AutoPixAuthorizationHistoryViewModel viewModel) {
    // Aplica filtro de status se houver
    if (_statusSelecionado.isNotEmpty) {
      viewModel.filterByStatus(_statusSelecionado);
    }

    // Aplica ordenação por período
    if (_periodoSelecionado == AutoPixHistoryPeriod.maisRecentes) {
      viewModel.sortByDate(descending: true);
    } else if (_periodoSelecionado == AutoPixHistoryPeriod.maisAntigos) {
      viewModel.sortByDate(descending: false);
    }
  }

  /// Remove todos os filtros
  void _removerFiltros() {
    setState(() {
      _periodoSelecionado = null;
      _statusSelecionado.clear();
      _dataInicioSelecionada = null;
      _dataFimSelecionada = null;
    });

    // Recarrega dados sem filtros
    final viewModel = context.read<AutoPixAuthorizationHistoryViewModel>();
    viewModel.refresh();
  }

  /// Abre o modal de filtros
  void _abrirModalFiltros() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => _FiltroBottomSheet(
        periodoSelecionado: _periodoSelecionado,
        statusSelecionado: Set<String>.from(_statusSelecionado),
        dataInicioSelecionada: _dataInicioSelecionada,
        dataFimSelecionada: _dataFimSelecionada,
        onLimpar: () {
          _removerFiltros();
          Navigator.pop(context);
        },
        onFiltrar: () {
          _aplicarFiltros();
          Navigator.pop(context);
        },
        onUpdate: (periodo, status, dataInicio, dataFim) {
          setState(() {
            _periodoSelecionado = periodo;
            _statusSelecionado
              ..clear()
              ..addAll(status);
            _dataInicioSelecionada = dataInicio;
            _dataFimSelecionada = dataFim;
          });
        },
      ),
    );
  }

  /// Scroll para o topo
  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Histórico de autorizações",
          maxLines: J17OtherSizes.regularMaxLines,
          style: J17TextStyles.titleLarge().textStyle(context),
        ),
      ),
      floatingActionButton: AnimatedOpacity(
        opacity: _showFab ? 1 : 0,
        duration: const Duration(milliseconds: 300),
        child: FloatingActionButton(
          mini: true,
          onPressed: _scrollToTop,
          child: const SvgIcon(
            icon: J17Icons.arrowUp,
            size: J17IconSizes.regSmall,
            color: J17ThemeColor.textAuxiliary,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(J17Padding.regular),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "Aqui você encontra o registro completo de todas as autorizações da sua conta. Acompanhe as autorizações ativas, canceladas, expiradas e pendentes.",
              style: J17TextStyles.labelMedium().textStyle(context),
            ),
            const SizedBox(height: J17Padding.regular),
            Row(
              children: [
                CustomFilterChip(
                  type: CustomFilterChipType.filterCount,
                  filterCount: _activeFilterCount,
                  value: 'openFilters',
                  selectedValue: true,
                  onSelected: (value) {
                    _abrirModalFiltros();
                  },
                ),
                const SizedBox(width: J17Padding.regSmall),
                if (_activeFilterCount > 0)
                  TextButton(
                    onPressed: _removerFiltros,
                    child: Text(
                      'Remover filtros',
                      style: J17TextStyles.bodyLarge().textStyle(context),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: J17Padding.regular),
            Expanded(
              child: Consumer<AutoPixAuthorizationHistoryViewModel>(
                builder: (context, viewModel, _) {
                  if (viewModel.isLoading) {
                    return Center(
                      child: LoadingModal(),
                    );
                  }

                  if (viewModel.groupedAuthorizations.isEmpty) {
                    return Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Center(
                          child: DynamicTile(
                            margin: EdgeInsets.all(0),
                            title: 'Nenhum registro encontrado no histórico.',
                            leadingIcon: J17Icons.infoCircle,
                          ),
                        ),
                      ],
                    );
                  }

                  if (viewModel.hasError) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      BottomPageErrorCustom.abrir(
                        context: context,
                        sheetDescriptionText:
                            'Não conseguimos aplicar os filtros no momento. Por favor, tente novamente.',
                        whiteButton: true,
                        onPressed: () {
                          Navigator.of(context).pop();
                          viewModel.refresh();
                        },
                        buttonText: 'Tentar novamente',
                      );
                    });
                    return const SizedBox.shrink();
                  }

                  if (!viewModel.hasData) {
                    return Center(
                      child: Text(
                        'Nenhuma autorização encontrada.',
                        style: J17TextStyles.labelMedium().textStyle(context),
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () async {
                      await viewModel.refresh();
                    },
                    child: ListView.separated(
                      controller: _scrollController,
                      itemCount: viewModel.sortedKeys.length,
                      separatorBuilder: (context, index) =>
                          const SizedBox(height: J17Padding.regSmall),
                      itemBuilder: (context, index) {
                        final dateKey = viewModel.sortedKeys[index];
                        final transactionsForDate =
                            viewModel.groupedAuthorizations[dateKey]!;
                        final parsedDate = DateTime.parse(dateKey).toLocal();
                        final formattedDate =
                            "${parsedDate.day.toString().padLeft(2, '0')}/${parsedDate.month.toString().padLeft(2, '0')}/${parsedDate.year}";

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(
                                top: J17Padding.regular,
                              ),
                              child: Text(
                                formattedDate,
                                style: J17TextStyles.bodyMedium(
                                  bold: true,
                                ).textStyle(context),
                              ),
                            ),
                            Divider(
                              thickness: J17OtherSizes.dividerThickness,
                              color: J17ThemeColor.dividerColor.color(context),
                            ),
                            ...transactionsForDate.map((authorization) {
                              return PixAutomaticAuthorizationItem(
                                authorization: authorization,
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (_) =>
                                          PixAutomaticHistoryDetailPage(
                                        authorization: authorization,
                                      ),
                                    ),
                                  );
                                },
                              );
                            }).toList(),
                          ],
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget para o modal de filtros
class _FiltroBottomSheet extends StatefulWidget {
  final AutoPixHistoryPeriod? periodoSelecionado;
  final Set<String> statusSelecionado;
  final DateTime? dataInicioSelecionada;
  final DateTime? dataFimSelecionada;
  final VoidCallback onLimpar;
  final VoidCallback onFiltrar;
  final void Function(AutoPixHistoryPeriod?, Set<String>, DateTime?, DateTime?)
      onUpdate;

  const _FiltroBottomSheet({
    required this.periodoSelecionado,
    required this.statusSelecionado,
    required this.dataInicioSelecionada,
    required this.dataFimSelecionada,
    required this.onLimpar,
    required this.onFiltrar,
    required this.onUpdate,
  });

  @override
  State<_FiltroBottomSheet> createState() => _FiltroBottomSheetState();
}

class _FiltroBottomSheetState extends State<_FiltroBottomSheet> {
  late AutoPixHistoryPeriod? _periodoSelecionado;
  late Set<String> _statusSelecionado;
  late DateTime? _dataInicioSelecionada;
  late DateTime? _dataFimSelecionada;

  @override
  void initState() {
    super.initState();
    _periodoSelecionado = widget.periodoSelecionado;
    _statusSelecionado = Set<String>.from(widget.statusSelecionado);
    _dataInicioSelecionada = widget.dataInicioSelecionada;
    _dataFimSelecionada = widget.dataFimSelecionada;
  }

  /// Atualiza o estado do widget pai
  void _updateParent() {
    widget.onUpdate(_periodoSelecionado, _statusSelecionado,
        _dataInicioSelecionada, _dataFimSelecionada);
  }

  /// Limpa todos os filtros
  void _limparFiltros() {
    setState(() {
      _periodoSelecionado = null;
      _statusSelecionado.clear();
      _dataInicioSelecionada = null;
      _dataFimSelecionada = null;
    });
    _updateParent();
  }

  /// Limpa filtros de período quando status é selecionado
  void _limparFiltrosPeriodo() {
    _periodoSelecionado = null;
    _dataInicioSelecionada = null;
    _dataFimSelecionada = null;
  }

  /// Limpa filtros de status quando período é selecionado
  void _limparFiltrosStatus() {
    _statusSelecionado.clear();
  }

  /// Gerencia seleção de status
  void _onStatusChanged(String status, bool value) {
    setState(() {
      if (value) {
        _statusSelecionado.add(status);
      } else {
        _statusSelecionado.remove(status);
      }
      // Ao selecionar status, NÃO limpa ordenação!
      // Só limpa período se estava em 'Escolher período'
      if (_periodoSelecionado == AutoPixHistoryPeriod.escolherPeriodo) {
        _periodoSelecionado = null;
        _dataInicioSelecionada = null;
        _dataFimSelecionada = null;
      }
    });
    _updateParent();
  }

  /// Gerencia seleção de período
  void _onPeriodoChanged(AutoPixHistoryPeriod? periodo) {
    setState(() {
      _periodoSelecionado = periodo;

      if (periodo == AutoPixHistoryPeriod.escolherPeriodo) {
        // Limpa filtros de status e datas quando escolher período é selecionado
        _limparFiltrosStatus();
        _dataInicioSelecionada = null;
        _dataFimSelecionada = null;
      }
      // Ao selecionar Mais recentes/Mais antigos, NÃO limpa status!
    });
    _updateParent();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: J17Padding.regular,
        right: J17Padding.regular,
        bottom: MediaQuery.of(context).viewInsets.bottom + J17Padding.regular,
        top: J17Padding.large,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Filtros',
                style: J17TextStyles.headlineSmall().textStyle(context),
              ),
              TextButton(
                onPressed: _limparFiltros,
                child: Text(
                  'Limpar',
                  style: J17TextStyles.bodyLarge().textStyle(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: J17Padding.regular),

          // Filtros de Status
          Text(
            'Status',
            style: J17TextStyles.bodyLarge(
              bold: true,
            ).textStyle(context),
          ),
          const SizedBox(height: J17Padding.small),
          Wrap(
            spacing: J17Padding.small,
            runSpacing: J17Padding.small,
            children: [
              CustomFilterChip(
                label: 'Ativo',
                value: 'ativo',
                selectedValue: _statusSelecionado,
                onSelected: (value) => _onStatusChanged('ativo', value),
              ),
              CustomFilterChip(
                label: 'Pendente',
                value: 'pendente',
                selectedValue: _statusSelecionado,
                onSelected: (value) => _onStatusChanged('pendente', value),
              ),
              CustomFilterChip(
                label: 'Expirado',
                value: 'expirado',
                selectedValue: _statusSelecionado,
                onSelected: (value) => _onStatusChanged('expirado', value),
              ),
              CustomFilterChip(
                label: 'Cancelado',
                value: 'cancelado',
                selectedValue: _statusSelecionado,
                onSelected: (value) => _onStatusChanged('cancelado', value),
              ),
              CustomFilterChip(
                label: 'Processando',
                value: 'processando',
                selectedValue: _statusSelecionado,
                onSelected: (value) => _onStatusChanged('processando', value),
              ),
              CustomFilterChip(
                label: 'Rejeitado',
                value: 'rejeitado',
                selectedValue: _statusSelecionado,
                onSelected: (value) => _onStatusChanged('rejeitado', value),
              ),
            ],
          ),
          const SizedBox(height: J17Padding.regular),

          // Filtros de Período
          Text('Período',
              style: J17TextStyles.bodyLarge(bold: true).textStyle(context)),
          const SizedBox(height: J17Padding.small),
          Wrap(
            spacing: J17Padding.small,
            runSpacing: J17Padding.small,
            children: [
              CustomFilterChip(
                label: 'Mais recentes',
                value: AutoPixHistoryPeriod.maisRecentes,
                selectedValue: _periodoSelecionado,
                onSelected: (value) {
                  if (value) {
                    _onPeriodoChanged(AutoPixHistoryPeriod.maisRecentes);
                  } else {
                    _onPeriodoChanged(null);
                  }
                },
              ),
              CustomFilterChip(
                label: 'Mais antigos',
                value: AutoPixHistoryPeriod.maisAntigos,
                selectedValue: _periodoSelecionado,
                onSelected: (value) {
                  if (value) {
                    _onPeriodoChanged(AutoPixHistoryPeriod.maisAntigos);
                  } else {
                    _onPeriodoChanged(null);
                  }
                },
              ),
              CustomFilterChip(
                label: 'Escolher período',
                value: AutoPixHistoryPeriod.escolherPeriodo,
                selectedValue: _periodoSelecionado,
                onSelected: (value) {
                  if (value) {
                    _onPeriodoChanged(AutoPixHistoryPeriod.escolherPeriodo);
                  } else {
                    _onPeriodoChanged(null);
                  }
                },
              ),
            ],
          ),

          // Campos de data personalizada
          if (_periodoSelecionado == AutoPixHistoryPeriod.escolherPeriodo) ...[
            const SizedBox(height: J17Padding.regular),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Data inicial',
                        style: J17TextStyles.bodyMedium().textStyle(context),
                      ),
                      const SizedBox(height: J17Padding.small),
                      TextFormField(
                        readOnly: true,
                        controller: TextEditingController(
                          text: _dataInicioSelecionada != null
                              ? DateFormat('dd/MM/yyyy')
                                  .format(_dataInicioSelecionada!)
                              : '',
                        ),
                        decoration: InputDecoration(
                          suffixIcon: IconButton(
                            icon: const SvgIcon(icon: J17Icons.calendar),
                            onPressed: () async {
                              final DateTime? pickedDate = await showDatePicker(
                                context: context,
                                initialDate:
                                    _dataInicioSelecionada ?? DateTime.now(),
                                firstDate: DateTime(2000),
                                lastDate: DateTime.now(),
                              );
                              if (pickedDate != null) {
                                setState(() {
                                  _dataInicioSelecionada = pickedDate;
                                });
                                _updateParent();
                              }
                            },
                          ),
                          border: const OutlineInputBorder(),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: J17Padding.regular),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Data final',
                        style: J17TextStyles.bodyMedium().textStyle(context),
                      ),
                      const SizedBox(height: J17Padding.small),
                      TextFormField(
                        readOnly: true,
                        controller: TextEditingController(
                          text: _dataFimSelecionada != null
                              ? DateFormat('dd/MM/yyyy')
                                  .format(_dataFimSelecionada!)
                              : '',
                        ),
                        decoration: InputDecoration(
                          suffixIcon: IconButton(
                            icon: const SvgIcon(icon: J17Icons.calendar),
                            onPressed: () async {
                              final DateTime? pickedDate = await showDatePicker(
                                context: context,
                                initialDate:
                                    _dataFimSelecionada ?? DateTime.now(),
                                firstDate: DateTime(2000),
                                lastDate: DateTime.now(),
                              );
                              if (pickedDate != null) {
                                setState(() {
                                  _dataFimSelecionada = pickedDate;
                                });
                                _updateParent();
                              }
                            },
                          ),
                          border: const OutlineInputBorder(),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: J17Padding.small),
            Text(
              'Use os campos de data para definir o período exato que deseja ver.',
              style: J17TextStyles.bodySmall().textStyle(context),
            )
          ],
          const SizedBox(height: J17Padding.large),

          // Botão Filtrar
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                _updateParent();
                widget.onFiltrar();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: J17ThemeColor.actionAuxiliary.color(context),
              ),
              child: Text(
                'Filtrar',
                style: J17TextStyles.bodyLarge(
                  color: J17ThemeColor.textActionSecondaryColor,
                ).textStyle(context),
              ),
            ),
          ),
          const SizedBox(height: J17Padding.large),
        ],
      ),
    );
  }
}
