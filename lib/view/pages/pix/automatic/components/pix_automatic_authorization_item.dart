import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_automatic_authorization_data.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';

class PixAutomaticAuthorizationItem extends StatelessWidget {
  final PixAutomaticAuthorization authorization;
  final VoidCallback? onTap;

  const PixAutomaticAuthorizationItem({
    super.key,
    required this.authorization,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final statusInfo =
        getStatusInfo(authorization.situacaoPagamentoRecorrente, context);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: J17Padding.small),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          vertical: J17Padding.small,
          horizontal: J17Padding.regular,
        ),
        title: Text(
          authorization.recebedor.nome,
          style: J17TextStyles.labelLarge(
            bold: true,
          ).textStyle(context),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: J17Padding.small),
            Text(
              'R\$ ${authorization.valor.toStringAsFixed(2)}',
              style: J17TextStyles.labelLarge().textStyle(context),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: J17Padding.smallest,
                vertical: J17Padding.smallest,
              ),
              decoration: BoxDecoration(
                color: statusInfo['color'],
                borderRadius: BorderRadius.circular(J17BorderRadius.smaller),
              ),
              child: Text(
                statusInfo['label'],
                style: J17TextStyles.labelSmall().textStyle(context).copyWith(
                      color: statusInfo['textColor'],
                    ),
              ),
            ),
            const SizedBox(width: J17Padding.small),
            SvgIcon(
              size: J17IconSizes.regSmall,
              icon: J17Icons.arrowRight1,
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }

  Map<String, dynamic> getStatusInfo(
      SituacaoPagamentoRecorrente status, BuildContext context) {
    switch (status) {
      case SituacaoPagamentoRecorrente.AUTORIZADO:
      case SituacaoPagamentoRecorrente.ACEITA:
      case SituacaoPagamentoRecorrente.ACEITA_USUARIO_PAGADOR:
        return {
          'label': 'Ativo',
          'color': J17ThemeColor.flagActiveBG.color(context),
          'textColor': J17ThemeColor.textFlagsActive.color(context)
        };
      case SituacaoPagamentoRecorrente.PENDENTE_USUARIO_PAGADOR:
        return {
          'label': 'Pendente',
          'color': J17ThemeColor.flagWaitingBG.color(context),
          'textColor': J17ThemeColor.flagWaitingText.color(context)
        };
      case SituacaoPagamentoRecorrente.EM_PROCESSAMENTO:
        return {
          'label': 'Processando',
          'color': J17ThemeColor.flagProcessingBG.color(context),
          'textColor': J17ThemeColor.flagProcessingText.color(context)
        };
      case SituacaoPagamentoRecorrente.REJEITADO:
        return {
          'label': 'Rejeitado',
          'color': J17ThemeColor.flagCanceledBG.color(context),
          'textColor': J17ThemeColor.textFlagsCancelled.color(context)
        };
      case SituacaoPagamentoRecorrente.CANCELADO:
        return {
          'label': 'Cancelado',
          'color': J17ThemeColor.flagCanceledBG.color(context),
          'textColor': J17ThemeColor.textFlagsCancelled.color(context)
        };
      case SituacaoPagamentoRecorrente.EXPIRADO:
        return {
          'label': 'Expirado',
          'color': J17ThemeColor.flagExpiredBG.color(context),
          'textColor': J17ThemeColor.flagExpiredText.color(context)
        };
      case SituacaoPagamentoRecorrente.CANCELADA:
        return {
          'label': 'Cancelada',
          'color': J17ThemeColor.flagCanceledBG.color(context),
          'textColor': J17ThemeColor.textFlagsCancelled.color(context)
        };
      case SituacaoPagamentoRecorrente.ACEITA_REJEICAO_PSP_RECEBEDOR:
      case SituacaoPagamentoRecorrente.ACEITA_REJEITADA_PSP_RECEBEDOR:
        return {
          'label': 'Rejeitada',
          'color': J17ThemeColor.flagRejectedBG.color(context),
          'textColor': J17ThemeColor.flagRejectedText.color(context)
        };
      case SituacaoPagamentoRecorrente.AGUARDANDO_REGISTRO_VALIDACAO:
        return {
          'label': 'Aguardando validação',
          'color': J17ThemeColor.flagProcessingBG.color(context),
          'textColor': J17ThemeColor.flagProcessingText.color(context)
        };
      case SituacaoPagamentoRecorrente.RECEBIDA_AGUARDANDO_VALIDACAO:
        return {
          'label': 'Aguardando validação',
          'color': J17ThemeColor.flagWaitingBG.color(context),
          'textColor': J17ThemeColor.flagWaitingText.color(context)
        };
      case SituacaoPagamentoRecorrente.REJEICAO_USUARIO_PAGADOR:
        return {
          'label': 'Rejeitado',
          'color': J17ThemeColor.flagRejectedBG.color(context),
          'textColor': J17ThemeColor.flagRejectedText.color(context)
        };
      case SituacaoPagamentoRecorrente.REJEITADA:
        return {
          'label': 'Rejeitada',
          'color': J17ThemeColor.flagRejectedBG.color(context),
          'textColor': J17ThemeColor.flagRejectedText.color(context)
        };
      // default:
      //   // Para status não mapeados, usa a descrição do modelo
      //   return {
      //     'label': authorization.statusDescription,
      //     'color': J17ThemeColor.flagExpiredBG.color(context),
      //     'textColor': J17ThemeColor.flagExpiredText.color(context)
      //   };
    }
  }

  String _getFrequencyText(TipoFrequencia frequency) {
    switch (frequency) {
      case TipoFrequencia.SEMANAL:
        return 'Semanal';
      case TipoFrequencia.MENSAL:
        return 'Mensal';
      case TipoFrequencia.QUINZENAL:
        return 'Quinzenal';
      case TipoFrequencia.ANUAL:
        return 'Anual';
      case TipoFrequencia.TRIMESTRAL:
        return 'Trimestral';
      case TipoFrequencia.SEMESTRAL:
        return 'Semestral';
      default:
        return 'Desconhecida';
    }
  }
}
