import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/string_util.dart';

class AutoPixReceiptCardDynamicComponentItem {
  final dynamic item;
  final bool showInFront;
  final bool showHour;
  AutoPixReceiptCardDynamicComponentItem(
      {required this.item, this.showInFront = false, this.showHour = false});
}

class AutoPixReceiptCardDynamicComponent {
  static ListTile create(
      {required Map<String, AutoPixReceiptCardDynamicComponentItem> titleValue,
      required String header,
      required BuildContext context}) {
    return ListTile(
      tileColor: J17ThemeColor.surfacesElevate.color(context),
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(J17BorderRadius.small)),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            header,
            style: J17TextStyles.labelLarge(bold: true).textStyle(context),
          ),
          const SizedBox(
            height: J17Padding.smallest,
          ),
          Divider(
            color: J17ThemeColor.textAuxiliary.color(context),
            height: 1,
          ),
          ...titleValue.entries.map((entry) => content(
              entry.value.item, entry.key, context,
              showHour: entry.value.showHour,
              showInfront: entry.value.showInFront)),
        ],
      ),
    );
  }

  static Column content(Object item, String title, BuildContext context,
      {bool showHour = false, bool showInfront = false}) {
    List<String> itemSplit = [];
    if (item.toString().contains(';')) {
      itemSplit = item.toString().split(';');
    } else if (item is DateTime) {
      itemSplit.add(item.formatDateAsddMMyyyy());
      itemSplit.add(
          '${item.hour.toString().padLeft(2, '0')}:${item.minute.toString().padLeft(2, '0')}');
    } else {
      itemSplit.add(item.toString());
    }
    List<String> listTitle = [];
    if (title.contains(';')) {
      listTitle = title.split(';');
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (title.isNotEmpty)
          SizedBox(
            height: J17Padding.regular,
          ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: listTitle.isNotEmpty
                  ? MainAxisAlignment.start
                  : MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  listTitle.isNotEmpty ? listTitle[0] : title,
                  style: J17TextStyles.bodyMedium(
                          color: J17ThemeColor.textSecondaryColor)
                      .textStyle(context),
                ),
                if (listTitle.isNotEmpty) ...[
                  SizedBox(
                    width: J17Padding.large + 3.5,
                  ),
                  Text(
                    listTitle[1],
                    style: J17TextStyles.bodyMedium(
                            color: J17ThemeColor.textSecondaryColor)
                        .textStyle(context),
                  )
                ],
                if (showHour || showInfront)
                  Text(
                    showInfront ? itemSplit[0] : 'Horário',
                    style: showInfront
                        ? J17TextStyles.bodyMedium(bold: true)
                            .textStyle(context)
                        : J17TextStyles.bodyMedium(
                                color: J17ThemeColor.textSecondaryColor)
                            .textStyle(context),
                  )
              ],
            ),
            if (!showInfront && title.isNotEmpty)
              SizedBox(
                height: J17Padding.small,
              ),
            Row(
              mainAxisAlignment: title.contains(';')
                  ? MainAxisAlignment.start
                  : MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: itemSplit.map((text) {
                return (item is DateTime && text.contains(':') && showHour)
                    ? Text(
                        text,
                        style: J17TextStyles.bodyMedium(bold: true)
                            .textStyle(context),
                      )
                    : (item is DateTime && !text.contains(':'))
                        ? Text(
                            text,
                            style: J17TextStyles.bodyMedium(bold: true)
                                .textStyle(context),
                          )
                        : (item is DateTime) == false
                            ? title.contains(';')
                                ? Row(
                                    children: [
                                      Text(
                                        text,
                                        style:
                                            J17TextStyles.bodyMedium(bold: true)
                                                .textStyle(context),
                                      ),
                                      if (title.contains(';'))
                                        SizedBox(
                                          width: J17Padding.extraLarger,
                                        ),
                                    ],
                                  )
                                : Expanded(
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            text,
                                            style: J17TextStyles.bodyMedium(
                                                    bold: true)
                                                .textStyle(context),
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                            : SizedBox.shrink();
              }).toList(),
            )
          ],
        )
      ],
    );
  }
}
