import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/auto_pix_data.dart';
import 'package:j17_bank_mybank_mobile/view/components/custom_page/bottom_page.dart';

class AutoPixCancelModal {
  static Future<ActiveAutorizationAutoPix> initCancel(
      {required BuildContext context, required String idRecorrencia}) async {
    ActiveAutorizationAutoPix activeAutorizationAutoPix =
        ActiveAutorizationAutoPix.Processando;
    return activeAutorizationAutoPix;
  }

  static Future<bool> authorization(
      {required BuildContext context, required String idRecorrencia}) async {
    bool prosseguir = false;
    await BottomPage.abrir(
        context: context,
        bodyContent: Column(
          children: [
            Text(
              "Regras para o cancelamento",
              style:
                  J17TextStyles.headlineMedium(bold: true).textStyle(context),
            ),
            SizedBox(height: J17Padding.large),
            RichText(
                text: TextSpan(
                    text: "Até as 22h",
                    style:
                        J17TextStyles.bodyMedium(bold: true).textStyle(context),
                    children: [
                  TextSpan(
                      text:
                          " - Permite o cancelamento da autorização e de todos os agendamentos do dia seguinte e demais dias, não cancela os do dia atual.",
                      style: J17TextStyles.bodyMedium().textStyle(context)),
                  TextSpan(
                      text: "\n\nApós as 22h",
                      style: J17TextStyles.bodyMedium(bold: true)
                          .textStyle(context)),
                  TextSpan(
                      text:
                          " - Permite cancelamento da autorização, mas não permite o cancelamento dos agendamentos do dia e do dia seguinte, somente dos demais dias.",
                      style: J17TextStyles.bodyMedium().textStyle(context)),
                  TextSpan(
                      text: "\n\nPrazo de cancelamento",
                      style: J17TextStyles.bodyMedium(bold: true)
                          .textStyle(context)),
                  TextSpan(
                      text:
                          " - O cancelamento de autorização será efetuado em um prazo de até 12h.",
                      style: J17TextStyles.bodyMedium().textStyle(context)),
                ])),
            SizedBox(height: J17Padding.large),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: J17ThemeColor.actionAuxiliary.color(context),
              ),
              onPressed: () {
                Navigator.pop(context);
                prosseguir = true;
              },
              child: Text(
                "Continuar",
                style: J17TextStyles.bodyLarge(
                  color: J17ThemeColor.textActionSecondaryColor,
                ).textStyle(context),
              ),
            ),
          ],
        ));
    return prosseguir;
  }

  static Future<bool> cancelamentoAction({
    required BuildContext context,
    bool autorizacao = false,
  }) async {
    bool cancelContinue = false;
    await BottomPage.abrir(
        context: context,
        bodyContent: Column(children: [
          Text(
            autorizacao
                ? "Deseja cancelar a autorização?"
                : "Deseja realmente cancelar o agendamento?",
            style: J17TextStyles.headlineSmall(bold: true).textStyle(context),
          ),
          SizedBox(height: J17Padding.large),
          if (autorizacao) ...[
            Text(
              "Caso você cancele a autorização, a operação não poderá ser desfeita e os pagamentos agendados serão cancelados automaticamente, exceto aquele agendado para hoje, caso exista.",
              style: J17TextStyles.bodyMedium().textStyle(context),
            ),
            SizedBox(height: J17Padding.large),
          ],
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: J17ThemeColor.actionButtonDelete.color(context),
            ),
            onPressed: () async {
              cancelContinue = true;
              Navigator.pop(context);
            },
            child: Text(
              "Confirmar cancelamento",
              style: J17TextStyles.bodyLarge(
                color: J17ThemeColor.textTextDelete,
              ).textStyle(context),
            ),
          ),
          SizedBox(height: J17Padding.large),
          OutlinedButton(
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                color: J17ThemeColor.actionAuxiliary.color(context),
              ),
            ),
            onPressed: () async {
              cancelContinue = false;
              Navigator.pop(context);
            },
            child: Text(
              "Não",
              style: J17TextStyles.bodyLarge(
                color: J17ThemeColor.actionAuxiliary,
              ).textStyle(context),
            ),
          ),
        ]));
    return cancelContinue;
  }
}
