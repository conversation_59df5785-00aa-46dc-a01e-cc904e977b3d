import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/utils/svg_icon.dart';
import 'package:j17_bank_mybank_mobile/view/pages/account/components/custom_filter_chip.dart';
import 'package:j17_bank_mybank_mobile/view/pages/pix/automatic/components/auto_pix_history_filter_enum.dart';

class AutoPixHistoryFilter {
  AutoPixHistoryPeriod? _periodoSelecionado;
  final Set<String> _statusSelecionado = {};

  DateTime? _dataInicioSelecionada;
  DateTime? _dataFimSelecionada;

  // Corrigir o cálculo do count
  int get activeFilterCount {
    int count = 0;
    if (_statusSelecionado.isNotEmpty) count += _statusSelecionado.length;
    if (_periodoSelecionado == AutoPixHistoryPeriod.maisRecentes ||
        _periodoSelecionado == AutoPixHistoryPeriod.maisAntigos) {
      count++;
    }
    if (_periodoSelecionado == AutoPixHistoryPeriod.escolherPeriodo &&
        (_dataInicioSelecionada != null || _dataFimSelecionada != null)) {
      count++;
    }
    return count;
  }

  // Método para limpar todos os filtros
  void clear() {
    _statusSelecionado.clear();
    _periodoSelecionado = null;
    _dataInicioSelecionada = null;
    _dataFimSelecionada = null;
  }

  Set<String> get selectedStatus => _statusSelecionado;
  AutoPixHistoryPeriod? get selectedPeriod => _periodoSelecionado;
  DateTime? get selectedDataInicio => _dataInicioSelecionada;
  DateTime? get selectedDataFim => _dataFimSelecionada;

  // Novo callback para acionar filtragem
  void filterCreateWithCallback(
      BuildContext context,
      Function setState,
      void Function(
              {Set<String> status,
              AutoPixHistoryPeriod? period,
              DateTime? dataInicio,
              DateTime? dataFim})
          onFiltrarPressed) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Padding(
          padding: EdgeInsets.only(
            left: J17Padding.regular,
            right: J17Padding.regular,
            bottom: MediaQuery.of(context).viewInsets.bottom + J17Padding.regular,
            top: J17Padding.large,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Filtros',
                    style: J17TextStyles.headlineSmall().textStyle(context),
                  ),
                  TextButton(
                    onPressed: activeFilterCount > 0
                        ? () {
                            setState(() {
                              _periodoSelecionado = null;
                              _statusSelecionado.clear();
                              _dataInicioSelecionada = null;
                              _dataFimSelecionada = null;
                            });
                            // Aplica a limpeza dos filtros
                            onFiltrarPressed(
                              status: const {},
                              period: null,
                              dataInicio: null,
                              dataFim: null,
                            );
                          }
                        : null,
                    style: TextButton.styleFrom(
                      foregroundColor: activeFilterCount > 0
                          ? J17ThemeColor.actionAuxiliary.color(context)
                          : Colors.grey,
                    ),
                    child: Text(
                      'Limpar',
                      style: J17TextStyles.bodyLarge(
                        color: activeFilterCount > 0
                            ? J17ThemeColor.actionAuxiliary
                            : J17ThemeColor.textAuxiliary,
                      ).textStyle(context),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: J17Padding.regular),
              Text(
                'Status',
                style: J17TextStyles.bodyLarge(
                  bold: true,
                ).textStyle(context),
              ),
              const SizedBox(height: J17Padding.small),
              Wrap(
                spacing: J17Padding.small,
                runSpacing: J17Padding.small,
                children: [
                  CustomFilterChip(
                    label: 'Ativo',
                    value: AutoPixHistoryStatus.ativo.name,
                    selectedValue: _statusSelecionado,
                    onSelected: (value) {
                      setState(() {
                        if (value) {
                          _statusSelecionado.add('ativo');
                        } else {
                          _statusSelecionado.remove('ativo');
                        }
                      });
                    },
                  ),
                  CustomFilterChip(
                    label: 'Pendente',
                    value: AutoPixHistoryStatus.pendente.name,
                    selectedValue: _statusSelecionado,
                    onSelected: (value) {
                      setState(() {
                        if (value) {
                          _statusSelecionado.add('pendente');
                        } else {
                          _statusSelecionado.remove('pendente');
                        }
                      });
                    },
                  ),
                  CustomFilterChip(
                    label: 'Expirado',
                    value: AutoPixHistoryStatus.expirado.name,
                    selectedValue: _statusSelecionado,
                    onSelected: (value) {
                      setState(() {
                        if (value) {
                          _statusSelecionado.add('expirado');
                        } else {
                          _statusSelecionado.remove('expirado');
                        }
                      });
                    },
                  ),
                  CustomFilterChip(
                    label: 'Cancelado',
                    value: AutoPixHistoryStatus.cancelado.name,
                    selectedValue: _statusSelecionado,
                    onSelected: (value) {
                      setState(() {
                        if (value) {
                          _statusSelecionado.add('cancelado');
                        } else {
                          _statusSelecionado.remove('cancelado');
                        }
                      });
                    },
                  ),
                  CustomFilterChip(
                    label: 'Processando',
                    value: AutoPixHistoryStatus.processando.name,
                    selectedValue: _statusSelecionado,
                    onSelected: (value) {
                      setState(() {
                        if (value) {
                          _statusSelecionado.add('processando');
                        } else {
                          _statusSelecionado.remove('processando');
                        }
                      });
                    },
                  ),
                  CustomFilterChip(
                    label: 'Rejeitado',
                    value: AutoPixHistoryStatus.rejeitado.name,
                    selectedValue: _statusSelecionado,
                    onSelected: (value) {
                      setState(() {
                        if (value) {
                          _statusSelecionado.add('rejeitado');
                        } else {
                          _statusSelecionado.remove('rejeitado');
                        }
                      });
                    },
                  ),
                ],
              ),
              const SizedBox(height: J17Padding.regular),
              Text(
                'Períodos',
                style: J17TextStyles.bodyLarge(
                  bold: true,
                ).textStyle(context),
              ),
              const SizedBox(height: J17Padding.small),
              Wrap(
                spacing: J17Padding.small,
                runSpacing: J17Padding.small,
                children: [
                  CustomFilterChip(
                    label: 'Mais recentes',
                    value: AutoPixHistoryPeriod.maisRecentes,
                    selectedValue: _periodoSelecionado,
                    onSelected: (value) {
                      setState(() {
                        _periodoSelecionado =
                            value ? AutoPixHistoryPeriod.maisRecentes : null;
                        if (_periodoSelecionado ==
                            AutoPixHistoryPeriod.maisRecentes) {
                          _dataInicioSelecionada = null;
                          _dataFimSelecionada = null;
                        }
                      });
                    },
                  ),
                  CustomFilterChip(
                    label: 'Mais antigos',
                    value: AutoPixHistoryPeriod.maisAntigos,
                    selectedValue: _periodoSelecionado,
                    onSelected: (value) {
                      setState(
                        () {
                          _periodoSelecionado =
                              value ? AutoPixHistoryPeriod.maisAntigos : null;
                          if (_periodoSelecionado ==
                              AutoPixHistoryPeriod.maisAntigos) {
                            _dataInicioSelecionada = null;
                            _dataFimSelecionada = null;
                          }
                        },
                      );
                    },
                  ),
                  CustomFilterChip(
                    label: 'Escolher período',
                    value: AutoPixHistoryPeriod.escolherPeriodo,
                    selectedValue: _periodoSelecionado,
                    onSelected: (value) {
                      setState(() {
                        _periodoSelecionado =
                            value ? AutoPixHistoryPeriod.escolherPeriodo : null;
                      });
                    },
                  ),
                ],
              ),
              if (_periodoSelecionado ==
                  AutoPixHistoryPeriod.escolherPeriodo) ...[
                const SizedBox(height: J17Padding.regular),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Data inicial',
                            style:
                                J17TextStyles.bodyMedium().textStyle(context),
                          ),
                          const SizedBox(height: J17Padding.small),
                          TextFormField(
                            readOnly: true,
                            controller: TextEditingController(
                              text: _dataInicioSelecionada != null
                                  ? DateFormat('dd/MM/yyyy')
                                      .format(_dataInicioSelecionada!)
                                  : '',
                            ),
                            decoration: InputDecoration(
                              suffixIcon: IconButton(
                                icon: const SvgIcon(icon: J17Icons.calendar),
                                onPressed: () async {
                                  final DateTime? pickedDate =
                                      await showDatePicker(
                                    context: context,
                                    initialDate: _dataInicioSelecionada ??
                                        DateTime.now(),
                                    firstDate: DateTime(2000),
                                    lastDate: DateTime.now(),
                                  );
                                  if (pickedDate != null) {
                                    setState(() {
                                      _dataInicioSelecionada = pickedDate;
                                    });
                                  }
                                },
                              ),
                              border: const OutlineInputBorder(),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: J17Padding.regular),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Data final',
                            style:
                                J17TextStyles.bodyMedium().textStyle(context),
                          ),
                          const SizedBox(height: J17Padding.small),
                          TextFormField(
                            readOnly: true,
                            controller: TextEditingController(
                              text: _dataFimSelecionada != null
                                  ? DateFormat('dd/MM/yyyy')
                                      .format(_dataFimSelecionada!)
                                  : '',
                            ),
                            decoration: InputDecoration(
                              suffixIcon: IconButton(
                                icon: const SvgIcon(icon: J17Icons.calendar),
                                onPressed: () async {
                                  final DateTime? pickedDate =
                                      await showDatePicker(
                                    context: context,
                                    initialDate:
                                        _dataFimSelecionada ?? DateTime.now(),
                                    firstDate: DateTime(2000),
                                    lastDate: DateTime.now(),
                                  );
                                  if (pickedDate != null) {
                                    setState(() {
                                      _dataFimSelecionada = pickedDate;
                                    });
                                  }
                                },
                              ),
                              border: const OutlineInputBorder(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
              const SizedBox(height: J17Padding.large),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    onFiltrarPressed(
                      status: _statusSelecionado,
                      period: _periodoSelecionado,
                      dataInicio: _dataInicioSelecionada,
                      dataFim: _dataFimSelecionada,
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        J17ThemeColor.actionAuxiliary.color(context),
                  ),
                  child: Text(
                    'Filtrar',
                    style: J17TextStyles.bodyLarge(
                      color: J17ThemeColor.textActionSecondaryColor,
                    ).textStyle(context),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Widget do chip de filtro
  Widget filterChip(
      BuildContext context,
      Function setState,
      void Function(
              {Set<String> status,
              AutoPixHistoryPeriod? period,
              DateTime? dataInicio,
              DateTime? dataFim})
          onFiltrarPressed) {
    return Row(
      children: [
        CustomFilterChip(
          type: CustomFilterChipType.filterCount,
          filterCount: activeFilterCount,
          value: 'openFilters',
          selectedValue: true,
          onSelected: (value) {
            filterCreateWithCallback(context, setState, onFiltrarPressed);
          },
        ),
      ],
    );
  }
}
