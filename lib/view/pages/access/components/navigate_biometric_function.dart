import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/utils/secure_logger.dart';
import 'package:j17_bank_mybank_mobile/view/components/loading/loading_modal.dart';
import 'package:j17_bank_mybank_mobile/viewmodel/my_cards_viewmodel.dart';
import 'package:j17_bank_mybank_mobile/viewmodel/session_viewmodel.dart';
import 'package:j17_bank_mybank_mobile/viewmodel/security/components/block_acess_page.dart';

class NavigateBiometricFunction {
  Future<void> navigateToNextScreenAfterBiometric(
    BuildContext context,
    MyCardsViewmodel myCardsViewmodel,
    SessionViewModel sessionViewModel,
  ) async {
    var navigator = Navigator.of(context);

    // Limpa a pilha de navegação
    navigator.popUntil((route) => route.isFirst);

    try {
      SecureLogger.debug(
          '👤 UserView detectado: ${sessionViewModel.userView.name}');
      SecureLogger.debug(
          '🎯 Cartões ativados: ${sessionViewModel.cartaoRequestCompleted}');

      // 🔒 LÓGICA CENTRALIZADA: Usa o getter shouldBlockAccess
      if (sessionViewModel.shouldBlockAccess) {
        // 🚫 onlyCards com cartões ativados: Redireciona para BlockAcessPage
        SecureLogger.debug(
            '🚫 Acesso bloqueado - redirecionando para BlockAcessPage');
        Navigator.of(context).pushReplacement(MaterialPageRoute(
          builder: (context) => const BlockAcessPage(),
        ));
        return;
      }

      // ✅ Navegação normal para home (onlyAccount, hybrid, ou onlyCards sem cartões ativados)
      SecureLogger.debug('✅ Navegação liberada - indo para home');
      navigator.pushReplacementNamed("/home");
    } catch (e) {
      SecureLogger.error('❌ Erro ao navegar após biometria: ', e);
      // Em caso de erro, mostra mensagem e volta para tela de login
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Erro ao processar login biométrico. Tente novamente.'),
          backgroundColor: Colors.red,
        ),
      );
      navigator.pushReplacementNamed("/signin");
    }
  }
}
