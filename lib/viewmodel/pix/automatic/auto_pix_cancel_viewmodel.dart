import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/auto_pix_active_cancel.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/auto_pix_schedule_cancel.dart';
import 'package:j17_bank_mybank_mobile/model/repository/pix/automatic/auto_pix_cancel_repository.dart';

class AutoPixCancelViewmodel {
  final AutoPixCancelRepository _autoPixCancelRepository =
      AutoPixCancelRepository();
  Future<AutoPixActiveCancel> authorizationCancel(
      AutoPixActiveCancel autoPixActiveCancel) async {
    return await _autoPixCancelRepository
        .autoPixActiveCancel(autoPixActiveCancel);
  }

  Future<AutoPixScheduleCancel> scheduleCancel(
      AutoPixScheduleCancel autoPixScheduleCancel) async {
    return await _autoPixCancelRepository
        .autoPixScheduleCancel(autoPixScheduleCancel);
  }
}
