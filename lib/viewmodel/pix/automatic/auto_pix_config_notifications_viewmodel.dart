import 'package:flutter/foundation.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_automatic_authorization_data.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_automatic_authorizations_batch_alteration_data.dart';
import 'package:j17_bank_mybank_mobile/model/repository/pix/automatic/pix_automatic_repository.dart';
import 'package:j17_bank_mybank_mobile/model/exception/business_exception.dart';

class AutoPixConfigNotificationsViewModel extends ChangeNotifier {
  final PixAutomaticRepository _repository = PixAutomaticRepository();
  final bool isNotificationMode;

  AutoPixConfigNotificationsViewModel({this.isNotificationMode = true});

  // Estados
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;
  List<PixAutomaticAuthorization> _authorizations = [];
  Map<String, bool> _notificationSettings = {};
  Map<String, bool> _originalSettings = {}; // Valores originais da API
  bool _selectAll = false;
  bool _hasChanges = false;

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;
  List<PixAutomaticAuthorization> get authorizations => _authorizations;
  Map<String, bool> get notificationSettings => _notificationSettings;
  bool get selectAll => _selectAll;
  bool get hasChanges => _hasChanges;
  bool get canSave => _hasChanges && !_isLoading;

  // Conta quantos itens estão selecionados
  int get selectedCount {
    return _notificationSettings.values.where((enabled) => enabled).length;
  }

  // Métodos
  Future<void> loadAuthorizations() async {
    _setLoading(true);
    _clearMessages();

    try {
      final response = await _repository.listPixAutomaticAuthorizations();

      // Filtra apenas autorizações ACEITA
      _authorizations = response.autorizacoes
          .where((auth) =>
              auth.situacaoPagamentoRecorrente ==
              SituacaoPagamentoRecorrente.ACEITA)
          .toList();

      // Inicializa as configurações com os valores da API
      _notificationSettings.clear();
      _originalSettings.clear(); // Limpa valores originais

      for (final auth in _authorizations) {
        final notificationValue = auth.notificacaoAgendamento;
        _notificationSettings[auth.idRecorrencia] = notificationValue;
        _originalSettings[auth.idRecorrencia] =
            notificationValue; // Armazena valor original
      }

      _updateSelectAll();
      _checkForChanges(); // Verifica se há mudanças

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Erro ao carregar autorizações: $e');
    }
  }

  void toggleSelectAll() {
    _selectAll = !_selectAll;

    // Aplica a seleção para todas as autorizações
    for (final auth in _authorizations) {
      _notificationSettings[auth.idRecorrencia] = _selectAll;
    }

    _checkForChanges();
    notifyListeners();
  }

  void toggleNotification(String idRecorrencia) {
    final currentValue = _notificationSettings[idRecorrencia] ?? false;
    _notificationSettings[idRecorrencia] = !currentValue;

    _updateSelectAll();
    _checkForChanges();
    notifyListeners();
  }

  void _updateSelectAll() {
    if (_authorizations.isEmpty) {
      _selectAll = false;
      return;
    }

    final allEnabled = _authorizations.every(
      (auth) => _notificationSettings[auth.idRecorrencia] == true,
    );

    final allDisabled = _authorizations.every(
      (auth) => _notificationSettings[auth.idRecorrencia] == false,
    );

    // Se todas estão habilitadas, selectAll = true
    // Se todas estão desabilitadas, selectAll = false
    // Se algumas estão habilitadas e outras não, selectAll = false (estado intermediário)
    _selectAll = allEnabled;
  }

  /// Verifica se há mudanças comparando com os valores originais
  void _checkForChanges() {
    if (_authorizations.isEmpty) {
      _hasChanges = false;
      return;
    }

    // Compara cada configuração atual com a original
    for (final auth in _authorizations) {
      final currentValue = _notificationSettings[auth.idRecorrencia] ?? false;
      final originalValue = _originalSettings[auth.idRecorrencia] ?? false;

      if (currentValue != originalValue) {
        _hasChanges = true;
        return;
      }
    }

    _hasChanges = false;
  }

  bool getNotificationStatus(String idRecorrencia) {
    return _notificationSettings[idRecorrencia] ?? false;
  }

  Future<bool> saveChanges(String idConta) async {
    if (!_hasChanges) {
      _setError('Nenhuma alteração para salvar');
      return false;
    }

    _setLoading(true);
    _clearMessages();

    try {
      final autorizacoes = _authorizations.map((auth) {
        return PixAutoBatchAuthorizationsRecurrency(
          identificadorRecorrencia: auth.idRecorrencia,
          novoValorMaximoPagador: null, // Não altera valor máximo
          notificacaoAgendamento: isNotificationMode
              ? (_notificationSettings[auth.idRecorrencia] ?? false)
              : null, // Não altera notificação se não for modo notificação
        );
      }).toList();

      final request = PixAutomaticBatchAuthorizationsRequest(
        descricao: isNotificationMode
            ? 'Alteração de notificações de agendamento em lote'
            : 'Alteração de uso de limite de crédito em lote',
        valor: 0.0, // Não altera valor
        syncId: DateTime.now().millisecondsSinceEpoch.toString(),
        idConta: idConta,
        tipoAlteracaoAutorizacao: isNotificationMode
            ? TipoAlteracaoAutorizacao.NOTIFICACAO_AGENDAMENTO
            : TipoAlteracaoAutorizacao.LIMITE_CREDITO,
        autorizacoes: autorizacoes,
      );

      await _repository.alterarAutorizacoesEmLote(request);
      _setSuccess('Alterações salvas.');

      // Atualiza os valores originais após salvar com sucesso
      _originalSettings.clear();
      for (final auth in _authorizations) {
        _originalSettings[auth.idRecorrencia] =
            _notificationSettings[auth.idRecorrencia] ?? false;
      }

      _hasChanges = false;
      return true;
    } on BusinessException catch (e) {
      _setError('Erro ao salvar configurações: ${e.message}');
      return false;
    } catch (e) {
      _setError('Erro ao salvar configurações: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Métodos privados
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _successMessage = null;
    _isLoading = false;
    notifyListeners();
  }

  void _setSuccess(String message) {
    _successMessage = message;
    _errorMessage = null;
    _isLoading = false;
    notifyListeners();
  }

  void _clearMessages() {
    _errorMessage = null;
    _successMessage = null;
    notifyListeners();
  }

  // ✅ MÉTODO PÚBLICO PARA CONTROLE EXTERNO DO LOADING
  void setLoading(bool loading) {
    _setLoading(loading);
  }
}
