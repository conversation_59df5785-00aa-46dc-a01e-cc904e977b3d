import 'package:flutter/foundation.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_automatic_limit_data.dart';
import 'package:j17_bank_mybank_mobile/model/repository/pix/automatic/pix_automatic_repository.dart';
import 'package:j17_bank_mybank_mobile/utils/secure_logger.dart';

class AutoPixSetLimitViewModel extends ChangeNotifier {
  final PixAutomaticRepository _repository = PixAutomaticRepository();

  // ========== ESTADOS ==========
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  // ========== DADOS ==========
  PixAutomaticLimitResponse? _limitData;
  double _currentValue = 0.0;
  double _maxValue = 0.0;

  // ========== GETTERS ==========
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;
  PixAutomaticLimitResponse? get limitData => _limitData;
  double get currentValue => _currentValue;
  double get maxValue => _maxValue;

  // ========== MÉTODOS ==========

  /// Carrega os dados de limite do Pix Automático
  Future<bool> loadLimitData({required String syncId}) async {
    try {
      _setLoading(true);
      _clearMessages();

      _limitData =
          await _repository.consultarLimitePixAutomatico(syncId: syncId);

      // Atualiza os valores
      _currentValue = _limitData!.valorLimiteTransacaoPersonalizado;
      _maxValue = _limitData!.valorLimiteTransacaoMaximoAprovado;

      _setSuccess('Dados carregados com sucesso');
      SecureLogger.info(
          'Limite do Pix Automático carregado: $_currentValue / $_maxValue');
      return true;
    } catch (e) {
      _setError('Erro ao carregar dados de limite: ${e.toString()}');
      SecureLogger.error('Erro ao carregar limite do Pix Automático', e);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Altera o limite do Pix Automático
  Future<bool> updateLimit({
    required String descricao,
    required double valor,
    required String syncId,
    required String idConta,
    required double valorLimiteTransacao,
  }) async {
    try {
      _setLoading(true);
      _clearMessages();

      final response = await _repository.alterarLimitePixAutomatico(
        descricao: descricao,
        valor: valor,
        syncId: syncId,
        idConta: idConta,
        valorLimiteTransacao: valorLimiteTransacao,
      );

      if (response.statusAlteracao.toLowerCase().contains('sucesso') ||
          response.statusAlteracao.toLowerCase().contains('aprovado')) {
        _setSuccess('Limite alterado com sucesso');
        SecureLogger.info(
            'Limite do Pix Automático alterado: $valorLimiteTransacao');
        return true;
      } else {
        _setError(response.mensagemAlteracao.isNotEmpty
            ? response.mensagemAlteracao
            : 'Erro ao alterar limite');
        return false;
      }
    } catch (e) {
      // _setError('Erro ao alterar limite: ${e.toString()}');
      // SecureLogger.error('Erro ao alterar limite do Pix Automático', e);
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Valida o valor informado
  String? validateValue(double value) {
    if (value <= 0) {
      return 'O valor deve ser maior que zero';
    }

    if (value > _maxValue) {
      return 'O valor informado excede o limite disponível. Por favor, insira um valor dentro do saldo permitido.';
    }

    return null;
  }

  /// Gera syncId único
  String generateSyncId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// ✅ ATUALIZA O VALOR ATUAL DO SLIDER
  void setCurrentValue(double value) {
    _currentValue = value;
    notifyListeners();
  }

  // ========== MÉTODOS PRIVADOS ==========

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _successMessage = null;
    notifyListeners();
  }

  void _setSuccess(String message) {
    _successMessage = message;
    _errorMessage = null;
    notifyListeners();
  }

  void _clearMessages() {
    _errorMessage = null;
    _successMessage = null;
    notifyListeners();
  }
}
