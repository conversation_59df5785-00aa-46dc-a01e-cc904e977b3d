import 'package:flutter/foundation.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_automatic_authorization_data.dart';
import 'package:j17_bank_mybank_mobile/model/repository/pix/automatic/pix_automatic_repository.dart';
import 'package:j17_bank_mybank_mobile/utils/secure_logger.dart';

enum AutoPixHistoryLoadingState {
  idle,
  loading,
  success,
  error,
}

class AutoPixAuthorizationHistoryViewModel extends ChangeNotifier {
  final PixAutomaticRepository _repository = PixAutomaticRepository();

  // Estados
  AutoPixHistoryLoadingState _loadingState = AutoPixHistoryLoadingState.idle;
  String? _errorMessage;

  // Dados
  List<PixAutomaticAuthorization> _allAuthorizations = [];
  Map<String, List<PixAutomaticAuthorization>> _groupedAuthorizations = {};
  List<String> _sortedKeys = [];

  // Getters
  AutoPixHistoryLoadingState get loadingState => _loadingState;
  String? get errorMessage => _errorMessage;
  List<PixAutomaticAuthorization> get allAuthorizations => _allAuthorizations;
  Map<String, List<PixAutomaticAuthorization>> get groupedAuthorizations =>
      _groupedAuthorizations;
  List<String> get sortedKeys => _sortedKeys;

  bool get isLoading => _loadingState == AutoPixHistoryLoadingState.loading;
  bool get hasError => _loadingState == AutoPixHistoryLoadingState.error;
  bool get hasData => _allAuthorizations.isNotEmpty;

  /// Carrega todas as autorizações da API
  Future<void> loadAllAuthorizations() async {
    try {
      _setLoadingState(AutoPixHistoryLoadingState.loading);
      _errorMessage = null;

      final response = await _repository.listPixAutomaticAuthorizations();
      _allAuthorizations = response.autorizacoes;

      await _groupAuthorizationsByDate();
      _setLoadingState(AutoPixHistoryLoadingState.success);

      SecureLogger.info(
          'Carregadas ${_allAuthorizations.length} autorizações do histórico');
    } catch (e) {
      _errorMessage = _getErrorMessage(e);
      _setLoadingState(AutoPixHistoryLoadingState.error);
      SecureLogger.error('Erro ao carregar histórico de autorizações', e);
    }
  }

  /// Carrega autorizações com filtro de data
  Future<void> fetchWithFilter(
      {DateTime? dataInicio, DateTime? dataFim}) async {
    try {
      _setLoadingState(AutoPixHistoryLoadingState.loading);
      _errorMessage = null;

      // Formata as datas para YYYY-MM-DD
      String? dataInicioStr = dataInicio != null
          ? "${dataInicio.year.toString().padLeft(4, '0')}-${dataInicio.month.toString().padLeft(2, '0')}-${dataInicio.day.toString().padLeft(2, '0')}"
          : null;
      String? dataFimStr = dataFim != null
          ? "${dataFim.year.toString().padLeft(4, '0')}-${dataFim.month.toString().padLeft(2, '0')}-${dataFim.day.toString().padLeft(2, '0')}"
          : null;

      SecureLogger.info(
          'Aplicando filtro de data: $dataInicioStr até $dataFimStr');

      final response =
          await _repository.listPixAutomaticAuthorizationsWithFilter(
        dataInicio: dataInicioStr,
        dataFim: dataFimStr,
      );

      _allAuthorizations = response.autorizacoes;
      await _groupAuthorizationsByDate();
      _setLoadingState(AutoPixHistoryLoadingState.success);

      SecureLogger.info(
          'Carregadas ${_allAuthorizations.length} autorizações do histórico (filtro aplicado)');
    } catch (e) {
      _errorMessage = _getErrorMessage(e);
      _setLoadingState(AutoPixHistoryLoadingState.error);
      SecureLogger.error(
          'Erro ao carregar histórico de autorizações (filtro)', e);
    }
  }

  /// Agrupa as autorizações por data
  Future<void> _groupAuthorizationsByDate() async {
    _groupedAuthorizations.clear();
    _sortedKeys.clear();

    for (final authorization in _allAuthorizations) {
      try {
        // Extrai a data da criação da recorrência
        final dateString = authorization.dataHoraCriacaoRecorrencia
            .split('T')[0]; // YYYY-MM-DD

        if (!_groupedAuthorizations.containsKey(dateString)) {
          _groupedAuthorizations[dateString] = [];
        }

        _groupedAuthorizations[dateString]!.add(authorization);
      } catch (e) {
        SecureLogger.error(
            'Erro ao processar data da autorização: ${authorization.idRecorrencia}',
            e);
      }
    }

    // Ordena as chaves por data (mais recente primeiro)
    _sortedKeys = _groupedAuthorizations.keys.toList()
      ..sort((a, b) =>
          b.compareTo(a)); // Ordem decrescente (mais recente primeiro)
  }

  /// Filtra autorizações por status
  List<PixAutomaticAuthorization> getAuthorizationsByStatus(
      SituacaoPagamentoRecorrente status) {
    return _allAuthorizations
        .where((auth) => auth.situacaoPagamentoRecorrente == status)
        .toList();
  }

  /// Filtra autorizações por recebedor
  List<PixAutomaticAuthorization> getAuthorizationsByReceiver(
      String receiverName) {
    return _allAuthorizations
        .where((auth) => auth.recebedor.nome
            .toLowerCase()
            .contains(receiverName.toLowerCase()))
        .toList();
  }

  /// Filtra autorizações por valor mínimo
  List<PixAutomaticAuthorization> getAuthorizationsByMinValue(double minValue) {
    return _allAuthorizations.where((auth) => auth.valor >= minValue).toList();
  }

  /// Filtra autorizações por período
  List<PixAutomaticAuthorization> getAuthorizationsByPeriod(
      DateTime? startDate, DateTime? endDate) {
    return _allAuthorizations.where((auth) {
      try {
        final dataCriacao = DateTime.parse(auth.dataHoraCriacaoRecorrencia);

        if (startDate != null && dataCriacao.isBefore(startDate)) {
          return false;
        }

        if (endDate != null && dataCriacao.isAfter(endDate)) {
          return false;
        }

        return true;
      } catch (e) {
        return false;
      }
    }).toList();
  }

  /// Obtém estatísticas das autorizações
  Map<String, int> getAuthorizationStatistics() {
    final stats = <String, int>{};

    for (final status in SituacaoPagamentoRecorrente.values) {
      stats[status.name] = _allAuthorizations
          .where((auth) => auth.situacaoPagamentoRecorrente == status)
          .length;
    }

    return stats;
  }

  /// Obtém valor total das autorizações
  double get totalValue {
    return _allAuthorizations.fold<double>(
        0.0, (sum, auth) => sum + auth.valor);
  }

  /// Obtém valor médio das autorizações
  double get averageValue {
    return _allAuthorizations.isNotEmpty
        ? totalValue / _allAuthorizations.length
        : 0.0;
  }

  /// Recarrega os dados
  Future<void> refresh() async {
    await loadAllAuthorizations();
  }

  /// Recarrega os dados originais (sem filtros)
  Future<void> reloadOriginalData() async {
    try {
      _setLoadingState(AutoPixHistoryLoadingState.loading);
      _errorMessage = null;

      final response = await _repository.listPixAutomaticAuthorizations();
      _allAuthorizations = response.autorizacoes;

      await _groupAuthorizationsByDate();
      _setLoadingState(AutoPixHistoryLoadingState.success);

      SecureLogger.info(
          'Recarregados ${_allAuthorizations.length} autorizações originais');
    } catch (e) {
      _errorMessage = _getErrorMessage(e);
      _setLoadingState(AutoPixHistoryLoadingState.error);
      SecureLogger.error('Erro ao recarregar dados originais', e);
    }
  }

  /// Limpa o estado de erro
  void clearError() {
    _errorMessage = null;
    if (_loadingState == AutoPixHistoryLoadingState.error) {
      _setLoadingState(AutoPixHistoryLoadingState.idle);
    }
  }

  /// Limpa todos os dados
  void clearData() {
    _allAuthorizations.clear();
    _groupedAuthorizations.clear();
    _sortedKeys.clear();
    notifyListeners();
  }

  /// Filtra localmente por múltiplos status
  void filterByStatus(Set<String> statusNames) {
    SecureLogger.info('🔍 Aplicando filtro de status: $statusNames');

    if (statusNames.isEmpty) {
      // Se não há filtros de status, apenas reagrupa os dados atuais
      SecureLogger.info(
          '🔄 Nenhum status selecionado, reagrupando dados atuais');
      _groupAuthorizationsByDate();
      notifyListeners();
      return;
    }

    // Mapeamento entre strings de filtro e SituacaoPagamentoRecorrente
    // Define quais flags serão destinadas a cada status
    final statusMapping = <String, List<SituacaoPagamentoRecorrente>>{
      'ativo': [
        SituacaoPagamentoRecorrente.AUTORIZADO,
        SituacaoPagamentoRecorrente.ACEITA,
        SituacaoPagamentoRecorrente.ACEITA_USUARIO_PAGADOR,
      ],
      'pendente': [
        SituacaoPagamentoRecorrente.PENDENTE_USUARIO_PAGADOR,
      ],
      'expirado': [
        SituacaoPagamentoRecorrente.EXPIRADO,
      ],
      'cancelado': [
        SituacaoPagamentoRecorrente.CANCELADO,
        SituacaoPagamentoRecorrente.CANCELADA,
      ],
      'processando': [
        SituacaoPagamentoRecorrente.EM_PROCESSAMENTO,
        SituacaoPagamentoRecorrente.AGUARDANDO_REGISTRO_VALIDACAO,
      ],
      'rejeitado': [
        SituacaoPagamentoRecorrente.REJEITADO,
        SituacaoPagamentoRecorrente.REJEITADA,
        SituacaoPagamentoRecorrente.ACEITA_REJEICAO_PSP_RECEBEDOR,
        SituacaoPagamentoRecorrente.ACEITA_REJEITADA_PSP_RECEBEDOR,
        SituacaoPagamentoRecorrente.REJEICAO_USUARIO_PAGADOR,
      ],
    };

    final allowedStatuses = <SituacaoPagamentoRecorrente>{};

    for (final statusName in statusNames) {
      final mappedStatuses = statusMapping[statusName.toLowerCase()];
      if (mappedStatuses != null) {
        allowedStatuses.addAll(mappedStatuses);
        SecureLogger.info('   ✅ Mapeado "$statusName" para: $mappedStatuses');
      } else {
        SecureLogger.error('   ⚠️ Status não mapeado: "$statusName"');
      }
    }

    SecureLogger.info('🎯 Status permitidos: $allowedStatuses');

    // Filtra as autorizações por status
    final filtered = _allAuthorizations.where((auth) {
      return allowedStatuses.contains(auth.situacaoPagamentoRecorrente);
    }).toList();

    SecureLogger.info(
        '📊 Resultado do filtro: ${filtered.length} de ${_allAuthorizations.length} autorizações');

    // Atualiza a lista com os dados filtrados
    _allAuthorizations = filtered;

    // Reagrupa as autorizações filtradas
    _groupAuthorizationsByDate();
    notifyListeners();
  }

  /// Ordena localmente por data de criação da recorrência
  void sortByDate({required bool descending}) {
    // Junta todas as autorizações em uma lista
    final all = _groupedAuthorizations.values.expand((x) => x).toList();

    // Ordena por data
    all.sort((a, b) {
      try {
        final dateA = DateTime.parse(a.dataHoraCriacaoRecorrencia);
        final dateB = DateTime.parse(b.dataHoraCriacaoRecorrencia);
        return descending ? dateB.compareTo(dateA) : dateA.compareTo(dateB);
      } catch (e) {
        return 0; // Mantém ordem original em caso de erro
      }
    });

    // Reagrupa por data
    _groupedAuthorizations.clear();
    _sortedKeys.clear();

    for (final authorization in all) {
      try {
        final dateString =
            authorization.dataHoraCriacaoRecorrencia.split('T')[0];
        if (!_groupedAuthorizations.containsKey(dateString)) {
          _groupedAuthorizations[dateString] = [];
        }
        _groupedAuthorizations[dateString]!.add(authorization);
      } catch (e) {
        SecureLogger.error(
            'Erro ao processar data da autorização: ${authorization.idRecorrencia}',
            e);
      }
    }

    // Ordena as chaves por data
    _sortedKeys = _groupedAuthorizations.keys.toList()
      ..sort((a, b) => descending ? b.compareTo(a) : a.compareTo(b));

    notifyListeners();
  }

  // Métodos privados
  void _setLoadingState(AutoPixHistoryLoadingState state) {
    if (_loadingState != state) {
      _loadingState = state;
      notifyListeners();
    }
  }

  String _getErrorMessage(dynamic error) {
    if (error is Exception) {
      return error.toString().replaceAll('Exception: ', '');
    }
    return error.toString();
  }
}
