import 'package:flutter/foundation.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_automatic_authorization_data.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_automatic_authorizations_batch_alteration_data.dart';
import 'package:j17_bank_mybank_mobile/model/repository/pix/automatic/pix_automatic_repository.dart';

class AutoPixMaxValueViewModel extends ChangeNotifier {
  final PixAutomaticRepository _repository = PixAutomaticRepository();

  // Estados
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;
  List<PixAutomaticAuthorization> _authorizations = [];
  List<String> _selectedIds = [];
  bool _selectAll = false;

  // Getters
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;
  List<PixAutomaticAuthorization> get authorizations => _authorizations;
  List<String> get selectedIds => _selectedIds;
  bool get selectAll => _selectAll;
  bool get hasSelectedItems => _selectedIds.isNotEmpty;

  // Conta quantos itens estão selecionados
  int get selectedCount => _selectedIds.length;

  // Métodos
  Future<void> loadAuthorizations() async {
    _setLoading(true);
    _clearMessages();

    try {
      final response = await _repository.listPixAutomaticAuthorizations();

      // Filtra apenas autorizações ACEITA e VARIAVEL
      _authorizations = response.autorizacoes
          .where((auth) =>
              auth.situacaoPagamentoRecorrente ==
                  SituacaoPagamentoRecorrente.ACEITA &&
              auth.tipoPagamentoRecorrente == 'VARIAVEL')
          .toList();

      _selectedIds = [];
      _selectAll = false;

      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Erro ao carregar autorizações: $e');
    }
  }

  void toggleSelectAll() {
    _selectAll = !_selectAll;

    if (_selectAll) {
      _selectedIds = _authorizations.map((auth) => auth.idRecorrencia).toList();
    } else {
      _selectedIds = [];
    }

    notifyListeners();
  }

  void toggleAuthorization(String idRecorrencia) {
    if (_selectedIds.contains(idRecorrencia)) {
      _selectedIds.remove(idRecorrencia);
    } else {
      _selectedIds.add(idRecorrencia);
    }

    // Atualiza selectAll baseado na seleção
    _selectAll = _selectedIds.length == _authorizations.length;

    notifyListeners();
  }

  double? getMaxValueFromSelected() {
    if (_selectedIds.isEmpty) return null;

    double maxValue = 0.0;
    for (final id in _selectedIds) {
      final auth = _authorizations.firstWhere(
        (auth) => auth.idRecorrencia == id,
      );

      // Para autorizações VARIAVEL, usa valorMaximoPagador
      final authValue = auth.valorMaximoPagador;

      if (authValue > maxValue) {
        maxValue = authValue;
      }
    }

    return maxValue;
  }

  String? getMaxValueAuthorizationName() {
    if (_selectedIds.isEmpty) return null;

    double maxValue = 0.0;
    String? maxAuthName;

    for (final id in _selectedIds) {
      final auth = _authorizations.firstWhere(
        (auth) => auth.idRecorrencia == id,
      );

      // Para autorizações VARIAVEL, usa valorMaximoPagador
      final authValue = auth.valorMaximoPagador;

      if (authValue > maxValue) {
        maxValue = authValue;
        maxAuthName = auth.recebedor.nome;
      }
    }

    return maxAuthName;
  }

  bool isValueValid(double value) {
    final maxValue = getMaxValueFromSelected();
    if (maxValue == null) return false;

    // Verifica se o valor é maior que o maior valor das autorizações selecionadas
    if (value <= maxValue) return false;

    // Verifica se o valor é maior ou igual ao pisoValorMaximo das autorizações selecionadas
    for (final id in _selectedIds) {
      final auth = _authorizations.firstWhere(
        (auth) => auth.idRecorrencia == id,
      );

      if (value < auth.pisoValorMaximo) {
        return false;
      }
    }

    return true;
  }

  String? getValidationMessage(double value) {
    if (_selectedIds.isEmpty) {
      return 'Nenhuma autorização selecionada';
    }

    // Verifica se o valor é maior que o maior valor das autorizações
    final maxValue = getMaxValueFromSelected();
    if (maxValue != null && value <= maxValue) {
      final maxAuthName = getMaxValueAuthorizationName();

      if (_selectedIds.length == 1) {
        return 'O valor máximo deve ser superior à autorização selecionada.';
      } else {
        return 'O valor máximo deve ser superior à autorização de maior valor. ($maxAuthName).';
      }
    }

    // Verifica se o valor é maior ou igual ao pisoValorMaximo
    for (final id in _selectedIds) {
      final auth = _authorizations.firstWhere(
        (auth) => auth.idRecorrencia == id,
      );

      if (value < auth.pisoValorMaximo) {
        final formattedPiso =
            auth.pisoValorMaximo.toStringAsFixed(2).replaceAll('.', ',');
        if (_selectedIds.length == 1) {
          return 'O valor máximo deve ser maior ou igual ao valor mínimo permitido (R\$ $formattedPiso).';
        } else {
          return 'O valor máximo deve ser maior ou igual ao valor mínimo permitido (R\$ $formattedPiso).';
        }
      }
    }

    return null;
  }

  Future<bool> saveMaxValue(double maxValue, String idConta) async {
    if (_selectedIds.isEmpty) {
      _setError('Nenhuma autorização selecionada');
      return false;
    }

    final validationMessage = getValidationMessage(maxValue);
    if (validationMessage != null) {
      _setError(validationMessage);
      return false;
    }

    _setLoading(true);
    _clearMessages();

    try {
      final autorizacoes = _selectedIds.map((id) {
        return PixAutoBatchAuthorizationsRecurrency(
          identificadorRecorrencia: id,
          novoValorMaximoPagador: maxValue,
          notificacaoAgendamento: null,
        );
      }).toList();

      final request = PixAutomaticBatchAuthorizationsRequest(
        descricao: 'Alteração de valor máximo em lote',
        valor: maxValue,
        syncId: DateTime.now().millisecondsSinceEpoch.toString(),
        idConta: idConta,
        tipoAlteracaoAutorizacao: TipoAlteracaoAutorizacao.VALOR_MAXIMO,
        autorizacoes: autorizacoes,
      );

      await _repository.alterarAutorizacoesEmLote(request);
      _setSuccess('Valor máximo alterado com sucesso');
      return true;
    } catch (e) {
      _setError('Erro ao salvar valor máximo: $e');
      return false;
    }
  }

  // Métodos privados
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _successMessage = null;
    _isLoading = false;
    notifyListeners();
  }

  void _setSuccess(String message) {
    _successMessage = message;
    _errorMessage = null;
    _isLoading = false;
    notifyListeners();
  }

  void _clearMessages() {
    _errorMessage = null;
    _successMessage = null;
    notifyListeners();
  }
}
