import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/auto_pix_schedule.dart';
import 'package:j17_bank_mybank_mobile/model/repository/pix/automatic/auto_pix_schedule_list_repository.dart';

class AutoPixScheduleListViewmodel extends ChangeNotifier {
  final AutoPixScheduleListRepository _autoPixScheduleListRepository =
      AutoPixScheduleListRepository();
  AutoPixSchedule? _autoPixSchedule;
  AutoPixSchedule? get autoPixSchedule => _autoPixSchedule;
  bool _carregou = false;
  bool get carregou => _carregou;
  Future list() async {
    _autoPixSchedule = await _autoPixScheduleListRepository.list();
    _carregou = true;
    notifyListeners();
  }
}
