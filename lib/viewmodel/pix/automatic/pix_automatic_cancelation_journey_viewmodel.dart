import 'package:flutter/foundation.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_automatic_authorization_data.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_automatic_cancel_data.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/pix_automatic_cancel_authorization_status_data.dart'
    hide TipoPessoaSolicitante;
import 'package:j17_bank_mybank_mobile/model/repository/pix/automatic/pix_automatic_repository.dart';
import 'package:j17_bank_mybank_mobile/utils/secure_logger.dart';

enum CancelationLoadingState {
  idle,
  validating,
  canceling,
  consultingStatus,
  success,
  error,
}

class CancelationRequest {
  final String identificadorRecorrencia;
  final String descricao;
  final double valor;
  final String idConta;
  final TipoPessoaSolicitante tipoPessoaSolicitante;
  final MotivoCancelamentoDetalhado motivo;
  final DateTime requestTime;
  String? cancelationId;
  PixAutomaticCancelResponse? response;
  String? errorMessage;

  CancelationRequest({
    required this.identificadorRecorrencia,
    required this.descricao,
    required this.valor,
    required this.idConta,
    required this.tipoPessoaSolicitante,
    required this.motivo,
    DateTime? requestTime,
  }) : requestTime = requestTime ?? DateTime.now();

  bool get isProcessed => response != null;
  bool get hasError => errorMessage != null;
  String get motivoDescription => motivo.description;

  Map<String, dynamic> toJson() {
    return {
      'identificadorRecorrencia': identificadorRecorrencia,
      'descricao': descricao,
      'valor': valor,
      'idConta': idConta,
      'tipoPessoaSolicitante': tipoPessoaSolicitante.name,
      'motivo': motivo.name,
      'requestTime': requestTime.toIso8601String(),
      'cancelationId': cancelationId,
      'isProcessed': isProcessed,
      'hasError': hasError,
      'errorMessage': errorMessage,
    };
  }
}

class CancelationValidation {
  final bool canCancel;
  final List<String> reasons;
  final PixAutomaticAuthorization? authorization;
  final DateTime validationTime;

  CancelationValidation({
    required this.canCancel,
    required this.reasons,
    this.authorization,
    DateTime? validationTime,
  }) : validationTime = validationTime ?? DateTime.now();

  bool get hasReasons => reasons.isNotEmpty;
  String get reasonsText => reasons.join(', ');
}

class PixAutomaticCancelationViewModel extends ChangeNotifier {
  final PixAutomaticRepository _repository = PixAutomaticRepository();

  // ========== ESTADOS ==========
  CancelationLoadingState _loadingState = CancelationLoadingState.idle;
  String? _errorMessage;
  String? _successMessage;

  // ========== DADOS PRINCIPAIS ==========
  final List<CancelationRequest> _cancelationRequests = [];
  final List<PixAutomaticCancelResponse> _cancelationHistory = [];
  final Map<String, CancelationValidation> _validationCache = {};

  // ========== DADOS ATUAIS ==========
  CancelationRequest? _currentRequest;
  PixAutomaticCancelResponse? _lastCancelResponse;
  PixAutomaticCancelAuthorizationStatusResponse? _lastStatusResponse;
  CancelationValidation? _lastValidation;

  // ========== FORMULÁRIO DE CANCELAMENTO ==========
  String _identificadorRecorrencia = '';
  String _descricao = '';
  double _valor = 0.0;
  String _idConta = '';
  TipoPessoaSolicitante _tipoPessoaSolicitante =
      TipoPessoaSolicitante.PESSOA_FISICA;
  MotivoCancelamentoDetalhado _motivo =
      MotivoCancelamentoDetalhado.SOLICITACAO_CLIENTE;

  // ========== FILTROS E BUSCA ==========
  String _searchQuery = '';
  MotivoCancelamentoDetalhado? _motivoFilter;
  TipoPessoaSolicitante? _tipoPessoaFilter;
  DateTime? _dataInicioFilter;
  DateTime? _dataFimFilter;

  // ========== GETTERS ==========
  CancelationLoadingState get loadingState => _loadingState;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;
  List<CancelationRequest> get cancelationRequests =>
      List.unmodifiable(_cancelationRequests);
  List<PixAutomaticCancelResponse> get cancelationHistory =>
      List.unmodifiable(_cancelationHistory);
  CancelationRequest? get currentRequest => _currentRequest;
  PixAutomaticCancelResponse? get lastCancelResponse => _lastCancelResponse;
  PixAutomaticCancelAuthorizationStatusResponse? get lastStatusResponse =>
      _lastStatusResponse;
  CancelationValidation? get lastValidation => _lastValidation;

  bool get isValidating => _loadingState == CancelationLoadingState.validating;
  bool get isCanceling => _loadingState == CancelationLoadingState.canceling;
  bool get isConsultingStatus =>
      _loadingState == CancelationLoadingState.consultingStatus;
  bool get isLoading => isValidating || isCanceling || isConsultingStatus;
  bool get hasError => _loadingState == CancelationLoadingState.error;
  bool get hasSuccess => _loadingState == CancelationLoadingState.success;

  // Getters do formulário
  String get identificadorRecorrencia => _identificadorRecorrencia;
  String get descricao => _descricao;
  double get valor => _valor;
  String get idConta => _idConta;
  TipoPessoaSolicitante get tipoPessoaSolicitante => _tipoPessoaSolicitante;
  MotivoCancelamentoDetalhado get motivo => _motivo;

  // Getters dos filtros
  String get searchQuery => _searchQuery;
  MotivoCancelamentoDetalhado? get motivoFilter => _motivoFilter;
  TipoPessoaSolicitante? get tipoPessoaFilter => _tipoPessoaFilter;
  DateTime? get dataInicioFilter => _dataInicioFilter;
  DateTime? get dataFimFilter => _dataFimFilter;

  // ========== SETTERS DO FORMULÁRIO ==========

  void setIdentificadorRecorrencia(String value) {
    _identificadorRecorrencia = value;
    _lastValidation = null; // Limpa validação anterior
    notifyListeners();
  }

  void setDescricao(String value) {
    _descricao = value;
    notifyListeners();
  }

  void setValor(double value) {
    _valor = value;
    notifyListeners();
  }

  void setIdConta(String value) {
    _idConta = value;
    notifyListeners();
  }

  void setTipoPessoaSolicitante(TipoPessoaSolicitante value) {
    _tipoPessoaSolicitante = value;
    notifyListeners();
  }

  void setMotivo(MotivoCancelamentoDetalhado value) {
    _motivo = value;
    _descricao = value.description; // Auto-preenche a descrição
    notifyListeners();
  }

  // ========== SETTERS DOS FILTROS ==========

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void setMotivoFilter(MotivoCancelamentoDetalhado? motivo) {
    _motivoFilter = motivo;
    notifyListeners();
  }

  void setTipoPessoaFilter(TipoPessoaSolicitante? tipoPessoa) {
    _tipoPessoaFilter = tipoPessoa;
    notifyListeners();
  }

  void setDataFilter({DateTime? inicio, DateTime? fim}) {
    _dataInicioFilter = inicio;
    _dataFimFilter = fim;
    notifyListeners();
  }

  void clearFilters() {
    _searchQuery = '';
    _motivoFilter = null;
    _tipoPessoaFilter = null;
    _dataInicioFilter = null;
    _dataFimFilter = null;
    notifyListeners();
  }

  // ========== MÉTODOS PRINCIPAIS ==========

  /// Valida se um cancelamento pode ser realizado
  Future<CancelationValidation> validateCancelation(
      String identificadorRecorrencia) async {
    try {
      _setLoadingState(CancelationLoadingState.validating);
      _clearMessages();

      // Verifica cache primeiro
      if (_validationCache.containsKey(identificadorRecorrencia)) {
        final cached = _validationCache[identificadorRecorrencia]!;
        // Cache válido por 5 minutos
        if (DateTime.now().difference(cached.validationTime).inMinutes < 5) {
          _lastValidation = cached;
          _setLoadingState(CancelationLoadingState.idle);
          return cached;
        }
      }

      final validationResult =
          await _repository.validarCancelamento(identificadorRecorrencia);

      final validation = CancelationValidation(
        canCancel: validationResult['canCancel'] ?? false,
        reasons: List<String>.from(validationResult['reasons'] ?? []),
        authorization: validationResult['authorization'],
      );

      _validationCache[identificadorRecorrencia] = validation;
      _lastValidation = validation;

      if (validation.canCancel) {
        _setSuccess('Cancelamento pode ser realizado');
      } else {
        _setError(
            'Cancelamento não pode ser realizado: ${validation.reasonsText}');
      }

      SecureLogger.info(
          'Validação de cancelamento para $identificadorRecorrencia: ${validation.canCancel}');
      return validation;
    } catch (e) {
      _setError('Erro ao validar cancelamento: ${e.toString()}');
      SecureLogger.error('Erro na validação de cancelamento', e);

      final errorValidation = CancelationValidation(
        canCancel: false,
        reasons: ['Erro interno na validação'],
      );

      _lastValidation = errorValidation;
      return errorValidation;
    }
  }

  /// Executa cancelamento
  Future<bool> executeCancelation({
    String? customIdentificadorRecorrencia,
    String? customDescricao,
    double? customValor,
    String? customIdConta,
    TipoPessoaSolicitante? customTipoPessoaSolicitante,
  }) async {
    final identificador =
        customIdentificadorRecorrencia ?? _identificadorRecorrencia;
    final descricao = customDescricao ?? _descricao;
    final valor = customValor ?? _valor;
    final idConta = customIdConta ?? _idConta;
    final tipoPessoa = customTipoPessoaSolicitante ?? _tipoPessoaSolicitante;

    // Validação dos campos obrigatórios
    final validationErrors =
        _validateForm(identificador, descricao, valor, idConta);
    if (validationErrors.isNotEmpty) {
      _setError('Erro de validação: ${validationErrors.join(', ')}');
      return false;
    }

    try {
      _setLoadingState(CancelationLoadingState.canceling);
      _clearMessages();

      // Cria request
      final request = CancelationRequest(
        identificadorRecorrencia: identificador,
        descricao: descricao,
        valor: valor,
        idConta: idConta,
        tipoPessoaSolicitante: tipoPessoa,
        motivo: _motivo,
      );

      _currentRequest = request;
      _cancelationRequests.add(request);

      // Executa cancelamento
      _lastCancelResponse = await _repository.cancelPixAutomaticPaymentNew(
        identificadorRecorrencia: identificador,
        descricao: descricao,
        valor: valor,
        idConta: idConta,
        tipoPessoaSolicitante: tipoPessoa,
      );

      // Atualiza request com resposta
      request.response = _lastCancelResponse;
      request.cancelationId =
          _lastCancelResponse!.resposta.identificadorCancelamento;

      _setSuccess('Cancelamento executado com sucesso');
      SecureLogger.info('Cancelamento executado: ${request.cancelationId}');

      // Limpa formulário após sucesso
      _resetForm();

      return true;
    } catch (e) {
      if (_currentRequest != null) {
        _currentRequest!.errorMessage = e.toString();
      }

      _setError('Erro ao executar cancelamento: ${e.toString()}');
      SecureLogger.error('Erro na execução do cancelamento', e);
      return false;
    }
  }

  /// Consulta status de cancelamento
  Future<PixAutomaticCancelAuthorizationStatusResponse?>
      consultCancelationStatus({
    required String identificadorCancelamento,
    required String identificadorRecorrencia,
    String? syncId,
  }) async {
    try {
      _setLoadingState(CancelationLoadingState.consultingStatus);
      _clearMessages();

      final finalSyncId =
          syncId ?? DateTime.now().millisecondsSinceEpoch.toString();

      _lastStatusResponse =
          await _repository.consultarSituacaoCancelamentoAutorizacao(
        identificadorCancelamento: identificadorCancelamento,
        identificadorRecorrencia: identificadorRecorrencia,
        syncId: finalSyncId,
      );

      _setSuccess('Status consultado com sucesso');
      SecureLogger.info(
          'Status de cancelamento consultado: $identificadorCancelamento');

      return _lastStatusResponse;
    } catch (e) {
      _setError('Erro ao consultar status: ${e.toString()}');
      SecureLogger.error('Erro na consulta de status de cancelamento', e);
      return null;
    }
  }

  /// Carrega histórico de cancelamentos
  Future<void> loadCancelationHistory({
    String? dataInicio,
    String? dataFim,
    String? idConta,
  }) async {
    try {
      _setLoadingState(CancelationLoadingState.validating); // Reutiliza estado
      _clearMessages();

      final history = await _repository.getCancelHistory(
        dataInicio: dataInicio,
        dataFim: dataFim,
        idConta: idConta,
      );

      _cancelationHistory.clear();
      _cancelationHistory.addAll(history);

      _setSuccess('Histórico carregado: ${history.length} cancelamentos');
      SecureLogger.info(
          'Histórico de cancelamentos carregado: ${history.length} itens');
    } catch (e) {
      _setError('Erro ao carregar histórico: ${e.toString()}');
      SecureLogger.error('Erro ao carregar histórico de cancelamentos', e);
    }
  }

  // ========== MÉTODOS DE CONVENIÊNCIA ==========

  /// Cancela com motivo pré-definido
  Future<bool> cancelWithPredefinedReason({
    required String identificadorRecorrencia,
    required MotivoCancelamentoDetalhado motivo,
    required double valor,
    required String idConta,
    TipoPessoaSolicitante tipoPessoaSolicitante =
        TipoPessoaSolicitante.PESSOA_FISICA,
  }) async {
    setMotivo(motivo);

    return await executeCancelation(
      customIdentificadorRecorrencia: identificadorRecorrencia,
      customDescricao: motivo.description,
      customValor: valor,
      customIdConta: idConta,
      customTipoPessoaSolicitante: tipoPessoaSolicitante,
    );
  }

  /// Executa fluxo completo de cancelamento (validação + execução)
  Future<bool> executeCompleteCancelationFlow() async {
    // Primeiro valida
    final validation = await validateCancelation(_identificadorRecorrencia);
    if (!validation.canCancel) {
      return false;
    }

    // Depois executa o cancelamento
    return await executeCancelation();
  }

  /// Busca request de cancelamento por identificador
  CancelationRequest? findRequestByIdentifier(String identificadorRecorrencia) {
    try {
      return _cancelationRequests.firstWhere(
        (request) =>
            request.identificadorRecorrencia == identificadorRecorrencia,
      );
    } catch (e) {
      return null;
    }
  }

  /// Busca requests por motivo
  List<CancelationRequest> getRequestsByMotivo(
      MotivoCancelamentoDetalhado motivo) {
    return _cancelationRequests
        .where((request) => request.motivo == motivo)
        .toList();
  }

  /// Busca requests pendentes (sem resposta)
  List<CancelationRequest> get pendingRequests {
    return _cancelationRequests
        .where((request) => !request.isProcessed)
        .toList();
  }

  /// Busca requests com erro
  List<CancelationRequest> get errorRequests {
    return _cancelationRequests.where((request) => request.hasError).toList();
  }

  /// Requests filtrados
  List<CancelationRequest> get filteredRequests {
    var result = List<CancelationRequest>.from(_cancelationRequests);

    // Filtro por busca
    if (_searchQuery.isNotEmpty) {
      result = result
          .where((request) =>
              request.identificadorRecorrencia
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              request.descricao
                  .toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ||
              (request.cancelationId
                      ?.toLowerCase()
                      .contains(_searchQuery.toLowerCase()) ??
                  false))
          .toList();
    }

    // Filtro por motivo
    if (_motivoFilter != null) {
      result =
          result.where((request) => request.motivo == _motivoFilter).toList();
    }

    // Filtro por tipo de pessoa
    if (_tipoPessoaFilter != null) {
      result = result
          .where(
              (request) => request.tipoPessoaSolicitante == _tipoPessoaFilter)
          .toList();
    }

    // Filtro por data
    if (_dataInicioFilter != null) {
      result = result
          .where((request) => request.requestTime.isAfter(_dataInicioFilter!))
          .toList();
    }
    if (_dataFimFilter != null) {
      result = result
          .where((request) => request.requestTime.isBefore(_dataFimFilter!))
          .toList();
    }

    return result;
  }

  // ========== MÉTODOS DE ESTATÍSTICAS ==========

  /// Obtém estatísticas de cancelamento
  Map<String, dynamic> getCancelationStatistics() {
    final requests = _cancelationRequests;
    final history = _cancelationHistory;

    return {
      'totalRequests': requests.length,
      'processedRequests': requests.where((r) => r.isProcessed).length,
      'pendingRequests':
          requests.where((r) => !r.isProcessed && !r.hasError).length,
      'errorRequests': requests.where((r) => r.hasError).length,
      'historyCount': history.length,
      'byMotivo': _getStatisticsByMotivo(requests),
      'byTipoPessoa': _getStatisticsByTipoPessoa(requests),
      'averageValue': _calculateAverageValue(requests),
      'totalValue': _calculateTotalValue(requests),
    };
  }

  Map<String, int> _getStatisticsByMotivo(List<CancelationRequest> requests) {
    final stats = <String, int>{};
    for (final motivo in MotivoCancelamentoDetalhado.values) {
      stats[motivo.name] = requests.where((r) => r.motivo == motivo).length;
    }
    return stats;
  }

  Map<String, int> _getStatisticsByTipoPessoa(
      List<CancelationRequest> requests) {
    final stats = <String, int>{};
    for (final tipo in TipoPessoaSolicitante.values) {
      stats[tipo.name] =
          requests.where((r) => r.tipoPessoaSolicitante == tipo).length;
    }
    return stats;
  }

  double _calculateAverageValue(List<CancelationRequest> requests) {
    if (requests.isEmpty) return 0.0;
    final totalValue =
        requests.fold<double>(0.0, (sum, request) => sum + request.valor);
    return totalValue / requests.length;
  }

  double _calculateTotalValue(List<CancelationRequest> requests) {
    return requests.fold<double>(0.0, (sum, request) => sum + request.valor);
  }

  // ========== MÉTODOS DE VALIDAÇÃO ==========

  List<String> _validateForm(
      String identificador, String descricao, double valor, String idConta) {
    final errors = <String>[];

    if (identificador.isEmpty)
      errors.add('Identificador de recorrência é obrigatório');
    if (descricao.isEmpty) errors.add('Descrição é obrigatória');
    if (valor < 0) errors.add('Valor não pode ser negativo');
    if (idConta.isEmpty) errors.add('ID da conta é obrigatório');

    return errors;
  }

  /// Verifica se o formulário é válido
  bool get isFormValid =>
      _validateForm(_identificadorRecorrencia, _descricao, _valor, _idConta)
          .isEmpty;

  /// Verifica se pode executar cancelamento
  bool get canExecuteCancelation {
    return isFormValid && !isLoading && (_lastValidation?.canCancel ?? false);
  }

  // ========== MÉTODOS DE LIMPEZA ==========

  void _resetForm() {
    _identificadorRecorrencia = '';
    _descricao = '';
    _valor = 0.0;
    _idConta = '';
    _tipoPessoaSolicitante = TipoPessoaSolicitante.PESSOA_FISICA;
    _motivo = MotivoCancelamentoDetalhado.SOLICITACAO_CLIENTE;
    _currentRequest = null;
    _lastValidation = null;
  }

  /// Reseta formulário
  void resetForm() {
    _resetForm();
    notifyListeners();
  }

  /// Limpa dados
  void clearData() {
    _resetForm();
    _cancelationRequests.clear();
    _cancelationHistory.clear();
    _validationCache.clear();
    _lastCancelResponse = null;
    _lastStatusResponse = null;
    clearFilters();
    _clearMessages();
    _setLoadingState(CancelationLoadingState.idle);
  }

  /// Limpa cache de validação
  void clearValidationCache() {
    _validationCache.clear();
    _lastValidation = null;
    notifyListeners();
  }

  // ========== MÉTODOS PRIVADOS ==========

  void _setLoadingState(CancelationLoadingState state) {
    if (_loadingState != state) {
      _loadingState = state;
      notifyListeners();
    }
  }

  void _setError(String message) {
    _errorMessage = message;
    _successMessage = null;
    _setLoadingState(CancelationLoadingState.error);
  }

  void _setSuccess(String message) {
    _successMessage = message;
    _errorMessage = null;
    _setLoadingState(CancelationLoadingState.success);
  }

  void _clearMessages() {
    _errorMessage = null;
    _successMessage = null;
  }

  /// Limpa mensagens
  void clearMessages() {
    _clearMessages();
    notifyListeners();
  }

  @override
  void dispose() {
    clearData();
    super.dispose();
  }
}
