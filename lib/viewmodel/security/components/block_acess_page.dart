import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/constants/constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/text_style_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/view/components/list/views/icon_component.dart';

class BlockAcessPage extends StatelessWidget {
  const BlockAcessPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Acesso ao aplicativo",
          style: J17TextStyles.headlineMedium().textStyle(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.only(
          top: J17Padding.large,
          left: J17Padding.regular,
          right: J17Padding.regular,
          bottom: J17Padding.larger,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                spacing: J17Padding.large,
                children: [
                  IconComponent(
                    model: IconSvgModel(
                      image: J17Icons.danger,
                      size: J17IconSizes.huge,
                      color: J17ThemeColor.textPrimaryColor,
                    ),
                  ),
                  Text(
                    "Identificamos que você possui um cartão de crédito J17. Atualmente, o acesso ao aplicativo está disponível apenas para usuários com conta corrente.\n\nSe você acredita que isso foi um  engano, entre em contato com nosso suporte oficial para mais informmações.",
                    style: J17TextStyles.bodyLarge().textStyle(context),
                    textAlign: TextAlign.start,
                  ),
                ],
              ),
            ),
            Column(
              spacing: J17Padding.large,
              children: [
                OutlinedButton(
                  onPressed: () {
                    Navigator.of(context).pushReplacementNamed('/signin');
                  },
                  child: Text(
                    "Voltar ao login",
                    style: J17TextStyles.bodyLarge().textStyle(context),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
