import 'package:flutter/foundation.dart';
import 'package:j17_bank_mybank_mobile/model/exception/business_exception.dart';
import 'package:j17_bank_mybank_mobile/model/repository/access_repository.dart';
import 'package:j17_bank_mybank_mobile/model/repository/topaz_repository.dart';
import 'package:j17_bank_mybank_mobile/utils/secure_logger.dart';

typedef TopazDeviceAuthorizer = Future<bool> Function(String cpf);

class DeviceRegistrationViewmodel {
  final AccessRepository _accessRepository = AccessRepository();

  final TopazRepository _topazRepository = TopazRepository();

  /// Registra o token de segurança do dispositivo
  /// Se o token não existir, solicita autorização via SMS
  Future<void> registraToken(
      String cpf, TopazDeviceAuthorizer deviceAuthorizer) async {
    try {
      SecureLogger.debug(
          'DeviceRegistrationViewmodel: Iniciando registro de token para CPF: $cpf');

      if (kReleaseMode) {
        // ✅ VERIFICAÇÃO: Obtém token existente primeiro
        var token = await _topazRepository.getToken(cpf);

        SecureLogger.debug(
            'DeviceRegistrationViewmodel: Token existente: ${token != null ? "SIM" : "NÃO"}');

        if (token == null) {
          SecureLogger.debug(
              'DeviceRegistrationViewmodel: Token não encontrado, solicitando autorização...');

          bool success = await deviceAuthorizer(cpf);

          if (!success) {
            SecureLogger.error(
                'DeviceRegistrationViewmodel: Falha na autorização do dispositivo');
            throw const BusinessException(
                "Não foi possível realizar a verificação antifraude."
                " Tente novamente ou contate um de nossos canais de atendimento.");
          }

          // ✅ VERIFICAÇÃO: Tenta obter token novamente após autorização
          token = await _topazRepository.getToken(cpf);

          if (token == null) {
            SecureLogger.error(
                'DeviceRegistrationViewmodel: Token ainda não disponível após autorização');
            throw const BusinessException(
                "Token de segurança não disponível. Aguarde alguns instantes e tente novamente.");
          }
        }

        if (token != null) {
          var syncId = await _topazRepository.syncId();

          if (syncId != null) {
            SecureLogger.debug(
                'DeviceRegistrationViewmodel: Registrando token no servidor...');
            await _accessRepository.registraToken(token, syncId);
            SecureLogger.debug(
                'DeviceRegistrationViewmodel: Token registrado com sucesso');
            return;
          } else {
            SecureLogger.error(
                'DeviceRegistrationViewmodel: SyncID não disponível');
            throw const BusinessException(
                "Erro na sincronização do dispositivo. Tente novamente.");
          }
        }

        throw const BusinessException(
            "Não foi possível realizar a verificação antifraude."
            " Tente novamente ou contate um de nossos canais de atendimento.");
      } else {
        SecureLogger.debug(
            'DeviceRegistrationViewmodel: Modo debug - pulando registro de token');
      }
    } catch (e) {
      SecureLogger.error(
          'DeviceRegistrationViewmodel: Erro no registro de token', e);
      rethrow;
    }
  }
}
