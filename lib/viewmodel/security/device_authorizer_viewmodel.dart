import 'package:flutter/cupertino.dart';
import 'package:j17_bank_mybank_mobile/model/exception/business_exception.dart';
import 'package:j17_bank_mybank_mobile/model/repository/access_repository.dart';
import 'package:j17_bank_mybank_mobile/model/repository/topaz_repository.dart';
import 'package:j17_bank_mybank_mobile/utils/secure_logger.dart';
import 'package:j17_bank_mybank_mobile/viewmodel/abstract_otp_viewmodel.dart';

class DeviceAuthorizerViewmodel extends ChangeNotifier
    implements AbstractOtpViewModel {
  final _topazRepository = TopazRepository();
  final _accessRepository = AccessRepository();

  final String cpf;

  bool _validated = false;
  bool _isLoading = false;

  DeviceAuthorizerViewmodel({required this.cpf});

  bool get validated => _validated;
  bool get isLoading => _isLoading;

  @override
  Future<void> sendOtp() async {
    try {
      SecureLogger.debug(
          'DeviceAuthorizerViewmodel: Iniciando envio de OTP para CPF: $cpf');
      _isLoading = true;
      notifyListeners();

      var syncId = await _topazRepository.syncId();

      if (syncId != null) {
        await _accessRepository.authorizeToken(syncId);
        await _topazRepository.requestAuthorization(cpf);
        SecureLogger.debug(
            'DeviceAuthorizerViewmodel: OTP enviado com sucesso');
      } else {
        throw const BusinessException(
            "Não foi possível realizar a verificação antifraude."
            " Tente novamente ou contate um de nossos canas de atendimento.");
      }
    } catch (e) {
      SecureLogger.error('DeviceAuthorizerViewmodel: Erro ao enviar OTP', e);
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  @override
  Future<void> validaOtp(String otp) async {
    try {
      SecureLogger.debug(
          'DeviceAuthorizerViewmodel: Validando OTP para CPF: $cpf');
      _isLoading = true;
      notifyListeners();

      // ✅ MELHORADO: Valida se a autorização foi bem-sucedida
      final success = await _topazRepository.authorize(cpf, otp);

      if (success) {
        _validated = true;
        SecureLogger.debug(
            'DeviceAuthorizerViewmodel: OTP validado com sucesso');
      } else {
        throw const BusinessException(
          "Código inválido. Verifique e tente novamente.",
        );
      }
    } catch (e) {
      SecureLogger.error('DeviceAuthorizerViewmodel: Erro ao validar OTP', e);
      _validated = false;
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
