import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/model/data/account/transactional_password.data.dart';
import 'package:j17_bank_mybank_mobile/model/repository/account/transactional_password_repository.dart';
import 'package:j17_bank_mybank_mobile/model/exception/business_exception.dart';
import 'package:dio/dio.dart';

class TransactionalPasswordStatusViewModel extends ChangeNotifier {
  final TransactionalPasswordRepository _repository =
      TransactionalPasswordRepository();

  TransactionalPasswordStatusResponse? _passwordStatus;
  bool _isLoading = false;
  String? _error;

  TransactionalPasswordStatusResponse? get passwordStatus => _passwordStatus;
  bool get isLoading => _isLoading;
  String? get error => _error;

  bool get isPasswordEnabled => _passwordStatus?.isPasswordEnabled ?? false;
  bool get isPasswordNotFound => _passwordStatus?.isPasswordNotFound ?? false;
  bool get isPasswordBlocked => _passwordStatus?.isPasswordBlocked ?? false;
  bool get requiresReRegistration =>
      _passwordStatus?.requiresReRegistration ?? false;

  Future<void> loadPasswordStatus() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _passwordStatus = await _repository.getTransactionalPasswordStatus();
    } on DioException catch (e) {
      if (e.response?.statusCode == 502) {
        _error =
            'Serviço temporariamente indisponível. Tente novamente em alguns instantes.';
      } else if (e.response?.statusCode == 401) {
        _error = 'Sessão expirada. Faça login novamente.';
      } else if (e.response?.statusCode == 403) {
        _error = 'Acesso negado. Verifique suas permissões.';
      } else if (e.response?.statusCode == 404) {
        _error = 'Serviço não encontrado.';
      } else if (e.response?.statusCode == 500) {
        _error = 'Erro interno do servidor. Tente novamente mais tarde.';
      } else {
        _error = 'Erro de conexão. Verifique sua internet e tente novamente.';
      }
    } on BusinessException catch (e) {
      _error = e.message ?? 'Erro de negócio. Tente novamente.';
    } catch (e) {
      _error = 'Erro inesperado. Tente novamente.';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
}
