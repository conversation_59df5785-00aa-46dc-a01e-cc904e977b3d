import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:j17_bank_mybank_mobile/utils/services/shared_preferences_service.dart';
import 'package:j17_bank_mybank_mobile/utils/secure_logger.dart';

/// Serviço centralizado para armazenamento seguro.
/// Utiliza FlutterSecureStorage com fallback opcional em SharedPreferences (prefixo 'secure_').
///
/// IMPORTANTE: Configurado para **NÃO** persistir dados após reinstalação do app.
/// ✅ CORREÇÃO ESPECÍFICA: Força limpeza no iOS Simulator onde o Keychain não é limpo automaticamente
class SecureStorageService {
  static final FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: const AndroidOptions(
      encryptedSharedPreferences: true,
      // ✅ CONFIGURAÇÃO CORRETA: Remove dados na desinstalação no Android
      sharedPreferencesName: 'J17BankSecureStorage',
      preferencesKeyPrefix: 'j17_',
      resetOnError: true,
    ),
    iOptions: IOSOptions(
      // ✅ CORRIGIDO: Configuração que remove dados na desinstalação no iOS
      accessibility: KeychainAccessibility.first_unlock_this_device,
      synchronizable: false,
      // ✅ CORRIGIDO: Usar accountName específico do app para garantir remoção na desinstalação
      accountName: 'J17Bank',
    ),
  );

  static const String _securePrefix = 'secure_';

  static FlutterSecureStorage get storage => _secureStorage;

  /// ✅ NOVA FUNÇÃO: Verifica se está rodando no iOS Simulator
  /// O iOS Simulator não limpa o Keychain automaticamente na desinstalação
  static bool get _isIOSSimulator {
    return Platform.isIOS && !kReleaseMode;
  }

  /// ✅ NOVA FUNÇÃO: Força limpeza completa no iOS Simulator
  /// Remove TODOS os dados do Keychain e SharedPreferences
  static Future<void> _forceCleanupForIOSSimulator() async {
    if (!_isIOSSimulator) return;

    try {
      SecureLogger.debug(
          '🔍 iOS Simulator detectado - forçando limpeza completa...');

      // ✅ LIMPEZA COMPLETA: Remove TODOS os dados do Keychain
      await _secureStorage.deleteAll();

      // ✅ LIMPEZA COMPLETA: Remove TODOS os dados do SharedPreferences
      await SharedPrefsService.clearAll(prefix: _securePrefix);
      await SharedPrefsService.clearAll(); // Remove também dados sem prefixo

      // ✅ LIMPEZA COMPLETA: Remove dados específicos do app
      await _secureStorage.delete(key: 'SAVED_CPF');
      await _secureStorage.delete(key: 'SAVED_CLIENT');
      await _secureStorage.delete(key: 'BIOMETRY_INFO');
      await _secureStorage.delete(key: 'biometryEnabled');
      await _secureStorage.delete(key: 'DEVICE_INFO');
      await _secureStorage.delete(key: 'FIRST_INSTALL_FLAG');

      SecureLogger.debug('✅ Limpeza completa no iOS Simulator concluída');
      SecureLogger.debug(
          '✅ TODOS os dados foram removidos do Keychain e SharedPreferences');
    } catch (e) {
      SecureLogger.error('❌ Erro na limpeza do iOS Simulator: ', e);
    }
  }

  /// ✅ NOVA FUNÇÃO: Força limpeza manual no iOS Simulator
  /// Útil para testes ou quando o usuário quer limpar todos os dados
  static Future<void> forceCleanupForSimulator() async {
    if (!_isIOSSimulator) {
      SecureLogger.debug('🔍 Não é iOS Simulator - limpeza não necessária');
      return;
    }

    SecureLogger.debug('🔍 Forçando limpeza manual no iOS Simulator...');
    await _forceCleanupForIOSSimulator();
  }

  static Future<void> write(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
      developer.log('✅ SecureStorage: Salvou $key');
    } catch (e) {
      developer.log('❌ SecureStorage: Erro ao salvar $key - $e');
      await SharedPrefsService.save<String>(key, value, prefix: _securePrefix);
    }
  }

  static Future<String?> read(String key) async {
    try {
      final value = await _secureStorage.read(key: key);
      if (value != null && value.isNotEmpty) {
        developer.log('✅ SecureStorage: Leu $key');
        return value;
      }
      return await SharedPrefsService.read<String>(key, prefix: _securePrefix);
    } catch (e) {
      developer.log('❌ SecureStorage: Erro ao ler $key - $e');
      return await SharedPrefsService.read<String>(key, prefix: _securePrefix);
    }
  }

  static Future<void> delete(String key) async {
    try {
      await _secureStorage.delete(key: key);
      developer.log('✅ SecureStorage: Removeu $key');
    } catch (e) {
      developer.log('❌ SecureStorage: Erro ao remover $key - $e');
    }

    await SharedPrefsService.delete(key, prefix: _securePrefix);
  }

  static Future<bool> containsKey(String key) async {
    try {
      final exists = await _secureStorage.containsKey(key: key);
      if (exists) {
        developer.log('✅ SecureStorage: Chave $key existe');
        return true;
      }
      return await SharedPrefsService.containsKey(key, prefix: _securePrefix);
    } catch (e) {
      developer.log('❌ SecureStorage: Erro containsKey $key - $e');
      return await SharedPrefsService.containsKey(key, prefix: _securePrefix);
    }
  }

  static Future<void> deleteAll() async {
    try {
      await _secureStorage.deleteAll();
      developer.log('✅ SecureStorage: Todos os dados removidos');
    } catch (e) {
      developer.log('❌ SecureStorage: Erro ao limpar - $e');
    }

    await SharedPrefsService.clearAll(prefix: _securePrefix);
  }

  static Future<Map<String, String>> readAll() async {
    final Map<String, String> allData = {};

    try {
      final secureData = await _secureStorage.readAll();
      allData.addAll(secureData);
      developer.log('✅ SecureStorage: Lidos ${secureData.length} dados');
    } catch (e) {
      developer.log('❌ SecureStorage: Erro ao ler todos - $e');
    }

    try {
      final sharedData =
          await SharedPrefsService.readAll(prefix: _securePrefix);
      sharedData.forEach((key, value) {
        if (value is String && !allData.containsKey(key)) {
          allData[key] = value;
        }
      });
      developer.log('✅ SharedPrefs fallback: +${sharedData.length} dados');
    } catch (e) {
      developer.log('❌ SharedPrefs fallback: Erro ao ler todos - $e');
    }

    return allData;
  }

  static Future<void> migrateToSharedPreferences() async {
    try {
      final secureData = await _secureStorage.readAll();
      for (final entry in secureData.entries) {
        await SharedPrefsService.save<String>(entry.key, entry.value,
            prefix: _securePrefix);
      }

      developer.log(
          '✅ SecureStorage: ${secureData.length} dados migrados para SharedPrefs');
    } catch (e) {
      developer.log('❌ SecureStorage: Erro na migração - $e');
    }
  }

  static Future<Map<String, dynamic>> getStorageInfo() async {
    final Map<String, dynamic> info = {
      'platform': Platform.operatingSystem,
      'secure_storage_available': false,
      'shared_preferences_available': false,
      'secure_storage_entries': 0,
      'shared_preferences_entries': 0,
    };

    try {
      final secureData = await _secureStorage.readAll();
      info['secure_storage_available'] = true;
      info['secure_storage_entries'] = secureData.length;
    } catch (e) {
      info['secure_storage_error'] = e.toString();
    }

    try {
      final sharedData =
          await SharedPrefsService.readAll(prefix: _securePrefix);
      info['shared_preferences_available'] = true;
      info['shared_preferences_entries'] = sharedData.length;
    } catch (e) {
      info['shared_preferences_error'] = e.toString();
    }

    return info;
  }

  /// ✅ MELHORADA: Verifica se é primeira instalação e limpa TODOS os dados
  /// Garante que dados de instalações anteriores sejam completamente removidos
  /// ✅ CORREÇÃO ESPECÍFICA: Força limpeza no iOS Simulator onde o Keychain não é limpo automaticamente
  static Future<void> checkFirstInstallationAndCleanup() async {
    try {
      // ✅ CORREÇÃO: NÃO força limpeza no iOS Simulator para testar persistência
      if (_isIOSSimulator) {
        SecureLogger.debug(
            '🔍 iOS Simulator detectado - PULANDO limpeza forçada para testar persistência...');
        // await _forceCleanupForIOSSimulator(); // DESABILITADO para teste
        // return; // DESABILITADO para teste
      }

      final isFirstInstall =
          await _secureStorage.read(key: 'FIRST_INSTALL_FLAG') == null;

      if (isFirstInstall) {
        SecureLogger.debug(
            '🔍 Primeira instalação detectada - executando limpeza completa...');

        // ✅ LIMPEZA COMPLETA: Remove TODOS os dados do secure storage
        await _secureStorage.deleteAll();

        // ✅ LIMPEZA COMPLETA: Remove TODOS os dados do SharedPreferences
        await SharedPrefsService.clearAll(prefix: _securePrefix);

        // ✅ LIMPEZA COMPLETA: Remove também dados sem prefixo (fallback)
        await SharedPrefsService.clearAll();

        // Marca que já foi executada a limpeza
        await _secureStorage.write(key: 'FIRST_INSTALL_FLAG', value: 'true');

        SecureLogger.debug(
            '✅ Limpeza completa de primeira instalação concluída');
        SecureLogger.debug(
            '✅ TODOS os dados de instalações anteriores foram removidos');
      } else {
        SecureLogger.debug('✅ App já foi inicializado anteriormente');

        // ✅ NOVO: Verifica dados residuais mesmo em instalações subsequentes
        await forceCleanupResidualData();
      }
    } catch (e) {
      SecureLogger.error('❌ Erro na verificação de primeira instalação: ', e);
    }
  }

  /// ✅ CORRIGIDO: Força limpeza de dados residuais do Keychain iOS
  /// Útil para casos onde o Keychain não remove dados automaticamente na desinstalação
  /// ✅ CORREÇÃO: NÃO remove SAVED_CPF pois pode ser um dado válido do usuário
  static Future<void> forceCleanupResidualData() async {
    try {
      SecureLogger.debug('🔍 Verificando dados residuais no Keychain...');

      // Verifica se há dados que não deveriam estar lá
      final allData = await _secureStorage.readAll();

      // ✅ CORREÇÃO: NÃO considera SAVED_CPF ou BIOMETRY_INFO como residuais
      // O CPF pode ser um dado válido se o usuário ativou "Lembrar meus dados"
      // A biometria pode existir independentemente do cliente salvo
      final hasResidualData =
          allData.isNotEmpty && allData.containsKey('SAVED_CLIENT');

      if (hasResidualData) {
        SecureLogger.debug(
            '🔍 Dados residuais detectados - executando limpeza forçada...');

        // ✅ CORREÇÃO: Remove apenas dados que são realmente residuais
        // NÃO remove SAVED_CPF nem BIOMETRY_INFO pois podem ser dados válidos
        await _secureStorage.delete(key: 'SAVED_CLIENT');
        await _secureStorage.delete(key: 'DEVICE_INFO');

        SecureLogger.debug('✅ Limpeza forçada de dados residuais concluída');
        SecureLogger.debug('✅ CPF salvo (SAVED_CPF) foi preservado');
      } else {
        SecureLogger.debug('✅ Nenhum dado residual detectado');
      }
    } catch (e) {
      SecureLogger.error('❌ Erro na limpeza forçada: ', e);
    }
  }

  /// ✅ NOVA FUNÇÃO: Debug do status do storage
  static Future<void> debugStorageStatus() async {
    try {
      final allData = await _secureStorage.readAll();
      SecureLogger.debug(
          '🔍 Dados no SecureStorage: ${allData.length} entradas');

      for (final entry in allData.entries) {
        SecureLogger.debug('  - Chave: ${entry.key}');
      }

      final sharedData =
          await SharedPrefsService.readAll(prefix: _securePrefix);
      SecureLogger.debug(
          '🔍 Dados no SharedPreferences: ${sharedData.length} entradas');

      for (final entry in sharedData.entries) {
        SecureLogger.debug('  - Chave: ${entry.key}');
      }
    } catch (e) {
      SecureLogger.error('❌ Erro ao debugar storage: ', e);
    }
  }
}

/// Chaves constantes usadas no armazenamento seguro
class Keys {
  static const String savedCpf = 'SAVED_CPF';
  static const String savedClient = 'SAVED_CLIENT';
  static const String biometryEnabled = 'biometryEnabled';
  static const String biometryInfo = 'BIOMETRY_INFO';
}
