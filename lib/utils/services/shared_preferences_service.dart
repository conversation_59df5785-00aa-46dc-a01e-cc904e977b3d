import 'package:shared_preferences/shared_preferences.dart';
import 'dart:developer' as developer;

/// Serviço centralizado para manipulação de SharedPreferences
/// Suporta valores String, bool, int e double.
/// Utiliza prefixo opcional para organização de chaves.
class SharedPrefsService {
  static const String _defaultPrefix = 'j17_';

  /// Salva um valor no SharedPreferences
  static Future<void> save<T>(String key, T value, {String? prefix}) async {
    final prefs = await SharedPreferences.getInstance();
    final fullKey = '${prefix ?? _defaultPrefix}$key';

    try {
      if (value is String) {
        await prefs.setString(fullKey, value);
      } else if (value is bool) {
        await prefs.setBool(fullKey, value);
      } else if (value is int) {
        await prefs.setInt(fullKey, value);
      } else if (value is double) {
        await prefs.setDouble(fullKey, value);
      } else {
        throw UnsupportedError('Tipo não suportado: ${value.runtimeType}');
      }

      developer.log('✅ SharedPrefs: Salvou $key = $value');
    } catch (e) {
      developer.log('❌ SharedPrefs: Erro ao salvar $key - $e');
    }
  }

  /// Lê um valor (String, bool, int ou double)
  static Future<T?> read<T>(String key, {String? prefix}) async {
    final prefs = await SharedPreferences.getInstance();
    final fullKey = '${prefix ?? _defaultPrefix}$key';

    try {
      final value = prefs.get(fullKey);
      if (value is T) {
        developer.log('✅ SharedPrefs: Leu $key = $value');
        return value;
      } else {
        return null;
      }
    } catch (e) {
      developer.log('❌ SharedPrefs: Erro ao ler $key - $e');
      return null;
    }
  }

  /// Remove um valor
  static Future<void> delete(String key, {String? prefix}) async {
    final prefs = await SharedPreferences.getInstance();
    final fullKey = '${prefix ?? _defaultPrefix}$key';

    try {
      await prefs.remove(fullKey);
      developer.log('✅ SharedPrefs: Removeu $key');
    } catch (e) {
      developer.log('❌ SharedPrefs: Erro ao remover $key - $e');
    }
  }

  /// Verifica se existe uma chave
  static Future<bool> containsKey(String key, {String? prefix}) async {
    final prefs = await SharedPreferences.getInstance();
    final fullKey = '${prefix ?? _defaultPrefix}$key';

    try {
      final exists = prefs.containsKey(fullKey);
      developer.log('✅ SharedPrefs: $key ${exists ? 'existe' : 'não existe'}');
      return exists;
    } catch (e) {
      developer.log('❌ SharedPrefs: Erro containsKey $key - $e');
      return false;
    }
  }

  /// Remove todas as chaves com determinado prefixo
  static Future<void> clearAll({String? prefix}) async {
    final prefs = await SharedPreferences.getInstance();
    final currentPrefix = prefix ?? _defaultPrefix;

    try {
      final keysToRemove = prefs
          .getKeys()
          .where((key) => key.startsWith(currentPrefix));

      for (final key in keysToRemove) {
        await prefs.remove(key);
      }

      developer.log('✅ SharedPrefs: Limpou ${keysToRemove.length} entradas');
    } catch (e) {
      developer.log('❌ SharedPrefs: Erro ao limpar - $e');
    }
  }

  /// Lê todas as chaves com determinado prefixo
  static Future<Map<String, Object>> readAll({String? prefix}) async {
    final prefs = await SharedPreferences.getInstance();
    final currentPrefix = prefix ?? _defaultPrefix;
    final Map<String, Object> result = {};

    try {
      final keys = prefs.getKeys().where((k) => k.startsWith(currentPrefix));
      for (final key in keys) {
        final value = prefs.get(key);
        if (value != null) {
          final originalKey = key.replaceFirst(currentPrefix, '');
          result[originalKey] = value;
        }
      }

      developer.log('✅ SharedPrefs: Lidos ${result.length} itens');
    } catch (e) {
      developer.log('❌ SharedPrefs: Erro ao ler todos - $e');
    }

    return result;
  }
}
