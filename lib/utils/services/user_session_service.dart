import 'package:j17_bank_mybank_mobile/utils/services/secure_storage_service.dart';

class UserSessionService {
  static const String _idContaKey = 'user_id_conta';
  static const String _userIdKey = 'user_id';
  static const String _userNameKey = 'user_name';

  static final UserSessionService _instance = UserSessionService._internal();
  factory UserSessionService() => _instance;
  UserSessionService._internal();

  // Cache em memória para melhor performance
  String? _cachedIdConta;
  String? _cachedUserId;
  String? _cachedUserName;

  /// Obtém o idConta do usuário logado
  Future<String> getIdConta() async {
    // Retorna cache se disponível
    if (_cachedIdConta != null) {
      return _cachedIdConta!;
    }

    try {
      final idConta = await SecureStorageService.read(_idContaKey);
      if (idConta != null && idConta.isNotEmpty) {
        _cachedIdConta = idConta;
        return idConta;
      }

      // ✅ CORRIGIDO: Retorna string vazia em vez de valor de exemplo
      return '';
    } catch (e) {
      // ✅ CORRIGIDO: Retorna string vazia em vez de valor de exemplo
      return '';
    }
  }

  /// Obtém o ID do usuário logado
  Future<String> getUserId() async {
    if (_cachedUserId != null) {
      return _cachedUserId!;
    }

    try {
      final userId = await SecureStorageService.read(_userIdKey);
      if (userId != null && userId.isNotEmpty) {
        _cachedUserId = userId;
        return userId;
      }
      return '';
    } catch (e) {
      return '';
    }
  }

  /// Obtém o nome do usuário logado
  Future<String> getUserName() async {
    if (_cachedUserName != null) {
      return _cachedUserName!;
    }

    try {
      final userName = await SecureStorageService.read(_userNameKey);
      if (userName != null && userName.isNotEmpty) {
        _cachedUserName = userName;
        return userName;
      }
      return '';
    } catch (e) {
      return '';
    }
  }

  /// Define o idConta do usuário
  Future<void> setIdConta(String idConta) async {
    await SecureStorageService.write(_idContaKey, idConta);
    _cachedIdConta = idConta;
  }

  /// Define o ID do usuário
  Future<void> setUserId(String userId) async {
    await SecureStorageService.write(_userIdKey, userId);
    _cachedUserId = userId;
  }

  /// Define o nome do usuário
  Future<void> setUserName(String userName) async {
    await SecureStorageService.write(_userNameKey, userName);
    _cachedUserName = userName;
  }

  /// Limpa os dados da sessão
  Future<void> clearSession() async {
    await SecureStorageService.delete(_idContaKey);
    await SecureStorageService.delete(_userIdKey);
    await SecureStorageService.delete(_userNameKey);

    _cachedIdConta = null;
    _cachedUserId = null;
    _cachedUserName = null;
  }

  /// Verifica se o usuário está logado
  Future<bool> isLoggedIn() async {
    try {
      final idConta = await getIdConta();
      return idConta.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// ✅ REMOVIDO: Fallback para desenvolvimento/teste
  /// Agora retorna string vazia quando não há ID salvo

  /// Atualiza os dados da sessão (útil após login)
  Future<void> updateSession({
    required String idConta,
    required String userId,
    required String userName,
  }) async {
    await setIdConta(idConta);
    await setUserId(userId);
    await setUserName(userName);
  }
}
