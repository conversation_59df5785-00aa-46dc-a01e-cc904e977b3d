import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/utils/secure_logger.dart';
import 'package:j17_bank_mybank_mobile/utils/logout_service.dart';
import 'package:j17_bank_mybank_mobile/view/pages/access/sigin_page.dart';
import 'package:j17_bank_mybank_mobile/view/components/customSnackBar/custom_snack_bar.dart';
import 'package:j17_bank_mybank_mobile/constants/icon_constants.dart';
import 'package:j17_bank_mybank_mobile/constants/colors_constants.dart';

/// 🔒 SERVICE PARA GERENCIAR TIMEOUT DE SESSÃO
/// Implementa logout automático após período de inatividade
///
/// ✅ COMPORTAMENTO PADRÃO DE APPS BANCÁRIOS:
/// - Timeout de 5 minutos de inatividade
/// - Logout automático com redirecionamento
/// - Mensagem informativa para o usuário
/// - Segurança bancária mantida
class SessionTimeoutService {
  static final SessionTimeoutService _instance =
      SessionTimeoutService._internal();
  factory SessionTimeoutService() => _instance;
  SessionTimeoutService._internal();

  // ✅ CONFIGURAÇÃO DE TIMEOUT
  static const Duration _timeoutDuration =
      Duration(minutes: 5); // 5 minutos de inatividade
  static const Duration _warningDuration =
      Duration(minutes: 4); // Aviso 1 minuto antes

  Timer? _timeoutTimer;
  Timer? _warningTimer;
  DateTime? _lastActivity;
  bool _isLoggedIn = false;
  BuildContext? _currentContext;
  bool _isWarningShown = false; // ✅ NOVO: Controla se o aviso está visível

  /// ✅ Inicia o monitoramento de timeout de sessão
  /// Deve ser chamado após login bem-sucedido
  void startSessionMonitoring(BuildContext context) {
    try {
      _currentContext = context;
      _isLoggedIn = true;
      _resetTimers();

      SecureLogger.debug('🔒 SessionTimeoutService: Monitoramento iniciado');
      SecureLogger.debug('  - Timeout: $_timeoutDuration');
      SecureLogger.debug('  - Warning: $_warningDuration');

      if (Platform.isIOS) {
        SecureLogger.debug('🔍 iOS: SessionTimeoutService iniciado');
      }
    } catch (e) {
      SecureLogger.error('❌ Erro ao iniciar monitoramento de sessão: ', e);
    }
  }

  /// ✅ Para o monitoramento de timeout de sessão
  /// Deve ser chamado no logout ou quando app é fechado
  void stopSessionMonitoring() {
    try {
      _timeoutTimer?.cancel();
      _warningTimer?.cancel();

      // ✅ NOVO: Fecha o aviso se estiver visível
      if (_isWarningShown) {
        _hideWarning();
      }

      _isLoggedIn = false;
      _currentContext = null;
      _isWarningShown = false; // ✅ NOVO: Reseta flag do aviso

      SecureLogger.debug('🔒 SessionTimeoutService: Monitoramento parado');

      if (Platform.isIOS) {
        SecureLogger.debug('🔍 iOS: SessionTimeoutService parado');
      }
    } catch (e) {
      SecureLogger.error('❌ Erro ao parar monitoramento de sessão: ', e);
    }
  }

  /// ✅ Registra atividade do usuário
  /// Deve ser chamado em qualquer interação do usuário
  void registerUserActivity() {
    try {
      if (!_isLoggedIn) return;

      _lastActivity = DateTime.now();

      // ✅ NOVO: Fecha o aviso se estiver visível
      if (_isWarningShown) {
        _hideWarning();
      }

      _resetTimers();

      // ✅ OTIMIZADO: Remove logs excessivos para melhor performance
      // SecureLogger.debug('🔒 SessionTimeoutService: Atividade registrada');
      // SecureLogger.debug('  - Última atividade: $_lastActivity');
    } catch (e) {
      SecureLogger.error('❌ Erro ao registrar atividade: ', e);
    }
  }

  /// ✅ Reseta os timers de timeout
  void _resetTimers() {
    try {
      // Cancela timers existentes
      _timeoutTimer?.cancel();
      _warningTimer?.cancel();

      if (!_isLoggedIn) return;

      // Timer de aviso (1 minuto antes do timeout)
      _warningTimer = Timer(_warningDuration, _showWarning);

      // Timer de timeout (logout automático)
      _timeoutTimer = Timer(_timeoutDuration, _performAutoLogout);

      // ✅ OTIMIZADO: Remove logs excessivos para melhor performance
      // SecureLogger.debug('🔒 SessionTimeoutService: Timers resetados');
    } catch (e) {
      SecureLogger.error('❌ Erro ao resetar timers: ', e);
    }
  }

  /// ✅ Mostra aviso de timeout próximo
  void _showWarning() {
    try {
      if (!_isLoggedIn || _currentContext == null) return;

      SecureLogger.debug(
          '🔒 SessionTimeoutService: Mostrando aviso de timeout');

      // ✅ CORREÇÃO: Usa CustomSnackBar com mensagem conforme anexo
      CustomSnackBar.show(
        _currentContext!,
        'Por motivo de segurança, sua sessão será encerrada em 1 minuto devido à inatividade. Toque na tela para mantê-la ativa.',
        backgroundColor: J17ThemeColor.surfacesBgSnackBar,
        leadingIcon: J17Icons.tickCircleCustom,
        leadingIconColor: J17ThemeColor.textSnackBar,
        duration: const Duration(seconds: 10),
      );

      _isWarningShown = true; // ✅ NOVO: Marca que o aviso está visível

      if (Platform.isIOS) {
        SecureLogger.debug('🔍 iOS: Aviso de timeout mostrado');
      }
    } catch (e) {
      SecureLogger.error('❌ Erro ao mostrar aviso: ', e);
    }
  }

  /// ✅ NOVO: Fecha o aviso de timeout
  void _hideWarning() {
    try {
      if (!_isLoggedIn || _currentContext == null || !_isWarningShown) return;

      SecureLogger.debug('🔒 SessionTimeoutService: Fechando aviso de timeout');

      // ✅ Fecha o SnackBar atual
      final scaffoldMessenger = ScaffoldMessenger.maybeOf(_currentContext!);
      if (scaffoldMessenger != null) {
        scaffoldMessenger.hideCurrentSnackBar();
      }

      _isWarningShown = false; // ✅ Marca que o aviso foi fechado

      if (Platform.isIOS) {
        SecureLogger.debug('🔍 iOS: Aviso de timeout fechado');
      }
    } catch (e) {
      SecureLogger.error('❌ Erro ao fechar aviso: ', e);
    }
  }

  /// ✅ Executa logout automático por timeout
  void _performAutoLogout() {
    try {
      if (!_isLoggedIn || _currentContext == null) return;

      SecureLogger.debug(
          '🔒 SessionTimeoutService: Executando logout automático por timeout');

      // ✅ CORREÇÃO: Usa CustomSnackBar para mensagem de logout
      CustomSnackBar.show(
        _currentContext!,
        'Sessão expirada por inatividade. Faça login novamente.',
        backgroundColor: J17ThemeColor.surfacesBgSnackBar,
        textColor: J17ThemeColor.textSnackBar,
        leadingIcon: J17Icons.warning2,
        leadingIconColor: J17ThemeColor.textSnackBar,
        duration: const Duration(seconds: 5),
      );

      // Executa logout automático
      _executeLogout();

      if (Platform.isIOS) {
        SecureLogger.debug('🔍 iOS: Logout automático executado');
      }
    } catch (e) {
      SecureLogger.error('❌ Erro ao executar logout automático: ', e);
    }
  }

  /// ✅ Executa o logout e redirecionamento
  void _executeLogout() async {
    try {
      if (_currentContext == null) return;

      // Para o monitoramento
      stopSessionMonitoring();

      // Executa logout via LogoutService
      await LogoutService().performLogout(_currentContext!);

      SecureLogger.debug(
          '🔒 SessionTimeoutService: Logout automático concluído');
    } catch (e) {
      SecureLogger.error('❌ Erro ao executar logout: ', e);

      // Fallback: tenta redirecionar para login
      if (_currentContext != null) {
        try {
          Navigator.of(_currentContext!, rootNavigator: true)
              .pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const SignInPage()),
            (route) => false,
          );
        } catch (e) {
          SecureLogger.error('❌ Erro no fallback de redirecionamento: ', e);
        }
      }
    }
  }

  /// ✅ Verifica se o usuário está logado
  bool get isLoggedIn => _isLoggedIn;

  /// ✅ Obtém o tempo restante até o timeout
  Duration? get remainingTime {
    if (_lastActivity == null || !_isLoggedIn) return null;

    final elapsed = DateTime.now().difference(_lastActivity!);
    final remaining = _timeoutDuration - elapsed;

    return remaining.isNegative ? Duration.zero : remaining;
  }

  /// ✅ Cleanup para dispose
  void dispose() {
    stopSessionMonitoring();
  }
}

/// 🔒 MIXIN PARA WIDGETS QUE PRECISAM REGISTRAR ATIVIDADE
/// ✅ OTIMIZADO: Usa debounce para evitar chamadas excessivas
mixin SessionActivityMixin<T extends StatefulWidget> on State<T> {
  Timer? _debounceTimer;

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _registerActivityWithDebounce();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _registerActivityWithDebounce();
  }

  void _registerActivityWithDebounce() {
    // ✅ OTIMIZADO: Usa debounce de 200ms para evitar chamadas excessivas
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 200), () {
      try {
        SessionTimeoutService().registerUserActivity();
      } catch (e) {
        SecureLogger.error('❌ Erro ao registrar atividade: ', e);
      }
    });
  }
}

/// 🔒 WIDGET WRAPPER PARA REGISTRAR ATIVIDADE EM GESTOS
/// ✅ OTIMIZADO: Usa debounce para evitar chamadas excessivas
class SessionActivityDetector extends StatefulWidget {
  final Widget child;

  const SessionActivityDetector({
    super.key,
    required this.child,
  });

  @override
  State<SessionActivityDetector> createState() =>
      _SessionActivityDetectorState();
}

class _SessionActivityDetectorState extends State<SessionActivityDetector> {
  Timer? _debounceTimer;

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _registerActivityWithDebounce() {
    // ✅ OTIMIZADO: Usa debounce de 100ms para evitar chamadas excessivas
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 100), () {
      SessionTimeoutService().registerUserActivity();
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _registerActivityWithDebounce,
      onScaleUpdate: (_) => _registerActivityWithDebounce(),
      behavior: HitTestBehavior
          .translucent, // ✅ Permite que eventos passem para widgets filhos
      child: widget.child,
    );
  }
}
