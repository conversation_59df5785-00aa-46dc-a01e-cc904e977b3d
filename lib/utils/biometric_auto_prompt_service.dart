import 'dart:io';
import 'package:j17_bank_mybank_mobile/utils/secure_logger.dart';
import 'package:j17_bank_mybank_mobile/model/repository/biometry_repository.dart';

/// 🔐 SERVICE PARA GERENCIAR FLAG DE BIOMETRIA AUTOMÁTICA
/// Controla quando a biometria deve ser solicitada automaticamente
///
/// ✅ REGRAS IMPORTANTES CONFORME TABELA:
/// - Biometria automática APENAS quando app é completamente fechado e reaberto
/// - Ap<PERSON> logout, biometria deve ser manual (clicar no botão) mas dados NÃO limpos
/// - Flag é resetada quando app é fechado (AppLifecycleState.detached) OU após logout
/// - Primeira instalação: biometria NÃO automática, dados limpos
class BiometricAutoPromptService {
  static final BiometricAutoPromptService _instance =
      BiometricAutoPromptService._internal();
  factory BiometricAutoPromptService() => _instance;
  BiometricAutoPromptService._internal();

  // ✅ Flag estática que controla se a biometria já foi acionada automaticamente
  // Esta flag é resetada quando o app é completamente fechado e reaberto OU após logout
  // Isso garante que após logout a biometria seja manual (clicar no botão)
  static bool _biometricAutoPrompted = false;

  // ✅ NOVA: Flag para controlar se estamos após logout
  // Após logout, biometria deve ser manual (não automática)
  static bool _isAfterLogout = false;

  /// ✅ Verifica se a biometria automática já foi acionada nesta sessão
  /// Esta flag é resetada após logout OU quando app é fechado
  bool get isAutoPrompted => _biometricAutoPrompted;

  /// ✅ Marca que a biometria automática foi acionada
  /// Após isso, biometria só será automática novamente quando app for fechado e reaberto
  void markAsPrompted() {
    _biometricAutoPrompted = true;
    SecureLogger.debug('🔐 Biometria automática marcada como acionada');
    if (Platform.isIOS) {
      SecureLogger.debug('🔍 iOS: Flag _biometricAutoPrompted = true');
    }
  }

  /// ✅ Reseta a flag para permitir biometria automática no próximo acesso
  /// Chamado quando o app é completamente fechado (AppLifecycleState.detached) OU após logout
  /// Isso garante que após logout a biometria seja manual (clicar no botão)
  void resetAutoPromptFlag() {
    _biometricAutoPrompted = false;
    SecureLogger.debug(
        '🔐 Flag de biometria automática resetada (app fechado ou logout)');
    if (Platform.isIOS) {
      SecureLogger.debug(
          '🔍 iOS: Flag _biometricAutoPrompted resetada para false');
    }
  }

  /// ✅ NOVO: Marca que estamos após logout
  /// Após logout, biometria deve ser manual (não automática)
  void markAsAfterLogout() {
    _isAfterLogout = true;
    SecureLogger.debug('🔐 Marcado como após logout - biometria será manual');
    if (Platform.isIOS) {
      SecureLogger.debug('🔍 iOS: Flag _isAfterLogout = true');
    }
  }

  /// ✅ NOVO: Reseta flag de após logout
  /// Chamado quando app é completamente fechado e reaberto
  void resetAfterLogoutFlag() {
    _isAfterLogout = false;
    SecureLogger.debug('🔐 Flag de após logout resetada (app fechado)');
    if (Platform.isIOS) {
      SecureLogger.debug('🔍 iOS: Flag _isAfterLogout resetada para false');
    }
  }

  /// ✅ Verifica se deve acionar biometria automaticamente
  /// Retorna true APENAS se:
  /// - Biometria não foi acionada ainda nesta sessão
  /// - Biometria está habilitada
  /// - NÃO estamos após logout (após logout = biometria manual)
  /// - App foi completamente fechado e reaberto (flag resetada)
  /// - NÃO é a primeira instalação (há dados salvos)
  Future<bool> shouldAutoPromptBiometry() async {
    try {
      // ✅ CORREÇÃO: Se estamos após logout, biometria deve ser manual
      if (_isAfterLogout) {
        SecureLogger.debug(
            '🔐 Após logout - biometria manual (não automática)');
        return false;
      }

      // Se biometria já foi acionada nesta sessão, não aciona novamente
      if (_biometricAutoPrompted) {
        SecureLogger.debug('🔐 Biometria já foi acionada nesta sessão');
        return false;
      }

      // Verifica se biometria está habilitada
      final biometryRepository = BiometryRepository();
      final isBiometricEnabled =
          await biometryRepository.isBiometrySignInEnabled();

      if (!isBiometricEnabled) {
        SecureLogger.debug('🔐 Biometria não está habilitada');
        return false;
      }

      // ✅ CORREÇÃO: NÃO verifica CPF salvo para biometria automática
      // A biometria automática deve funcionar independentemente do switch "Lembrar meus dados"
      // Apenas verifica se há dados biométricos salvos
      SecureLogger.debug('🔐 Biometria automática - não verifica CPF salvo');

      // ✅ CORREÇÃO: App fechado e reaberto = biometria automática
      // Após logout = biometria manual
      SecureLogger.debug(
          '🔐 Condições atendidas - biometria automática permitida');
      SecureLogger.debug('  - Biometria habilitada: $isBiometricEnabled');
      SecureLogger.debug('  - Após logout: $_isAfterLogout');
      SecureLogger.debug('  - Já acionada: $_biometricAutoPrompted');

      return true;
    } catch (e) {
      SecureLogger.error('❌ Erro ao verificar biometria automática: ', e);
      return false;
    }
  }
}
