import 'package:flutter/material.dart';
import 'package:j17_bank_mybank_mobile/utils/capture_theme.dart';
import 'package:provider/provider.dart';

// Funções helper para constantes que dependem do tema
String getLogoReceiptPNG(BuildContext context) {
  try {
    final themeChanger =
        Provider.of<CaptureThemeChanger>(context, listen: false);
    return themeChanger.isDarkMode
        ? j17bankLogoWhitePNGPath
        : j17bankLogoBlackPNGPath;
  } catch (e) {
    return CaptureTheme.temaPadrao
        ? j17bankLogoWhitePNGPath
        : j17bankLogoBlackPNGPath;
  }
}

String getLogoReceiptSVG(BuildContext context) {
  try {
    final themeChanger =
        Provider.of<CaptureThemeChanger>(context, listen: false);
    return themeChanger.isDarkMode
        ? j17bankLogoWhiteSVGPath
        : j17bankLogoBlackSVGPath;
  } catch (e) {
    return CaptureTheme.temaPadrao
        ? j17bankLogoWhiteSVGPath
        : j17bankLogoBlackSVGPath;
  }
}

String getCheckSuccess(BuildContext context) {
  try {
    final themeChanger =
        Provider.of<CaptureThemeChanger>(context, listen: false);
    return themeChanger.isDarkMode
        ? checkSuccessDarkPath
        : checkSuccessLightPath;
  } catch (e) {
    return CaptureTheme.temaPadrao
        ? checkSuccessDarkPath
        : checkSuccessLightPath;
  }
}

String getSchedulingSuccess(BuildContext context) {
  try {
    final themeChanger =
        Provider.of<CaptureThemeChanger>(context, listen: false);
    return themeChanger.isDarkMode
        ? schedulingSuccessDark
        : schedulingSuccessLight;
  } catch (e) {
    return CaptureTheme.temaPadrao
        ? schedulingSuccessDark
        : schedulingSuccessLight;
  }
}

// Logos
const String j17bankLogoWhiteSVGPath = "assets/img/j17bank-logo-white.svg";
const String j17bankLogoBlackSVGPath = "assets/img/j17bank-logo-black.svg";
const String j17bankLogoWhitePNGPath = "assets/img/j17bank-logo-white.png";
const String j17bankLogoBlackPNGPath = "assets/img/j17bank-logo-black.png";
const String j17bankLogoBlackPDFPath = "assets/img/logo_J17_bank.pdf";

const ImageProvider j17bankLogoYellow =
    ExactAssetImage("assets/img/j17bank-logo-yellow.png");
const ImageProvider j17bankLogoGrey =
    ExactAssetImage("assets/img/j17bank-logo-grey.png");

// Temporários
const ImageProvider j17BannerCard = ExactAssetImage("assets/banner/card.png");
const ImageProvider j17BannerNews = ExactAssetImage("assets/banner/news.png");
const ImageProvider j17BannerNewsDark =
    ExactAssetImage("assets/banner/news_dark.png");
const ImageProvider j17BannerNewCardDark =
    ExactAssetImage("assets/banner/banner_new_card_dark.png");
const ImageProvider j17CardOne = ExactAssetImage("assets/img/card_J17.png");
const ImageProvider j17CardBack = ExactAssetImage("assets/img/card_back.svg");
const ImageProvider j17UserAvatar = ExactAssetImage("assets/img/avatar.png");

// Background
const ImageProvider backgroundJ17Welcome =
    ExactAssetImage("assets/img/screen_welcome.png");

// Splash Screen Bottom Logo
const ImageProvider j17IconSplashDark =
    ExactAssetImage("assets/img/J17IconSplashDark.png");
const ImageProvider j17IconSplashLight =
    ExactAssetImage("assets/img/J17IconSplashLight.png");

// Misc
const ImageProvider checkSuccessDark =
    ExactAssetImage("assets/img/check_success_dark.png");
const ImageProvider checkSuccessLight =
    ExactAssetImage("assets/img/check_success_light.png");
const String checkSuccessDarkPath = "assets/img/check_success_dark.png";
const String checkSuccessLightPath = "assets/img/check_success_light.png";
const String schedulingSuccessDark = "assets/icon/scheduling_check_dark.svg";
const String schedulingSuccessLight = "assets/icon/scheduling_check_light.svg";
const ImageProvider logo_visa = ExactAssetImage("assets/img/logo_visa.png");
const ImageProvider logo_mastercard =
    ExactAssetImage("assets/icon/logo_master.png");

// Variáveis de cor do app
class J17Colors {
  // Cores para o tema escuro exemploDark
  // Cores para o tema claro exemploLight

  // Surfaces
  static const Color surfacesElevateLight =
      Color(0xFFFFFFFF); // Support Colors White
  static const Color surfacesElevateDark = Color(0xFF313131); // Gray 800
  static const Color surfacesBackgroundLight = Color(0xFFF5F5F5); // Gray 100
  static const Color surfacesBackgroundDark = Color(0xFF181818); // Primary Gray
  static const Color surfacesBackgroundSecondaryLight =
      Color(0xFFE0E0E0); // Gray 300
  static const Color surfacesBackgroundSecondaryDark =
      Color(0xFF181818); // Primary Gray
  static const Color surfacesHighlightLight =
      Color(0xFFFFCC57); // Yellow 900 ou Primary Yellow
  static const Color surfacesHighlightDark =
      Color(0xFFFFFFFF); // Support Colors White
  static const Color surfacesBorderRadiusLight = Color(0xFF313131); // Grey 800
  static const Color surfacesBorderRadiusDark =
      Color(0xFFFFCC57); // Yellow 900 ou Primary Yellow
  static const Color surfacesFieldsLight =
      Color(0xFFFFFFFF); // Support Colors White
  static const Color surfacesFieldsDark = Color(0xFF313131); // Gray 800
  static const Color surfacesBgFlagsLight = Color(0xFFECEFF1); // Slate 50
  static const Color surfacesBgFlagsDark = Color(0xFF455A64); // Slate 700
  static const Color surfacesBgIconsSuccessLight =
      Color(0xFFDAF0E8); // Green 50
  static const Color surfacesBgIconsSuccessDark =
      Color(0xFF486856); // Green 800
  static const Color surfacesBgSnackBarLight = Color(0xFF313131); // Grey 800
  static const Color surfacesBgSnackBarDark = Color(0xFFFAFAFA); // Gray 50
  static const Color surfacesBgWarningBarLight = Color(0xFFE0E0E0); // Gray 300
  static const Color surfacesBgWarningBarDark = Color(0xFF313131); // Grey 800
  static const Color surfacesShadowLight = Color(0xFF000000); // Black
  static const Color surfacesShadowDark = Color(0xFF000000); // Black

  // Text
  static const Color textPrimaryColorLight = Color(0xFF616161); // Gray 700
  static const Color textPrimaryColorDark = Color(0xFFF5F5F5); // Gray 100
  static const Color textSecondaryColorLight = Color(0xFF757575); // Gray 600
  static const Color textSecondaryColorDark = Color(0xFFBDBDBD); // Gray 400
  static const Color textTitlesColorLight = Color(0xFF313131); // Gray 800
  static const Color textTitlesColorDark = Color(0xFFF5F5F5); // Gray 100
  static const Color textActionSecondaryColorLight =
      Color(0xFFF5F5F5); // Gray 100
  static const Color textActionSecondaryColorDark =
      Color(0xFF121212); // Gray 900
  static const Color textHighlightPrimaryColorLight =
      Color(0xFFFFCC57); // Yellow 900 ou Primary Yellow
  static const Color textHighlightPrimaryColorDark =
      Color(0xFFFFCC57); // Yellow 900 ou Primary Yellow
  static const Color textHighlightSecondaryColorLight =
      Color(0xFF616161); // Gray 700
  static const Color textHighlightSecondaryColorDark =
      Color(0xFFFFCC57); // Yellow 900 ou Primary Yellow
  static const Color textHighlightButtonPressLight =
      Color(0xFFC0A366); // Support Colors Gold
  static const Color textHighlightButtonPressDark =
      Color(0xFFF5F5F5); // Gray 100
  static const Color textAuxiliaryLight = Color(0xFFF5F5F5); // Gray 100
  static const Color textAuxiliaryDark = Color(0xFF616161); // Gray 700
  static const Color textDisabledLight = Color(0xFF9E9E9E); // Gray 500
  static const Color textDisabledDark = Color(0xFF9E9E9E); // Gray 500
  static const Color textFlagsLight = Color(0xFF546E7A); // Slate 600
  static const Color textFlagsDark = Color(0xFFF5F5F5); // Gray 100
  static const Color textIconsSuccessLight =
      Color(0xFF006414); // Support Colors Green
  static const Color textIconsSuccessDark = Color(0xFFDAF0E8); // Green 50
  static const Color textLabelButtonsLight = Color(0xFF121212); // Gray 900
  static const Color textLabelButtonsDark = Color(0xFF121212); // Gray 900
  static const Color textLogoLight = Color(0xFF313131); // Gray 800
  static const Color textLogoDark = Color(0xFFFAFAFA); // Gray 50
  static const Color textFieldPasswordLight = Color(0xFF616161); // Gray 700
  static const Color textFieldPasswordDark = Color(0xFFF5F5F5); // Gray 100
  static const Color textHighlightPricesLight =
      Color(0xFF008A1C); // Support Colors Green Highlight
  static const Color textHighlightPricesDark =
      Color(0xFFFFCC57); // Yellow 900 ou Primary Yellow
  static const Color textTextErrorsLight =
      Color(0xFFB3261E); // Support Colors Error Default
  static const Color textTextErrorsDark =
      Color(0xFFE95D55); // Support Colors Guava
  static const Color textTextWarningLight = Color(0xFF906A00); // Support Colors Orange Strong
  static const Color textTextWarningDark = Color(0xFFFFE09A); // Yellow 500
  static const Color textTextDeleteLight =
      Color(0xFFC62828); // Support Colors Red New
  static const Color textTextDeleteDark =
      Color(0xFFED5350); // Support Colors Guava New
  static const Color textSnackBarLight = Color(0xFFF5F5F5); // Gray 100
  static const Color textSnackBarDark = Color(0xFF313131); // Grey 800
  static const Color textTextSuccessLight =
      Color(0xFF008A1C); // Support Colors Green Highlight
  static const Color textTextSuccessDark =
      Color(0xFF008A1C); // Support Colors Green Highlight

  // Actions
  static const Color actionPrimaryColorLight =
      Color(0xFFFFCC57); // Yellow 900 ou Primary Yellow
  static const Color actionPrimaryColorDark =
      Color(0xFFFFCC57); // Yellow 900 ou Primary Yellow
  static const Color actionHighlightButtonPressLight =
      Color(0xFFEEEEEE); // Gray 200
  static const Color actionHighlightButtonPressDark =
      Color(0xFF616161); // Gray 700
  static const Color actionBulletsNavigationLight =
      Color(0xFFE0E0E0); // Gray 300
  static const Color actionBulletsNavigationDark =
      Color(0xFF616161); // Gray 700
  static const Color actionAuxiliaryLight = Color(0xFF313131); // Gray 800
  static const Color actionAuxiliaryDark = Color(0xFFFFFFFF); // White
  static const Color actionDisabledLight = Color(0xFFE0E0E0); // Gray 300
  static const Color actionDisabledDark = Color(0xFF616161); // Gray 700
  static const Color actionButtonTextLight =
      Color(0xFF2D67E3); // Support Colors Blue Light
  static const Color actionButtonTextDark = Color(0xFFF5F5F5); // Gray 100
  static const Color actionChipsActiveLight = Color(0xFFE0E0E0); // Gray 300
  static const Color actionChipsActiveDark = Color(0xFFFAFAFA); // Gray 50
  static const Color actionButtonDeleteLight = Color(0xFFE0E0E0); // Gray 300
  static const Color actionButtonDeleteDark =
      Color(0xFF262626); // Support Colors Guava Medium Dark

  // Flags
  // Primary Flag
  static const Color flagPrimaryBGLight = Color(0xFFECEFF1); // Slate 50
  static const Color flagPrimaryBGDark = Color(0xFF455A64); // Slate 700
  static const Color flagCanceledBGLight =
      Color(0xFFF6C3C0); //Support Colors Guava Light
  static const Color flagCanceledBGDark =
      Color(0xFFF6C3C0); //Support Colors Guava Light
  static const Color textFlagsCancelledLight =
      Color(0xFF840700); // Support Colors Red Strong
  static const Color textFlagsCancelledDark =
      Color(0xFF840700); // Support Colors Red Strong

  // Rejected Flag
  static const Color flagRejectedBGLight =
      Color(0xFF840700); // Support Colors Red Strong
  static const Color flagRejectedBGDark =
      Color(0xFF840700); // Support Colors Red Strong
  static const Color flagRejectedTextLight = Color(0xFFFFFFFF); // white
  static const Color flagRejectedTextDark = Color(0xFFFFFFFF); // white
  static const Color flagActiveBGLight = Color(0xFFDAF0E8); // Green 50
  static const Color flagActiveBGDark = Color(0xFFDAF0E8); // Green 50
  static const Color textFlagsActiveLight =
      Color(0xFF006414); // Support Colors Green
  static const Color textFlagsActiveDark =
      Color(0xFF006414); // Support Colors Green
  static const Color flagExpiredBGLight =
      Color(0xFFE0E0E0); // GreyBackground OU Grey/300
  static const Color flagExpiredBGDark =
      Color(0xFFE0E0E0); // GreyBackground OU Grey/300
  static const Color flagExpiredTextLight = Color(0xFF616161); // TextPrimary
  static const Color flagExpiredTextDark = Color(0xFF616161); // TextPrimary
  static const Color flagWaitingBGLight = Color(0xFFFFCC57); // Yellow900 (P)
  static const Color flagWaitingBGDark = Color(0xFFFFCC57); // Yellow900 (P)
  static const Color flagWaitingTextLight = Color(0xFF181818); // Primary Gray
  static const Color flagWaitingTextDark = Color(0xFF181818); // Primary Gray
  static const Color flagProcessingBGLight =
      Color(0xFF906A00); // Support Colors Orange Strong
  static const Color flagProcessingBGDark =
      Color(0xFF906A00); // Support Colors Orange Strong
  static const Color flagProcessingTextLight = Color(0xFFFFFFFF); // White
  static const Color flagProcessingTextDark = Color(0xFFFFFFFF); // White
  // Open Invoice Flag
  static const Color flagOpenInvoiceBGLight =
      Color(0xFF2D67E3); // Support Colors Blue Light
  static const Color flagOpenInvoiceBGDark =
      Color(0xFF2D67E3); // Support Colors Blue Light
  static const Color flagOpenInvoiceOutlineLight =
      Color(0xFF9EBDFF); // Support Colors Lilac
  static const Color flagOpenInvoiceOutlineDark =
      Color(0xFF9EBDFF); // Support Colors Lilac
  static const Color flagOpenInvoiceTextLight = Color(0xFFFAFAFA); // Gray50
  static const Color flagOpenInvoiceTextDark = Color(0xFFFAFAFA); // Gray50
  // Closed Invoice Flag
  static const Color flagClosedInvoiceBGLight = Color(0xFFFFFFFF); // White
  static const Color flagClosedInvoiceBGDark =
      Color(0xFF313131); // Surfaces Elevate Dark
  static const Color flagClosedInvoiceOutlineLight =
      Color(0xFF616161); // TextPrimary
  static const Color flagClosedInvoiceOutlineDark =
      Color(0xFFF5F5F5); // TextPrimary
  static const Color flagClosedInvoiceTextLight =
      Color(0xFF616161); // TextPrimary
  static const Color flagClosedInvoiceTextDark =
      Color(0xFFF5F5F5); // TextPrimary
  // Late Invoice Flag
  static const Color flagLateInvoiceBGLight =
      Color(0xFF840700); // Support Colors Red Strong
  static const Color flagLateInvoiceBGDark =
      Color(0xFF840700); // Support Colors Red Strong
  static const Color flagLateInvoiceOutlineLight =
      Color(0xFFD20B00); // Support Colors Red Light
  static const Color flagLateInvoiceOutlineDark =
      Color(0xFFD20B00); // Support Colors Red Light
  static const Color flagLateInvoiceTextLight = Color(0xFFFAFAFA); // Gray50
  static const Color flagLateInvoiceTextDark = Color(0xFFFAFAFA); // Gray50
  // Paid Invoice Flag
  static const Color flagPaidInvoiceBGLight =
      Color(0xFF006414); // Support Colors Green
  static const Color flagPaidInvoiceBGDark = Color(0xFF486856); // Green800
  static const Color flagPaidInvoiceOutlineLight =
      Color(0xFF7CB69D); // Green300
  static const Color flagPaidInvoiceOutlineDark = Color(0xFF7CB69D); // Green300
  static const Color flagPaidInvoiceTextLight = Color(0xFFFAFAFA); // Gray50
  static const Color flagPaidInvoiceTextDark = Color(0xFFFAFAFA); // Gray50
  // Processing Invoice Flag
  static const Color flagProcessingInvoiceBGLight =
      Color(0xFF906A00); // Support Colors Orange Strong
  static const Color flagProcessingInvoiceBGDark =
      Color(0xFF906A00); // Support Colors Orange Strong
  static const Color flagProcessingInvoiceOutlineLight =
      Color(0xFFFFCC57); // Yellow900 (P)
  static const Color flagProcessingInvoiceOutlineDark =
      Color(0xFFFFCC57); // Yellow900 (P)
  static const Color flagProcessingInvoiceTextLight =
      Color(0xFFFAFAFA); // Gray50
  static const Color flagProcessingInvoiceTextDark =
      Color(0xFFFAFAFA); // Gray50
  static const Color flagProcessingMonthInvoiceBGLight =
      Color(0xFFFAFAFA); // Gray50
  static const Color flagProcessingMonthInvoiceBGDark =
      Color(0xFF906A00); // Support Colors Orange Strong
  static const Color flagProcessingMonthInvoiceOutlineLight =
      Color(0xFFFFCC57); // Yellow900 (P)
  static const Color flagProcessingMonthInvoiceOutlineDark =
      Color(0xFFFFCC57); // Yellow900 (P)
  static const Color flagProcessingMonthInvoiceTextLight =
      Color(0xFF906A00); // Support Colors Orange Strong
  static const Color flagProcessingMonthInvoiceTextDark =
      Color(0xFFFAFAFA); // Gray50

  // ---------------------  FAB Colors  ---------------------
  static const Color fabBGColorLight = Color(0xFF313131); // Gray 800
  static const Color fabBGColorDark = Color(0xFFF5F5F5); // Gray 100
  static const Color fabTextColorLight = Color(0xFFF5F5F5); // Gray 100
  static const Color fabTextColorDark = Color(0xFF313131); // Gray 800

  // ---------------------  STANDARD Colors  ---------------------
  static const Color transparent = Colors.transparent;

  // Welcome Page
  static const Color welcomeBGWhite =
      Color(0xFFFAFAFA); // WhiteBGBox -> 70% opacity
  static const Color welcomeBoldText = Color(0xFF121212); // DarkerGrey
  static const Color welcomePrimaryText = Color(0xFF616161); // Grey

  // Progress Bar
  static const Color progressBarHighlight = Color(0xFF607D8B); // Slate/500
  static const Color progressBarBackground =
      Color(0xFFE0E0E0); // GreyBackground OU Grey/300

  // Switch
  static const Color switchOnThumbColorLight = Color(0xFFFFFFFF); // White
  static const Color switchOnThumbColorDark = Color(0xFFFFFFFF); // White
  static const Color switchOffThumbColorLight = Color(0xFF616161); // Gray 700
  static const Color switchOffThumbColorDark = Color(0xFF616161); // Gray 700
  static const Color switchOnTrackColorLight =
      Color(0xFFFFCC57); // Yellow 900 ou Primary Yellow
  static const Color switchOnTrackColorDark =
      Color(0xFFFFCC57); // Yellow 900 ou Primary Yellow
  static const Color switchOffTrackColorLight =
      Color(0xFFFFFFFF); // Support Colors White
  static const Color switchOffTrackColorDark = Color(0xFF313131); // Gray 800
  static const Color switchOnTrackOutlineColorLight =
      Color(0xFFFFCC57); // Yellow 900 ou Primary Yellow
  static const Color switchOnTrackOutlineColorDark =
      Color(0xFFFFCC57); // Yellow 900 ou Primary Yellow
  static const Color switchOffTrackOutlineColorLight =
      Color(0xFF616161); // Gray 700
  static const Color switchOffTrackOutlineColorDark =
      Color(0xFFF5F5F5); // Gray 100

  // WebView
  static const Color webViewBG = Color(0xFF313131); // DarkGrey
  static const Color webViewBgLight = Color(0xFFF5F5F5); // Gray 100

  // Overlay
  static const Color overlayCameraLight = Color(0xCCD9D9D9); // Gray 300
  static const Color overlayCameraDark = Color(0xCC313131); // Gray 800
  static const Color insideOverlayCameraLight = Color(0xCC868686); // Gray 600
  static const Color insideOverlayCameraDark = Color(0xCCD9D9D9); // Gray 300

  // Avatar
  static const Color avatarWhite = Color(0xFFFFFFFF);
  static const Color avatarGrey = Color(0xFFE0E0E0); // Gray 300

  // Status Dot
  static const Color statusDotSuccessOutline = Color(0xFF7CB69D); // Green 300
  static const Color statusDotSuccessCenter =
      Color(0xFF008A1C); // Support Colors Green Highlight
  static const Color statusDotErrorOutline =
      Color(0xFFD20B00); // Support Colors Red Light
  static const Color statusDotErrorCenter =
      Color(0xFF840700); // Support Colors Red Strong

  // Divider
  static const Color dividerColorLight = Color(0xFFE0E0E0); // Gray 300
  static const Color dividerColorDark = Color(0xFFE0E0E0); // Gray 300
}

// Variáveis com tamanhos de padding
class J17Padding {
  static const extraLarger = 48.0;
  static const larger = 32.0;
  static const large = 24.0;
  static const regLarge = 20.0;
  static const regular = 16.0;
  static const regSmall = 12.0;
  static const regSmaller = 10.0;
  static const small = 8.0;
  static const smaller = 6.0;
  static const smallest = 4.0;
  static const two = 2.0;
}

// Variáveis com tamanhos de borda
class J17BorderRadius {
  static const circle = 100.0;
  static const larger = 24.0;
  static const large = 20.0;
  static const regular = 16.0;
  static const regSmall = 12.0;
  static const regSmaller = 10.0;
  static const small = 8.0;
  static const smaller = 4.0;
  static const blurRadius = 3.0;
  static const spreadRadius = 0.0;
}

// Variáveis com tamanhos de ícones
class J17IconSizes {
  static const huge = 130.0;
  static const doubleRegular = 64.0;
  static const extraLarge = 50.0;
  static const large = 40.0;
  static const regular = 32.0;
  static const regLarge = 28.0;
  static const regSmall = 24.0;
  static const small = 20.0;
  static const smaller = 18.0;
  static const smallest = 16.0;
}

// Variáveis de tamanhos gerais
class J17OtherSizes {
  static const qrImageViewer = 216.0;
  static const regularMaxLines = 2;
  static const dividerThickness = 1.0;
}

// Variáveis com tamanhos de fonte definidos no figma
class J17FontSizes {
  // Display Sizes
  static const displayLarge = 57.0;
  static const displayMedium = 45.0;
  static const displaySmall = 36.0;

  // Headline Sizes
  static const headlineLarge = 32.0;
  static const headlineMedium = 28.0;
  static const headlineSmall = 24.0;

  // Title Sizes
  static const titleLarge = 24.0;
  static const titleMedLarge = 18.0;
  static const titleMedium = 16.0;
  static const titleSmall = 14.0;

  // Label Sizes
  static const labelLarge = 14.0;
  static const labelMedium = 12.0;
  static const labelSmall = 11.0;

  // Body Sizes
  static const bodyLarge = 16.0;
  static const bodyMedium = 14.0;
  static const bodySmall = 12.0;
  static const bodyMin = 10.0;
}

// Variáveis de multiplicação para alturas dos textos definidos no figma
class J17LineHeightMultiplier {
  // Display Sizes
  static const displayLarge = (64 / 57);
  static const displayMedium = (52 / 45);
  static const displaySmall = (44 / 36);

  // Headline Sizes
  static const headlineLarge = (40 / 32);
  static const headlineMedium = (36 / 28);
  static const headlineSmall = (32 / 24);

  // Title Sizes
  static const titleLarge = (28 / 24);
  static const titleMedLarge = (24 / 18);
  static const titleMedium = (24 / 16);
  static const titleSmall = (20 / 14);

  // Label Sizes
  static const labelLarge = (20 / 14);
  static const labelMedium = (16 / 12);
  static const labelSmall = (15 / 11);

  // Body Sizes
  static const bodyLarge = (24 / 16);
  static const bodyMedium = (20 / 14);
  static const bodySmall = (16 / 12);
  static const bodyMin = (14 / 10);
}

class J17LoremText {
  static const loremText =
      'Figma ipsum component variant main layer. Pen content connection bold pixel component hand object component. Style subtract share figma italic strikethrough figjam draft. Share rectangle scale italic align link share. Outline font inspect hand team asset undo main main editor. Flatten ipsum inspect bullet rectangle community vertical hand. Pixel move bullet selection bold plugin line thumbnail opacity. Move bullet vertical component share undo. Union thumbnail overflow italic overflow polygon figma. Reesizing arrange flows component effect selection union plugin. Arrange create link arrow asset pencil ipsum object background flatten. Undo duplicate ellipse opacity library prototype auto. Rotate frame reesizing line clip flatten edit. Star group community object boolean follower edit. Share slice figma image vector group blur. Mask selection ipsum invite opacity main fill layout pixel create. Edit group editor list line ipsum library style. Flows group horizontal ipsum link. Overflow group flatten figjam ellipse rotate font duplicate. Italic flatten pen effect pixel asset. Underline prototype effect move draft group team inspect vector inspect. Thumbnail italic layout edit effect auto editor. Device plugin underline list thumbnail main bold. Strikethrough duplicate flatten flows component undo. Blur reesizing asset share vector style text. Italic frame bold layer variant arrow. Project text background content slice. Rectangle flows stroke strikethrough hand move subtract. Device rectangle rectangle arrow align. Underline comment layer device layer main edit mask device auto. Flows device scale line comment image underline team content. Link plugin pixel library invite. Vertical selection clip flows italic editor reesizing community prototype list. Comment mask editor create inspect. Library blur duplicate arrange figma layout. Font connection subtract select undo shadow bold select variant. Frame align reesizing horizontal prototype create asset. Select text strikethrough undo team prototype asset style strikethrough align. Thumbnail outline layer rectangle overflow. Group group pen list font vertical overflow. Image project connection team underline. Arrow layout editor arrange link. Connection opacity connection hand boolean underline content bold object. Text draft bullet group arrow pencil star line. Star project frame pen scale. Font plugin ellipse variant pixel. Distribute star blur figjam editor device rotate. Follower edit inspect draft blur arrange link font align.';
}

const String urlUnicoFinalizado = "https://cadastro.uat.unico.app/finalizado";

const limiteEnvioOtp = 3;
