import 'package:j17_bank_mybank_mobile/model/client/account/abstract_account_client.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/auto_pix_active_cancel.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/auto_pix_schedule_cancel.dart';

class AutoPixCancelClient extends AbstractAccountClient {
  Future<AutoPixActiveCancel> autoPixActiveCancel(
      AutoPixActiveCancel autoPixCancel) async {
    return client.post(
      "/pix-automatico/cancelar",
      body: autoPixCancel.toJson(),
      fromJson: AutoPixActiveCancel.fromJson,
    );
  }

  Future<AutoPixScheduleCancel> autoPixScheduleCancel(
      AutoPixScheduleCancel autoPixScheduleCancel) async {
    return client.post(
      "/pix-automatico/cancelar-agendamento",
      body: autoPixScheduleCancel.toJson(),
      fromJson: AutoPixScheduleCancel.fromJson,
    );
  }
}
