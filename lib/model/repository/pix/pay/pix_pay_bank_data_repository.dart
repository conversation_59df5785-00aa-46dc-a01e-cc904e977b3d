import 'package:j17_bank_mybank_mobile/model/client/pix/pay/with_bank_data/pix_pay_with_bank_data_client.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/pay/common/pix_pay_result.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/pay/common/pix_payment_data.dart';

class PixPayBankDataRepository {
  final PixPayWithBankDataClient _pixPayWithBankDataClient =
      PixPayWithBankDataClient();

  Future<PixPayResult> sendPix(PixPaymentData pixData) async {
    return await _pixPayWithBankDataClient.sendPix(pixData);
  }
}
