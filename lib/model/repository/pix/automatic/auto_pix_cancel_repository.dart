import 'package:j17_bank_mybank_mobile/model/client/pix/automatic/auto_pix_cancel_client.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/auto_pix_active_cancel.dart';
import 'package:j17_bank_mybank_mobile/model/data/pix/auto_pix/auto_pix_schedule_cancel.dart';

class AutoPixCancelRepository {
  final AutoPixCancelClient _client = AutoPixCancelClient();

  Future<AutoPixActiveCancel> autoPixActiveCancel(
      AutoPixActiveCancel autoPixCancel) async {
    return await _client.autoPixActiveCancel(autoPixCancel);
  }

  Future<AutoPixScheduleCancel> autoPixScheduleCancel(
      AutoPixScheduleCancel autoPixScheduleCancel) async {
    return await _client.autoPixScheduleCancel(autoPixScheduleCancel);
  }
}
