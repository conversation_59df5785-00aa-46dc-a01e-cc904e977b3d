import 'package:j17_bank_mybank_mobile/model/data/first_access/senha_criptografada.dart';

class TransacaoRegistroToken {
  SenhaCriptografada tokenTopazCriptografado;
  String syncIdTopaz;

  TransacaoRegistroToken({
    required this.tokenTopazCriptografado,
    required this.syncIdTopaz,
  });

  toJson() {
    return {
      "tokenTopazCriptografado": tokenTopazCriptografado.toJson(),
      "syncIdTopaz": syncIdTopaz,
    };
  }
}
