// ignore_for_file: constant_identifier_names

/// Request para consultar limite do Pix Automático
class PixAutomaticLimitRequest {
  final String syncId;

  PixAutomaticLimitRequest({
    required this.syncId,
  });

  Map<String, String> toQueryParams() {
    return {
      'syncId': syncId,
    };
  }
}

/// Response da consulta de limite do Pix Automático
class PixAutomaticLimitResponse {
  final int nsu;
  final String codigoOrigem;
  final int nsuDestino;
  final String dataHoraCriacao;
  final String dataReferencia;
  final double valor;
  final Map<String, dynamic> informacoesAdicionais;
  final String comprovante;
  final double valorLimiteTransacaoPersonalizado;
  final double valorLimiteTransacaoMaximoAprovado;

  const PixAutomaticLimitResponse({
    required this.nsu,
    required this.codigoOrigem,
    required this.nsuDestino,
    required this.dataHoraCriacao,
    required this.dataReferencia,
    required this.valor,
    required this.informacoesAdicionais,
    required this.comprovante,
    required this.valorLimiteTransacaoPersonalizado,
    required this.valorLimiteTransacaoMaximoAprovado,
  });

  factory PixAutomaticLimitResponse.fromJson(Map<String, dynamic> json) {
    return PixAutomaticLimitResponse(
      nsu: json['nsu'] ?? 0,
      codigoOrigem: json['codigoOrigem'] ?? '',
      nsuDestino: json['nsuDestino'] ?? 0,
      dataHoraCriacao: json['dataHoraCriacao'] ?? '',
      dataReferencia: json['dataReferencia'] ?? '',
      valor: (json['valor'] ?? 0).toDouble(),
      informacoesAdicionais: json['informacoesAdicionais'] ?? {},
      comprovante: json['comprovante'] ?? '',
      valorLimiteTransacaoPersonalizado:
          (json['valorLimiteTransacaoPersonalizado'] ?? 0).toDouble(),
      valorLimiteTransacaoMaximoAprovado:
          (json['valorLimiteTransacaoMaximoAprovado'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'nsu': nsu,
      'codigoOrigem': codigoOrigem,
      'nsuDestino': nsuDestino,
      'dataHoraCriacao': dataHoraCriacao,
      'dataReferencia': dataReferencia,
      'valor': valor,
      'informacoesAdicionais': informacoesAdicionais,
      'comprovante': comprovante,
      'valorLimiteTransacaoPersonalizado': valorLimiteTransacaoPersonalizado,
      'valorLimiteTransacaoMaximoAprovado': valorLimiteTransacaoMaximoAprovado,
    };
  }

  @override
  String toString() =>
      'PixAutomaticLimitResponse(nsu: $nsu, valorLimiteTransacaoPersonalizado: $valorLimiteTransacaoPersonalizado, valorLimiteTransacaoMaximoAprovado: $valorLimiteTransacaoMaximoAprovado)';
}

/// Request para alterar limite do Pix Automático
class PixAutomaticLimitUpdateRequest {
  final String descricao;
  final double valor;
  final String syncId;
  final String idConta;
  final double valorLimiteTransacao;

  PixAutomaticLimitUpdateRequest({
    required this.descricao,
    required this.valor,
    required this.syncId,
    required this.idConta,
    required this.valorLimiteTransacao,
  });

  Map<String, dynamic> toJson() {
    return {
      'descricao': descricao,
      'valor': valor,
      'syncId': syncId,
      'idConta': idConta,
      'valorLimiteTransacao': valorLimiteTransacao,
    };
  }
}

/// Response da alteração de limite do Pix Automático
class PixAutomaticLimitUpdateResponse {
  final int nsu;
  final String codigoOrigem;
  final int nsuDestino;
  final String dataHoraCriacao;
  final String dataReferencia;
  final double valor;
  final Map<String, dynamic> informacoesAdicionais;
  final String comprovante;
  final String statusAlteracao;
  final String mensagemAlteracao;

  const PixAutomaticLimitUpdateResponse({
    required this.nsu,
    required this.codigoOrigem,
    required this.nsuDestino,
    required this.dataHoraCriacao,
    required this.dataReferencia,
    required this.valor,
    required this.informacoesAdicionais,
    required this.comprovante,
    required this.statusAlteracao,
    required this.mensagemAlteracao,
  });

  factory PixAutomaticLimitUpdateResponse.fromJson(Map<String, dynamic> json) {
    return PixAutomaticLimitUpdateResponse(
      nsu: json['nsu'] ?? 0,
      codigoOrigem: json['codigoOrigem'] ?? '',
      nsuDestino: json['nsuDestino'] ?? 0,
      dataHoraCriacao: json['dataHoraCriacao'] ?? '',
      dataReferencia: json['dataReferencia'] ?? '',
      valor: (json['valor'] ?? 0).toDouble(),
      informacoesAdicionais: json['informacoesAdicionais'] ?? {},
      comprovante: json['comprovante'] ?? '',
      statusAlteracao: json['statusAlteracao'] ?? '',
      mensagemAlteracao: json['mensagemAlteracao'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'nsu': nsu,
      'codigoOrigem': codigoOrigem,
      'nsuDestino': nsuDestino,
      'dataHoraCriacao': dataHoraCriacao,
      'dataReferencia': dataReferencia,
      'valor': valor,
      'informacoesAdicionais': informacoesAdicionais,
      'comprovante': comprovante,
      'statusAlteracao': statusAlteracao,
      'mensagemAlteracao': mensagemAlteracao,
    };
  }

  @override
  String toString() =>
      'PixAutomaticLimitUpdateResponse(nsu: $nsu, statusAlteracao: $statusAlteracao, mensagemAlteracao: $mensagemAlteracao)';
}
