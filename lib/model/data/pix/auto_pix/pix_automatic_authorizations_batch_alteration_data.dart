// REQUEST
class PixAutomaticBatchAuthorizationsRequest {
  final String? descricao;
  final double? valor;
  final String? syncId;
  final String idConta;
  final TipoAlteracaoAutorizacao tipoAlteracaoAutorizacao;
  final List<PixAutoBatchAuthorizationsRecurrency> autorizacoes;

  PixAutomaticBatchAuthorizationsRequest({
    this.descricao,
    this.valor,
    this.syncId,
    required this.idConta,
    required this.tipoAlteracaoAutorizacao,
    required this.autorizacoes,
  });

  Map<String, dynamic> toJson() {
    return {
      'descricao': descricao,
      'valor': valor,
      'syncId': syncId,
      'idConta': idConta,
      'tipoAlteracaoAutorizacao': tipoAlteracaoAutorizacao.name,
      'autorizacoes': autorizacoes.map((e) => e.toJson()).toList(),
    };
  }
}

enum TipoAlteracaoAutorizacao {
  VALOR_MAXIMO,
  NOTIFICACAO_AGENDAMENTO,
  LIMITE_CREDITO,
}

class PixAutoBatchAuthorizationsRecurrency {
  final String? identificadorRecorrencia;
  final double? novoValorMaximoPagador;
  final bool? notificacaoAgendamento;

  PixAutoBatchAuthorizationsRecurrency({
    this.identificadorRecorrencia,
    this.novoValorMaximoPagador,
    this.notificacaoAgendamento,
  });

  Map<String, dynamic> toJson() {
    return {
      'identificadorRecorrencia': identificadorRecorrencia,
      'novoValorMaximoPagador': novoValorMaximoPagador,
      'notificacaoAgendamento': notificacaoAgendamento,
    };
  }
}

// RESPONSE
class PixAutomaticBatchAuthorizationsResponse {
  final int? nsu;
  final String? codigoOrigem;
  final int? nsuDestino;
  final DateTime? dataHoraCriacao;
  final DateTime? dataHoraRecorrencia;
  final double? valor;
  final Map<String, dynamic>? informacoesAdicionais;
  final List<PixAutoBatchAuthorizationsDataAlteration>?
      alteracaoDadosAutorizacoes;

  PixAutomaticBatchAuthorizationsResponse({
    this.nsu,
    this.codigoOrigem,
    this.nsuDestino,
    this.dataHoraCriacao,
    this.dataHoraRecorrencia,
    this.valor,
    this.informacoesAdicionais,
    this.alteracaoDadosAutorizacoes,
  });

  factory PixAutomaticBatchAuthorizationsResponse.fromJson(
      Map<String, dynamic> json) {
    return PixAutomaticBatchAuthorizationsResponse(
      nsu: json['nsu'] ?? 0,
      codigoOrigem: json['codigoOrigem'] ?? '',
      nsuDestino: json['nsuDestino'] ?? 0,
      dataHoraCriacao: json['dataHoraCriacao'] != null
          ? DateTime.parse(json['dataHoraCriacao'])
          : null,
      dataHoraRecorrencia: json['dataReferencia'] != null
          ? DateTime.parse(json['dataReferencia'])
          : null,
      valor: (json['valor'] ?? 0).toDouble(),
      informacoesAdicionais: json['informacoesAdicionais'] ?? {},
      alteracaoDadosAutorizacoes: (json['alteracaoDadosAutorizacoes']
                  as List<dynamic>?)
              ?.map((e) => PixAutoBatchAuthorizationsDataAlteration.fromJson(e))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'nsu': nsu,
      'codigoOrigem': codigoOrigem,
      'nsuDestino': nsuDestino,
      'dataHoraCriacao': dataHoraCriacao,
      'dataHoraRecorrencia': dataHoraRecorrencia,
      'valor': valor,
      'informacoesAdicionais': informacoesAdicionais,
      'alteracaoDadosAutorizacoes':
          alteracaoDadosAutorizacoes?.map((e) => e.toJson()).toList(),
    };
  }
}

class PixAutoBatchAuthorizationsDataAlteration {
  final String dataHoraRequisicaoJdpi;
  final String identificacaoRecorrencia;
  final bool notificacaoAgendamentoAtualizacao;

  PixAutoBatchAuthorizationsDataAlteration({
    required this.dataHoraRequisicaoJdpi,
    required this.identificacaoRecorrencia,
    required this.notificacaoAgendamentoAtualizacao,
  });

  factory PixAutoBatchAuthorizationsDataAlteration.fromJson(
      Map<String, dynamic> json) {
    return PixAutoBatchAuthorizationsDataAlteration(
      dataHoraRequisicaoJdpi: json['dataHoraRequisicaoJdpi'] ?? '',
      identificacaoRecorrencia: json['identificadorRecorrencia'] ?? '',
      notificacaoAgendamentoAtualizacao:
          json['notificacaoAgendamentoAtulizacao'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dataHoraRequisicaoJdpi': dataHoraRequisicaoJdpi,
      'identificacaoRecorrencia': identificacaoRecorrencia,
      'notificacaoAgendamentoAtualizacao': notificacaoAgendamentoAtualizacao,
    };
  }
}
