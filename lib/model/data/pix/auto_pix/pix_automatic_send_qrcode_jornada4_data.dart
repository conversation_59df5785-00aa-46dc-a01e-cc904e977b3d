class PixAutomaticSendQrcodeJornada4Data {
  final bool isSuccessful;
  final String? errorMessage;
  final String? pixId;
  final String? status;

  PixAutomaticSendQrcodeJornada4Data({
    required this.isSuccessful,
    this.errorMessage,
    this.pixId,
    this.status,
  });

  factory PixAutomaticSendQrcodeJornada4Data.fromJson(
      Map<String, dynamic> json) {
    return PixAutomaticSendQrcodeJornada4Data(
      isSuccessful: json['isSuccessful'] ?? false,
      errorMessage: json['errorMessage'],
      pixId: json['pixId'],
      status: json['status'],
    );
  }
}

class PixJornada4Request {
  final String qrCodePayload;
  final double valor;
  final String idConta;

  PixJornada4Request({
    required this.qrCodePayload,
    required this.valor,
    required this.idConta,
  });

  Map<String, dynamic> toJson() {
    return {
      'qrCodePayload': qrCodePayload,
      'valor': valor,
      'idConta': idConta,
    };
  }
}
