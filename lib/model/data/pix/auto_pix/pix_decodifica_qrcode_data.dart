class PixDecodificaQrcodeData {
  final bool isSuccessful;
  final String? errorMessage;
  final DecodificacaoQrcodeDTO? decodificacaoQrcodeDTO;

  PixDecodificaQrcodeData({
    required this.isSuccessful,
    this.errorMessage,
    this.decodificacaoQrcodeDTO,
  });

  factory PixDecodificaQrcodeData.fromJson(Map<String, dynamic> json) {
    // Se há dados válidos de decodificação, considera sucesso
    final hasValidData = json['decodificacaoQrcodeDTO'] != null;
    final explicitSuccess = json['isSuccessful'] ?? false;

    return PixDecodificaQrcodeData(
      isSuccessful: explicitSuccess ||
          hasValidData, // Sucesso se explícito OU se há dados válidos
      errorMessage: json['errorMessage'],
      decodificacaoQrcodeDTO: json['decodificacaoQrcodeDTO'] != null
          ? DecodificacaoQrcodeDTO.fromJson(json['decodificacaoQrcodeDTO'])
          : null,
    );
  }
}

class DecodificacaoQrcodeDTO {
  final String? fimAFimId;
  final String? tipoQRCode;
  final String? jornada;
  final DadosQrCodeEstatico? dadosQrCodeEstatico;
  final DadosQrCodeDinamico? dadosQrCodeDinamico;
  final DadosQrCodeDinamicoCobv? dadosQrCodeDinamicoCobv;
  final DadosQrCodeComposto? dadosQrCodeComposto;
  final String? message;
  final String? detail;

  DecodificacaoQrcodeDTO({
    this.fimAFimId,
    this.tipoQRCode,
    this.jornada,
    this.dadosQrCodeEstatico,
    this.dadosQrCodeDinamico,
    this.dadosQrCodeDinamicoCobv,
    this.dadosQrCodeComposto,
    this.message,
    this.detail,
  });

  factory DecodificacaoQrcodeDTO.fromJson(Map<String, dynamic> json) {
    String? jornadaStr;
    final jornadaJson = json['jornada'];
    if (jornadaJson is int) {
      jornadaStr = 'JORNADA_${jornadaJson}';
    } else if (jornadaJson is String) {
      jornadaStr = jornadaJson;
    } else {
      jornadaStr = null;
    }
    return DecodificacaoQrcodeDTO(
      fimAFimId: json['fimAFimId'],
      tipoQRCode: json['tipoQRCode'],
      jornada: jornadaStr,
      dadosQrCodeEstatico: json['dadosQrCodeEstatico'] != null
          ? DadosQrCodeEstatico.fromJson(json['dadosQrCodeEstatico'])
          : null,
      dadosQrCodeDinamico: json['dadosQrCodeDinamico'] != null
          ? DadosQrCodeDinamico.fromJson(json['dadosQrCodeDinamico'])
          : null,
      dadosQrCodeDinamicoCobv: json['dadosQrCodeDinamicoCobv'] != null
          ? DadosQrCodeDinamicoCobv.fromJson(json['dadosQrCodeDinamicoCobv'])
          : null,
      dadosQrCodeComposto: json['dadosQrCodeComposto'] != null
          ? DadosQrCodeComposto.fromJson(json['dadosQrCodeComposto'])
          : null,
      message: json['message'],
      detail: json['detail'],
    );
  }
}

class DadosQrCodeEstatico {
  final dynamic ispb;
  final String? agencia;
  final String? tipoConta;
  final String? conta;
  final String? tipoChave;
  final String? chave;
  final String? codigoCategoria;
  final String? nomeRecebedor;
  final String? tipoPessoaRecebedor;
  final String? cpfCnpjRecebedor;
  final String? cidade;
  final String? cep;
  final String? idConciliacaoRecebedor;
  final double? valor;
  final String? dadosAdicionais;
  final String? ispbFss;

  DadosQrCodeEstatico({
    this.ispb,
    this.agencia,
    this.tipoConta,
    this.conta,
    this.tipoChave,
    this.chave,
    this.codigoCategoria,
    this.nomeRecebedor,
    this.tipoPessoaRecebedor,
    this.cpfCnpjRecebedor,
    this.cidade,
    this.cep,
    this.idConciliacaoRecebedor,
    this.valor,
    this.dadosAdicionais,
    this.ispbFss,
  });

  factory DadosQrCodeEstatico.fromJson(Map<String, dynamic> json) {
    return DadosQrCodeEstatico(
      ispb: json['ispb'],
      agencia: json['agencia'],
      tipoConta: json['tipoConta'],
      conta: json['conta'],
      tipoChave: json['tipoChave'],
      chave: json['chave'],
      codigoCategoria: json['codigoCategoria'],
      nomeRecebedor: json['nomeRecebedor'],
      tipoPessoaRecebedor: json['tipoPessoaRecebedor'],
      cpfCnpjRecebedor: json['cpfCnpjRecebedor'],
      cidade: json['cidade'],
      cep: json['cep'],
      idConciliacaoRecebedor: json['idConciliacaoRecebedor'],
      valor: json['valor']?.toDouble(),
      dadosAdicionais: json['dadosAdicionais'],
      ispbFss: json['ispbFss'],
    );
  }
}

class DadosQrCodeDinamico {
  final dynamic ispb;
  final String? agencia;
  final String? tipoConta;
  final String? conta;
  final String? tipoChave;
  final String? chave;
  final String? codigoCategoria;
  final String? nomeRecebedor;
  final String? tipoPessoaRecebedor;
  final String? cpfCnpjRecebedor;
  final String? cidade;
  final String? cep;
  final String? idConciliacaoRecebedor;
  final int? revisao;
  final String? solicitacaoPagador;
  final String? cpfPagador;
  final String? cnpjPagador;
  final String? nomePagador;
  final double? valorOriginal;
  final String? modalidadeAlteracao;
  final double? valorSaque;
  final String? modalidadeAltSaque;
  final int? ispbPssSaque;
  final String? modalidadeAgSaque;
  final double? valorTroco;
  final String? modalidadeAltTroco;
  final int? ispbPssTroco;
  final String? modalidadeAgTroco;
  final String? expiracaoQR;
  final Map<String, dynamic>? dadosAdicionais;
  final String? dtHrCriacao;
  final String? dtHrApresentacao;
  final String? urlPspRecebedor;
  final bool? reutilizavel;
  final String? status;

  DadosQrCodeDinamico({
    this.ispb,
    this.agencia,
    this.tipoConta,
    this.conta,
    this.tipoChave,
    this.chave,
    this.codigoCategoria,
    this.nomeRecebedor,
    this.tipoPessoaRecebedor,
    this.cpfCnpjRecebedor,
    this.cidade,
    this.cep,
    this.idConciliacaoRecebedor,
    this.revisao,
    this.solicitacaoPagador,
    this.cpfPagador,
    this.cnpjPagador,
    this.nomePagador,
    this.valorOriginal,
    this.modalidadeAlteracao,
    this.valorSaque,
    this.modalidadeAltSaque,
    this.ispbPssSaque,
    this.modalidadeAgSaque,
    this.valorTroco,
    this.modalidadeAltTroco,
    this.ispbPssTroco,
    this.modalidadeAgTroco,
    this.expiracaoQR,
    this.dadosAdicionais,
    this.dtHrCriacao,
    this.dtHrApresentacao,
    this.urlPspRecebedor,
    this.reutilizavel,
    this.status,
  });

  factory DadosQrCodeDinamico.fromJson(Map<String, dynamic> json) {
    return DadosQrCodeDinamico(
      ispb: json['ispb'],
      agencia: json['agencia'],
      tipoConta: json['tipoConta'],
      conta: json['conta'],
      tipoChave: json['tipoChave'],
      chave: json['chave'],
      codigoCategoria: json['codigoCategoria'],
      nomeRecebedor: json['nomeRecebedor'],
      tipoPessoaRecebedor: json['tipoPessoaRecebedor'],
      cpfCnpjRecebedor: json['cpfCnpjRecebedor'],
      cidade: json['cidade'],
      cep: json['cep'],
      idConciliacaoRecebedor: json['idConciliacaoRecebedor'],
      revisao: json['revisao'],
      solicitacaoPagador: json['solicitacaoPagador'],
      cpfPagador: json['cpfPagador'],
      cnpjPagador: json['cnpjPagador'],
      nomePagador: json['nomePagador'],
      valorOriginal: json['valorOriginal']?.toDouble(),
      modalidadeAlteracao: json['modalidadeAlteracao'],
      valorSaque: json['valorSaque']?.toDouble(),
      modalidadeAltSaque: json['modalidadeAltSaque'],
      ispbPssSaque: json['ispbPssSaque'],
      modalidadeAgSaque: json['modalidadeAgSaque'],
      valorTroco: json['valorTroco']?.toDouble(),
      modalidadeAltTroco: json['modalidadeAltTroco'],
      ispbPssTroco: json['ispbPssTroco'],
      modalidadeAgTroco: json['modalidadeAgTroco'],
      expiracaoQR: json['expiracaoQR'],
      dadosAdicionais: json['dadosAdicionais'],
      dtHrCriacao: json['dtHrCriacao'],
      dtHrApresentacao: json['dtHrApresentacao'],
      urlPspRecebedor: json['urlPspRecebedor'],
      reutilizavel: json['reutilizavel'],
      status: json['status'],
    );
  }
}

class DadosQrCodeDinamicoCobv {
  final dynamic ispb;
  final String? agencia;
  final String? tipoConta;
  final String? conta;
  final String? tipoChave;
  final String? chave;
  final String? codigoCategoria;
  final String? nomeRecebedor;
  final String? tipoPessoaRecebedor;
  final String? cpfCnpjRecebedor;
  final String? cidade;
  final String? cep;
  final String? idConciliacaoRecebedor;
  final int? revisao;
  final String? nomeFantasiaRecebedor;
  final String? logradouroRecebedor;
  final String? uf;
  final String? solicitacaoPagador;
  final String? cpfPagador;
  final String? cnpjPagador;
  final String? nomePagador;
  final double? valorOriginal;
  final double? abatimento;
  final double? desconto;
  final double? juros;
  final double? multa;
  final double? valorFinal;
  final String? dataVencimento;
  final int? diasAposVencimento;
  final Map<String, dynamic>? dadosAdicionais;
  final String? dtHrCriacao;
  final String? dtHrApresentacao;
  final String? urlPspRecebedor;
  final bool? reutilizavel;
  final String? status;

  DadosQrCodeDinamicoCobv({
    this.ispb,
    this.agencia,
    this.tipoConta,
    this.conta,
    this.tipoChave,
    this.chave,
    this.codigoCategoria,
    this.nomeRecebedor,
    this.tipoPessoaRecebedor,
    this.cpfCnpjRecebedor,
    this.cidade,
    this.cep,
    this.idConciliacaoRecebedor,
    this.revisao,
    this.nomeFantasiaRecebedor,
    this.logradouroRecebedor,
    this.uf,
    this.solicitacaoPagador,
    this.cpfPagador,
    this.cnpjPagador,
    this.nomePagador,
    this.valorOriginal,
    this.abatimento,
    this.desconto,
    this.juros,
    this.multa,
    this.valorFinal,
    this.dataVencimento,
    this.diasAposVencimento,
    this.dadosAdicionais,
    this.dtHrCriacao,
    this.dtHrApresentacao,
    this.urlPspRecebedor,
    this.reutilizavel,
    this.status,
  });

  factory DadosQrCodeDinamicoCobv.fromJson(Map<String, dynamic> json) {
    return DadosQrCodeDinamicoCobv(
      ispb: json['ispb'],
      agencia: json['agencia'],
      tipoConta: json['tipoConta'],
      conta: json['conta'],
      tipoChave: json['tipoChave'],
      chave: json['chave'],
      codigoCategoria: json['codigoCategoria'],
      nomeRecebedor: json['nomeRecebedor'],
      tipoPessoaRecebedor: json['tipoPessoaRecebedor'],
      cpfCnpjRecebedor: json['cpfCnpjRecebedor'],
      cidade: json['cidade'],
      cep: json['cep'],
      idConciliacaoRecebedor: json['idConciliacaoRecebedor'],
      revisao: json['revisao'],
      nomeFantasiaRecebedor: json['nomeFantasiaRecebedor'],
      logradouroRecebedor: json['logradouroRecebedor'],
      uf: json['uf'],
      solicitacaoPagador: json['solicitacaoPagador'],
      cpfPagador: json['cpfPagador'],
      cnpjPagador: json['cnpjPagador'],
      nomePagador: json['nomePagador'],
      valorOriginal: json['valorOriginal']?.toDouble(),
      abatimento: json['abatimento']?.toDouble(),
      desconto: json['desconto']?.toDouble(),
      juros: json['juros']?.toDouble(),
      multa: json['multa']?.toDouble(),
      valorFinal: json['valorFinal']?.toDouble(),
      dataVencimento: json['dataVencimento'],
      diasAposVencimento: json['diasAposVencimento'],
      dadosAdicionais: json['dadosAdicionais'],
      dtHrCriacao: json['dtHrCriacao'],
      dtHrApresentacao: json['dtHrApresentacao'],
      urlPspRecebedor: json['urlPspRecebedor'],
      reutilizavel: json['reutilizavel'],
      status: json['status'],
    );
  }
}

class DadosQrCodeComposto {
  final dynamic ispb;
  final String? agencia;
  final String? tipoConta;
  final String? conta;
  final String? tipoChave;
  final String? chave;
  final String? codigoCategoria;
  final String? nomeRecebedor;
  final String? tipoPessoaRecebedor;
  final String? cpfCnpjRecebedor;
  final String? cidade;
  final String? cep;
  final String? idConciliacaoRecebedor;
  final String? idRecorrencia;
  final String? tpFrequencia;
  final String? dtInicialRecorrencia;
  final String? dtFinalRecorrencia;
  final int? codigoMunicipioIbge;
  final double? valor;
  final double? pisoValorMaximo;
  final String?
      tipoPagamentoRecorrente; // ✅ NOVO: Tipo de pagamento (FIXO/VARIAVEL)
  final RecebedorQrCodeComposto? recebedor;
  final DevedorQrCodeComposto? devedor;
  final String? nrContrato;
  final String? descContrato;
  final String? retentativa;
  final AtualizacoesQrCodeComposto? atualizacoes;
  final String? dtHrApresentacao;
  final String? urlPspRecebedor;
  final EstatisticasDoUsuario? estatisticasDoUsuario;
  final EstatisticasDaChave? estatisticasDaChave;

  DadosQrCodeComposto({
    this.ispb,
    this.agencia,
    this.tipoConta,
    this.conta,
    this.tipoChave,
    this.chave,
    this.codigoCategoria,
    this.nomeRecebedor,
    this.tipoPessoaRecebedor,
    this.cpfCnpjRecebedor,
    this.cidade,
    this.cep,
    this.idConciliacaoRecebedor,
    this.idRecorrencia,
    this.tpFrequencia,
    this.dtInicialRecorrencia,
    this.dtFinalRecorrencia,
    this.codigoMunicipioIbge,
    this.valor,
    this.pisoValorMaximo,
    this.tipoPagamentoRecorrente,
    this.recebedor,
    this.devedor,
    this.nrContrato,
    this.descContrato,
    this.retentativa,
    this.atualizacoes,
    this.dtHrApresentacao,
    this.urlPspRecebedor,
    this.estatisticasDoUsuario,
    this.estatisticasDaChave,
  });

  factory DadosQrCodeComposto.fromJson(Map<String, dynamic> json) {
    return DadosQrCodeComposto(
      ispb: json['ispb'],
      agencia: json['agencia'],
      tipoConta: json['tipoConta'],
      conta: json['conta'],
      tipoChave: json['tipoChave'],
      chave: json['chave'],
      codigoCategoria: json['codigoCategoria'],
      nomeRecebedor: json['nomeRecebedor'],
      tipoPessoaRecebedor: json['tipoPessoaRecebedor'],
      cpfCnpjRecebedor: json['cpfCnpjRecebedor'],
      cidade: json['cidade'],
      cep: json['cep'],
      idConciliacaoRecebedor: json['idConciliacaoRecebedor'],
      idRecorrencia: json['idRecorrencia'],
      tpFrequencia: json['tpFrequencia'],
      dtInicialRecorrencia: json['dtInicialRecorrencia'],
      dtFinalRecorrencia: json['dtFinalRecorrencia'],
      codigoMunicipioIbge: json['codigoMunicipioIbge'],
      valor: json['valor']?.toDouble(),
      pisoValorMaximo: json['pisoValorMaximo']?.toDouble(),
      tipoPagamentoRecorrente: json['tipoPagamentoRecorrente'],
      recebedor: json['recebedor'] != null
          ? RecebedorQrCodeComposto.fromJson(json['recebedor'])
          : null,
      devedor: json['devedor'] != null
          ? DevedorQrCodeComposto.fromJson(json['devedor'])
          : null,
      nrContrato: json['nrContrato'],
      descContrato: json['descContrato'],
      retentativa: json['retentativa'],
      atualizacoes: json['atualizacoes'] != null
          ? AtualizacoesQrCodeComposto.fromJson(json['atualizacoes'])
          : null,
      dtHrApresentacao: json['dtHrApresentacao'],
      urlPspRecebedor: json['urlPspRecebedor'],
      estatisticasDoUsuario: json['estatisticasDoUsuario'] != null
          ? EstatisticasDoUsuario.fromJson(json['estatisticasDoUsuario'])
          : null,
      estatisticasDaChave: json['estatisticasDaChave'] != null
          ? EstatisticasDaChave.fromJson(json['estatisticasDaChave'])
          : null,
    );
  }
}

class RecebedorQrCodeComposto {
  final int? ispb;
  final int? cnpj;
  final String? nome;
  final String? tipoPessoa;

  RecebedorQrCodeComposto({
    this.ispb,
    this.cnpj,
    this.nome,
    this.tipoPessoa,
  });

  factory RecebedorQrCodeComposto.fromJson(Map<String, dynamic> json) {
    return RecebedorQrCodeComposto(
      ispb: json['ispb'],
      cnpj: json['cnpj'],
      nome: json['nome'],
      tipoPessoa: json['tipoPessoa'],
    );
  }
}

class DevedorQrCodeComposto {
  final int? cpfCnpj;
  final String? nome;
  final String? tipoPessoa;

  DevedorQrCodeComposto({
    this.cpfCnpj,
    this.nome,
    this.tipoPessoa,
  });

  factory DevedorQrCodeComposto.fromJson(Map<String, dynamic> json) {
    return DevedorQrCodeComposto(
      cpfCnpj: json['cpfCnpj'],
      nome: json['nome'],
      tipoPessoa: json['tipoPessoa'],
    );
  }
}

class AtualizacoesQrCodeComposto {
  final String? stRecorrencia;
  final String? dtHrSituacao;

  AtualizacoesQrCodeComposto({
    this.stRecorrencia,
    this.dtHrSituacao,
  });

  factory AtualizacoesQrCodeComposto.fromJson(Map<String, dynamic> json) {
    return AtualizacoesQrCodeComposto(
      stRecorrencia: json['stRecorrencia'],
      dtHrSituacao: json['dtHrSituacao'],
    );
  }
}

class EstatisticasDoUsuario {
  final EstatisticasSpi? spi;
  final EstatisticasMarcacoesFraude? marcacoesFraude;
  final EstatisticasNotificacaoInfracoes? notificacaoInfracoes;
  final EstatisticasContas? contas;

  EstatisticasDoUsuario({
    this.spi,
    this.marcacoesFraude,
    this.notificacaoInfracoes,
    this.contas,
  });

  factory EstatisticasDoUsuario.fromJson(Map<String, dynamic> json) {
    return EstatisticasDoUsuario(
      spi: json['spi'] != null ? EstatisticasSpi.fromJson(json['spi']) : null,
      marcacoesFraude: json['marcacoesFraude'] != null
          ? EstatisticasMarcacoesFraude.fromJson(json['marcacoesFraude'])
          : null,
      notificacaoInfracoes: json['notificacaoInfracoes'] != null
          ? EstatisticasNotificacaoInfracoes.fromJson(
              json['notificacaoInfracoes'])
          : null,
      contas: json['contas'] != null
          ? EstatisticasContas.fromJson(json['contas'])
          : null,
    );
  }
}

class EstatisticasDaChave {
  final EstatisticasSpi? spi;
  final EstatisticasMarcacoesFraude? marcacoesFraude;
  final EstatisticasNotificacaoInfracoes? notificacaoInfracoes;
  final EstatisticasContas? contas;

  EstatisticasDaChave({
    this.spi,
    this.marcacoesFraude,
    this.notificacaoInfracoes,
    this.contas,
  });

  factory EstatisticasDaChave.fromJson(Map<String, dynamic> json) {
    return EstatisticasDaChave(
      spi: json['spi'] != null ? EstatisticasSpi.fromJson(json['spi']) : null,
      marcacoesFraude: json['marcacoesFraude'] != null
          ? EstatisticasMarcacoesFraude.fromJson(json['marcacoesFraude'])
          : null,
      notificacaoInfracoes: json['notificacaoInfracoes'] != null
          ? EstatisticasNotificacaoInfracoes.fromJson(
              json['notificacaoInfracoes'])
          : null,
      contas: json['contas'] != null
          ? EstatisticasContas.fromJson(json['contas'])
          : null,
    );
  }
}

class EstatisticasSpi {
  final String? dataHoraUltimoEvento;
  final TransacoesEstatisticas? transacoes;

  EstatisticasSpi({
    this.dataHoraUltimoEvento,
    this.transacoes,
  });

  factory EstatisticasSpi.fromJson(Map<String, dynamic> json) {
    return EstatisticasSpi(
      dataHoraUltimoEvento: json['dataHoraUltimoEvento'],
      transacoes: json['transacoes'] != null
          ? TransacoesEstatisticas.fromJson(json['transacoes'])
          : null,
    );
  }
}

class TransacoesEstatisticas {
  final int? d90;
  final int? m12;
  final int? m60;

  TransacoesEstatisticas({
    this.d90,
    this.m12,
    this.m60,
  });

  factory TransacoesEstatisticas.fromJson(Map<String, dynamic> json) {
    return TransacoesEstatisticas(
      d90: json['d90'],
      m12: json['m12'],
      m60: json['m60'],
    );
  }
}

class EstatisticasMarcacoesFraude {
  final String? dataHoraUltimoEvento;
  final TransacoesEstatisticas? falsidadeIdeologica;
  final TransacoesEstatisticas? contaLaranja;
  final TransacoesEstatisticas? contaFraudador;
  final TransacoesEstatisticas? outra;
  final TransacoesEstatisticas? fraudeDesconhecida;
  final TransacoesEstatisticas? fraudeTotalSpi;
  final TransacoesEstatisticas? fraudePspDistintos;

  EstatisticasMarcacoesFraude({
    this.dataHoraUltimoEvento,
    this.falsidadeIdeologica,
    this.contaLaranja,
    this.contaFraudador,
    this.outra,
    this.fraudeDesconhecida,
    this.fraudeTotalSpi,
    this.fraudePspDistintos,
  });

  factory EstatisticasMarcacoesFraude.fromJson(Map<String, dynamic> json) {
    return EstatisticasMarcacoesFraude(
      dataHoraUltimoEvento: json['dataHoraUltimoEvento'],
      falsidadeIdeologica: json['falsidadeIdeologica'] != null
          ? TransacoesEstatisticas.fromJson(json['falsidadeIdeologica'])
          : null,
      contaLaranja: json['contaLaranja'] != null
          ? TransacoesEstatisticas.fromJson(json['contaLaranja'])
          : null,
      contaFraudador: json['contaFraudador'] != null
          ? TransacoesEstatisticas.fromJson(json['contaFraudador'])
          : null,
      outra: json['outra'] != null
          ? TransacoesEstatisticas.fromJson(json['outra'])
          : null,
      fraudeDesconhecida: json['fraudeDesconhecida'] != null
          ? TransacoesEstatisticas.fromJson(json['fraudeDesconhecida'])
          : null,
      fraudeTotalSpi: json['fraudeTotalSpi'] != null
          ? TransacoesEstatisticas.fromJson(json['fraudeTotalSpi'])
          : null,
      fraudePspDistintos: json['fraudePspDistintos'] != null
          ? TransacoesEstatisticas.fromJson(json['fraudePspDistintos'])
          : null,
    );
  }
}

class EstatisticasNotificacaoInfracoes {
  final String? dataHoraUltimoEvento;
  final int? notificacoesEmAberto;
  final int? notificacoesPspEmAberto;
  final TransacoesEstatisticas? notificacoesRejeitadas;

  EstatisticasNotificacaoInfracoes({
    this.dataHoraUltimoEvento,
    this.notificacoesEmAberto,
    this.notificacoesPspEmAberto,
    this.notificacoesRejeitadas,
  });

  factory EstatisticasNotificacaoInfracoes.fromJson(Map<String, dynamic> json) {
    return EstatisticasNotificacaoInfracoes(
      dataHoraUltimoEvento: json['dataHoraUltimoEvento'],
      notificacoesEmAberto: json['notificacoesEmAberto'],
      notificacoesPspEmAberto: json['notificacoesPspEmAberto'],
      notificacoesRejeitadas: json['notificacoesRejeitadas'] != null
          ? TransacoesEstatisticas.fromJson(json['notificacoesRejeitadas'])
          : null,
    );
  }
}

class EstatisticasContas {
  final String? dataHoraUltimoEvento;
  final int? contasRegistradas;

  EstatisticasContas({
    this.dataHoraUltimoEvento,
    this.contasRegistradas,
  });

  factory EstatisticasContas.fromJson(Map<String, dynamic> json) {
    return EstatisticasContas(
      dataHoraUltimoEvento: json['dataHoraUltimoEvento'],
      contasRegistradas: json['contasRegistradas'],
    );
  }
}
