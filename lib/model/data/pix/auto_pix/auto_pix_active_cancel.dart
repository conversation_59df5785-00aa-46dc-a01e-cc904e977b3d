class AutoPixActiveCancel {
  String? descricao;
  double? valor;
  String? syncId;
  String? idConta;
  String? tipoPessoaSolicitante;
  String? identificadorRecorrencia;
  int? nsu;
  String? codigoOrigem;
  int? nsuDestino;
  DateTime? dataHoraCriacao;
  DateTime? dataReferencia;
  Map<String, dynamic>? informacoesAdicionais;
  String? comprovante;
  DateTime? dataHoraCancelamento;
  String? identificadorCancelamento;
  String? situacaoCancelamento;

  AutoPixActiveCancel({
    this.descricao,
    this.valor,
    this.syncId,
    this.idConta,
    this.tipoPessoaSolicitante,
    this.identificadorRecorrencia,
    this.nsu,
    this.codigoOrigem,
    this.nsuDestino,
    this.dataHoraCriacao,
    this.dataReferencia,
    this.informacoesAdicionais,
    this.comprovante,
    this.dataHoraCancelamento,
    this.identificadorCancelamento,
    this.situacaoCancelamento,
  });

  factory AutoPixActiveCancel.fromJson(Map<String, dynamic> json) {
    return AutoPixActiveCancel(
      descricao: json['descricao'] ?? "",
      valor: (json['valor'] as num?)?.toDouble() ?? 0.0,
      syncId: json['syncId'] ?? "",
      idConta: json['idConta'] ?? "",
      tipoPessoaSolicitante: json['tipoPessoaSolicitante'] ?? "",
      identificadorRecorrencia: json['identificadorRecorrencia'] ?? "",
      nsu: json['nsu'] ?? 0,
      codigoOrigem: json['codigoOrigem'] ?? "",
      nsuDestino: json['nsuDestino'] ?? 0,
      dataHoraCriacao: json['dataHoraCriacao'] != null
          ? DateTime.parse(json['dataHoraCriacao'])
          : null,
      dataReferencia: json['dataReferencia'] != null
          ? DateTime.parse(json['dataReferencia'])
          : null,
      informacoesAdicionais:
          json['informacoesAdicionais'] as Map<String, dynamic>?,
      comprovante: json['comprovante'] ?? "",
      dataHoraCancelamento: json['dataHoraCancelamento'] != null
          ? DateTime.parse(json['dataHoraCancelamento'])
          : null,
      identificadorCancelamento: json['identificadorCancelamento'] ?? "",
      situacaoCancelamento: json['situacaoCancelamento'] ?? "",
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'descricao': descricao,
      'valor': valor,
      'syncId': syncId,
      'idConta': idConta,
      'tipoPessoaSolicitante': tipoPessoaSolicitante,
      'identificadorRecorrencia': identificadorRecorrencia,
      'nsu': nsu,
      'codigoOrigem': codigoOrigem,
      'nsuDestino': nsuDestino,
      'dataHoraCriacao': dataHoraCriacao?.toIso8601String(),
      'dataReferencia': dataReferencia?.toIso8601String(),
      'informacoesAdicionais': informacoesAdicionais,
      'comprovante': comprovante,
      'dataHoraCancelamento': dataHoraCancelamento?.toIso8601String(),
      'identificadorCancelamento': identificadorCancelamento,
      'situacaoCancelamento': situacaoCancelamento,
    };
  }
}
