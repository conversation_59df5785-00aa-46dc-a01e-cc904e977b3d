class AutoPixSchedule {
  int? nsu;
  String? codigoOrigem;
  int? nsuDestino;
  DateTime? dataHoraCriacao;
  DateTime? dataReferencia;
  double? valor;
  Map<String, dynamic>? informacoesAdicionais;
  String? comprovante;
  List<ConteudoDTO>? conteudo;

  AutoPixSchedule({
    this.nsu,
    this.codigoOrigem,
    this.nsuDestino,
    this.dataHoraCriacao,
    this.dataReferencia,
    this.valor,
    this.informacoesAdicionais,
    this.comprovante,
    this.conteudo,
  });
  factory AutoPixSchedule.fromJson(Map<String, dynamic> json) {
    return AutoPixSchedule(
      nsu: json['nsu'],
      codigoOrigem: json['codigoOrigem'],
      nsuDestino: json['nsuDestino'],
      dataHoraCriacao: DateTime.parse(json['dataHoraCriacao']),
      dataReferencia: DateTime.parse(json['dataReferencia']),
      valor: double.tryParse(json['valor'].toString()) ?? 0.0,
      informacoesAdicionais: json['informacoesAdicionais'] != null
          ? Map<String, dynamic>.from(json['informacoesAdicionais'])
          : null,
      comprovante: json['comprovante'],
      conteudo: json['conteudoDTO'] != null
          ? (json['conteudoDTO'] as List)
              .map((item) => ConteudoDTO.fromJson(item))
              .toList()
          : null,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'nsu': nsu,
      'codigoOrigem': codigoOrigem,
      'nsuDestino': nsuDestino,
      'dataHoraCriacao': dataHoraCriacao?.toIso8601String(),
      'dataReferencia': dataReferencia?.toIso8601String(),
      'valor': valor,
      'conteudo': conteudo?.map((item) => item.toJson()).toList(),
      'comprovante': comprovante,
    };
  }
}

class ConteudoDTO {
  String? nomeRecebedor;
  String? cpfCnpjRecebedor;
  String? nomeDevedor;
  String? cpfCnpjDevedor;
  int? recebedorIspb;
  String? recebedorInstituicaoFinanceira;
  String? idConciliacaoRecebedor;
  String? endToEndIdentificador;
  String? recebedorAgencia;
  String? recebedorConta;
  DateTime? dataAgendamento;
  Pagador? pagador;
  ObjetoPagamento? objetoPagamento;

  ConteudoDTO({
    this.nomeRecebedor,
    this.cpfCnpjRecebedor,
    this.nomeDevedor,
    this.cpfCnpjDevedor,
    this.recebedorIspb,
    this.recebedorInstituicaoFinanceira,
    this.idConciliacaoRecebedor,
    this.endToEndIdentificador,
    this.recebedorAgencia,
    this.recebedorConta,
    this.dataAgendamento,
    this.pagador,
    this.objetoPagamento,
  });

  factory ConteudoDTO.fromJson(Map<String, dynamic> json) {
    return ConteudoDTO(
      nomeRecebedor: json['nomeRecebedor'] ?? '',
      cpfCnpjRecebedor: json['cpfCnpjRecebedor'] ?? '',
      nomeDevedor: json['nomeDevedor'] ?? '',
      cpfCnpjDevedor: json['cpfCnpjDevedor'] ?? '',
      recebedorIspb: json['recebedorIspb'] ?? 0,
      recebedorInstituicaoFinanceira:
          json['recebedorInstituicaoFinanceira']?.toString() ?? '',
      idConciliacaoRecebedor: json['idConciliacaoRecebedor'] ?? '',
      endToEndIdentificador: json['endToEndIdentificador'] ?? '',
      recebedorAgencia: json['recebedorAgencia'] ?? '',
      recebedorConta: json['recebedorConta'] ?? '',
      dataAgendamento: DateTime.parse(json['dataAgendamento']),
      pagador:
          json['pagador'] != null ? Pagador.fromJson(json['pagador']) : null,
      objetoPagamento: json['objetoPagamento'] != null
          ? ObjetoPagamento.fromJson(json['objetoPagamento'])
          : null,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'nomeRecebedor': nomeRecebedor,
      'cpfCnpjRecebedor': cpfCnpjRecebedor,
      'nomeDevedor': nomeDevedor,
      'cpfCnpjDevedor': cpfCnpjDevedor,
      'recebedorIspb': recebedorIspb,
      'recebedorInstituicaoFinanceira': recebedorInstituicaoFinanceira,
      'idConciliacaoRecebedor': idConciliacaoRecebedor,
      'endToEndIdentificador': endToEndIdentificador,
      'objetoPagamento': objetoPagamento?.toJson(),
    };
  }
}

class Pagador {
  int? ispb;
  String? nomeInstituicaoFinanaceira;
  String? tipoPessoa;
  String? cpfCnpj;
  String? nome;
  String? numeroAgencia;
  String? tipoConta;
  String? numeroConta;
  int? codigoMunicipioIbge;

  Pagador({
    this.ispb,
    this.nomeInstituicaoFinanaceira,
    this.tipoPessoa,
    this.cpfCnpj,
    this.nome,
    this.numeroAgencia,
    this.tipoConta,
    this.numeroConta,
    this.codigoMunicipioIbge,
  });
  factory Pagador.fromJson(Map<String, dynamic> json) {
    return Pagador(
      ispb: json['ispb'] ?? 0,
      nomeInstituicaoFinanaceira: json['nomeInstituicaoFinanaceira'] ?? '',
      tipoPessoa: json['nomeInstituicaoFinanaceira'] ?? '',
      cpfCnpj: json['cpfCnpj'] ?? '',
      nome: json['nome'] ?? '',
      numeroAgencia: json['numeroAgencia'] ?? '',
      tipoConta: json['tipoConta'] ?? '',
      numeroConta: json['numeroConta'] ?? '',
      codigoMunicipioIbge: json['valorPagamento'] ?? 0,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'ispb': ispb,
      'nomeInstituicaoFinanaceira': nomeInstituicaoFinanaceira,
      'tipoPessoa': tipoPessoa,
      'cpfCnpj': cpfCnpj,
      'nome': nome,
      'numeroAgencia': numeroAgencia,
      'tipoConta': tipoConta,
      'numeroConta': numeroConta,
      'codigoMunicipioIbge': codigoMunicipioIbge,
    };
  }
}

class ObjetoPagamento {
  int? codigoCliente;
  DateTime? dataPrevistaPagamento;
  double? valorPagamento;
  String? descricao;

  ObjetoPagamento({
    this.codigoCliente,
    this.dataPrevistaPagamento,
    this.valorPagamento,
    this.descricao,
  });
  factory ObjetoPagamento.fromJson(Map<String, dynamic> json) {
    return ObjetoPagamento(
      codigoCliente: json['identificadorCobranca']?['codigoCliente'] ?? 0,
      dataPrevistaPagamento: json['dataPrevistaPagamento'] != null
          ? DateTime.parse(json['dataPrevistaPagamento'])
          : null,
      valorPagamento: double.tryParse(json['valorPagamento'].toString()) ?? 0,
      descricao: json['descricao'] ?? '',
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'codigoCliente': codigoCliente,
      'dataPrevistaPagamento': dataPrevistaPagamento?.toIso8601String(),
      'valorPagamento': valorPagamento,
      'descricao': descricao,
    };
  }
}
