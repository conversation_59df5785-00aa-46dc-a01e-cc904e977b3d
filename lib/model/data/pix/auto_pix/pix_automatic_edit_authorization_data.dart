// ignore_for_file: constant_identifier_names

/// Request para editar autorização PIX automático
class PixAutomaticEditAuthorizationRequest {
  final String descricao;
  final double valor;
  final String syncId;
  final String idConta;
  final String identificadorRecorrencia;
  final double? novoValorMaximoPagador;
  final bool notificacaoAgendamento;
  final bool? usoLimiteCredito;

  PixAutomaticEditAuthorizationRequest({
    required this.descricao,
    required this.valor,
    required this.syncId,
    required this.idConta,
    required this.identificadorRecorrencia,
    this.novoValorMaximoPagador,
    required this.notificacaoAgendamento,
    this.usoLimiteCredito,
  });

  Map<String, dynamic> toJson() {
    return {
      'descricao': descricao,
      'valor': valor,
      'syncId': syncId,
      'idConta': idConta,
      'identificadorRecorrencia': identificadorRecorrencia,
      'novoValorMaximoPagador': novoValorMaximoPagador,
      'notificacaoAgendamento': notificacaoAgendamento,
      'usoLimiteCredito': usoLimiteCredito,
    };
  }
}

/// Response da edição de autorização PIX automático
class PixAutomaticEditAuthorizationResponse {
  final int nsu;
  final String codigoOrigem;
  final int nsuDestino;
  final String dataHoraCriacao;
  final String dataReferencia;
  final double valor;
  final Map<String, dynamic> informacoesAdicionais;
  final String comprovante;
  final String dataHoraRequisicaoJdpi;
  final String identificadorRecorrencia;
  final bool notificacaoAgendamentoAtulizacao;

  PixAutomaticEditAuthorizationResponse({
    required this.nsu,
    required this.codigoOrigem,
    required this.nsuDestino,
    required this.dataHoraCriacao,
    required this.dataReferencia,
    required this.valor,
    required this.informacoesAdicionais,
    required this.comprovante,
    required this.dataHoraRequisicaoJdpi,
    required this.identificadorRecorrencia,
    required this.notificacaoAgendamentoAtulizacao,
  });

  factory PixAutomaticEditAuthorizationResponse.fromJson(
      Map<String, dynamic> json) {
    return PixAutomaticEditAuthorizationResponse(
      nsu: json['nsu'] ?? 0,
      codigoOrigem: json['codigoOrigem'] ?? '',
      nsuDestino: json['nsuDestino'] ?? 0,
      dataHoraCriacao: json['dataHoraCriacao'] ?? '',
      dataReferencia: json['dataReferencia'] ?? '',
      valor: (json['valor'] ?? 0).toDouble(),
      informacoesAdicionais: json['informacoesAdicionais'] ?? {},
      comprovante: json['comprovante'] ?? '',
      dataHoraRequisicaoJdpi: json['dataHoraRequisicaoJdpi'] ?? '',
      identificadorRecorrencia: json['identificadorRecorrencia'] ?? '',
      notificacaoAgendamentoAtulizacao:
          json['notificacaoAgendamentoAtulizacao'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'nsu': nsu,
      'codigoOrigem': codigoOrigem,
      'nsuDestino': nsuDestino,
      'dataHoraCriacao': dataHoraCriacao,
      'dataReferencia': dataReferencia,
      'valor': valor,
      'informacoesAdicionais': informacoesAdicionais,
      'comprovante': comprovante,
      'dataHoraRequisicaoJdpi': dataHoraRequisicaoJdpi,
      'identificadorRecorrencia': identificadorRecorrencia,
      'notificacaoAgendamentoAtulizacao': notificacaoAgendamentoAtulizacao,
    };
  }
}
