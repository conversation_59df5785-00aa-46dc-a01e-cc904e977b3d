class AutoPixScheduleCancel {
  String? descricao;
  double? valor;
  String? idConta;
  int? solicitanteCancelamento;
  int? ispbSolicitante;
  int? ispbDestinatario;
  String? identificadorConciliacaoRecebedor;
  String? tipoPessoaSolicitante;
  int? cpfCnpjSolCancelamento;
  String? motivoCancelamento;
  String? endToEndIdentificador;
  int? nsu;
  String? codigoOrigem;
  int? nsuDestino;
  DateTime? dataHoraCriacao;
  DateTime? dataReferencia;
  Map<String, dynamic>? informacoesAdicionais;
  String? comprovante;
  DateTime? dataHoraRequisicaoJdPi;
  String? identificadorCancelamento;
  String? dataHoraCancelamento;
  String? situacaoCancelamento;

  AutoPixScheduleCancel({
    this.descricao,
    this.valor,
    this.idConta,
    this.solicitanteCancelamento,
    this.ispbSolicitante,
    this.ispbDestinatario,
    this.identificadorConciliacaoRecebedor,
    this.tipoPessoaSolicitante,
    this.cpfCnpjSolCancelamento,
    this.motivoCancelamento,
    this.endToEndIdentificador,
    this.nsu,
    this.codigoOrigem,
    this.nsuDestino,
    this.dataHoraCriacao,
    this.dataReferencia,
    this.informacoesAdicionais,
    this.comprovante,
    this.dataHoraRequisicaoJdPi,
    this.identificadorCancelamento,
    this.dataHoraCancelamento,
    this.situacaoCancelamento,
  });

  factory AutoPixScheduleCancel.fromJson(Map<String, dynamic> json) {
    return AutoPixScheduleCancel(
      descricao: json['descricao'] ?? "",
      valor: (json['valor'] as num?)?.toDouble() ?? 0.0,
      idConta: json['idConta'] ?? "",
      solicitanteCancelamento: json['solicitanteCancelamento'] ?? 0,
      ispbSolicitante: json['ispbSolicitante'] ?? 0,
      ispbDestinatario: json['ispbDestinatario'] ?? 0,
      identificadorConciliacaoRecebedor:
          json['identificadorConciliacaoRecebedor'] ?? "",
      tipoPessoaSolicitante: json['tipoPessoaSolicitante'] ?? "",
      cpfCnpjSolCancelamento: json['cpfCnpjSolCancelamento'] ?? 0,
      motivoCancelamento: json['motivoCancelamento'] ?? "",
      endToEndIdentificador: json['endToEndIdentificador'] ?? "",
      nsu: json['nsu'] ?? 0,
      codigoOrigem: json['codigoOrigem'] ?? "",
      nsuDestino: json['nsuDestino'] ?? 0,
      dataHoraCriacao: json['dataHoraCriacao'] != null
          ? DateTime.parse(json['dataHoraCriacao'])
          : null,
      dataReferencia: json['dataReferencia'] != null
          ? DateTime.parse(json['dataReferencia'])
          : null,
      informacoesAdicionais:
          json['informacoesAdicionais'] as Map<String, dynamic>?,
      comprovante: json['comprovante'] ?? "",
      dataHoraRequisicaoJdPi: json['dataHoraRequisicaoJdPi'] != null
          ? DateTime.parse(json['dataHoraRequisicaoJdPi'])
          : null,
      identificadorCancelamento: json['identificadorCancelamento'] ?? "",
      dataHoraCancelamento: json['dataHoraCancelamento'],
      situacaoCancelamento: json['situacaoCancelamento'] ?? "",
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'descricao': descricao,
      'valor': valor,
      'idConta': idConta,
      'solicitanteCancelamento': solicitanteCancelamento,
      'ispbSolicitante': ispbSolicitante,
      'ispbDestinatario': ispbDestinatario,
      'identificadorConciliacaoRecebedor': identificadorConciliacaoRecebedor,
      'tipoPessoaSolicitante': tipoPessoaSolicitante,
      'cpfCnpjSolCancelamento': cpfCnpjSolCancelamento,
      'motivoCancelamento': motivoCancelamento,
      'endToEndIdentificador': endToEndIdentificador,
      'nsu': nsu,
      'codigoOrigem': codigoOrigem,
      'nsuDestino': nsuDestino,
      'dataHoraCriacao': dataHoraCriacao?.toIso8601String(),
      'dataReferencia': dataReferencia?.toIso8601String(),
      'informacoesAdicionais': informacoesAdicionais,
      'comprovante': comprovante,
      'dataHoraRequisicaoJdPi': dataHoraRequisicaoJdPi?.toIso8601String(),
      'identificadorCancelamento': identificadorCancelamento,
      'dataHoraCancelamento': dataHoraCancelamento,
      'situacaoCancelamento': situacaoCancelamento,
    };
  }
}
