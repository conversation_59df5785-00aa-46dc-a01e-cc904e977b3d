{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "j17-bank-my-bank-app",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "j17-bank-my-bank-app (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "j17-bank-my-bank-app (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "flutter-heartbeat-plugin",
            "cwd": "dependencies/flutter-heartbeat-plugin",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "flutter-heartbeat-plugin (profile mode)",
            "cwd": "dependencies/flutter-heartbeat-plugin",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "flutter-heartbeat-plugin (release mode)",
            "cwd": "dependencies/flutter-heartbeat-plugin",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}